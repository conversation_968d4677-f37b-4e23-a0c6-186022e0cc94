#!/bin/bash

# ideaFlow GitHub 推送脚本
# 使用方法: ./push-to-github.sh YOUR_GITHUB_USERNAME

if [ -z "$1" ]; then
    echo "❌ 请提供 GitHub 用户名"
    echo "使用方法: ./push-to-github.sh YOUR_GITHUB_USERNAME"
    exit 1
fi

GITHUB_USERNAME=$1
REPO_URL="https://github.com/${GITHUB_USERNAME}/ideaflow.git"

echo "🚀 开始推送 ideaFlow 项目到 GitHub..."
echo "📍 目标仓库: ${REPO_URL}"

# 检查是否已经添加了远程仓库
if git remote get-url origin > /dev/null 2>&1; then
    echo "⚠️  远程仓库已存在，正在更新..."
    git remote set-url origin $REPO_URL
else
    echo "➕ 添加远程仓库..."
    git remote add origin $REPO_URL
fi

# 确保在 main 分支
echo "🔄 切换到 main 分支..."
git branch -M main

# 推送到远程仓库
echo "📤 推送代码到 GitHub..."
git push -u origin main

if [ $? -eq 0 ]; then
    echo "✅ 成功推送到 GitHub!"
    echo "🌐 仓库地址: ${REPO_URL}"
    echo "📋 项目包含:"
    echo "   - Next.js 15 + TypeScript"
    echo "   - shadcn/ui 组件库"
    echo "   - 可视化文档编辑器"
    echo "   - 流程设计器 (React Flow)"
    echo "   - Docker 部署配置"
    echo "   - 完整的 README 文档"
else
    echo "❌ 推送失败，请检查:"
    echo "   1. GitHub 仓库是否已创建"
    echo "   2. 用户名是否正确"
    echo "   3. 是否有推送权限"
fi
