# ideaFlow - 智能工作流程构建平台

ideaFlow 是一个创新的平台，旨在将自然语言描述的工作流程直接转化为可执行的业务应用。用户只需编写或导入说明文档，系统便能通过 AI 理解完成业务流程的构建。

## 🚀 核心特性

### 智能化流程构建
- **自然语言理解**: 直接从文档描述生成工作流程
- **AI 辅助设计**: 智能推荐最优的流程节点和连接
- **实时预览**: 即时查看应用运行效果

### 可视化设计界面
- **流程设计器**: 类似 FastGPT/Dify 的拖拽式流程编辑
- **双列布局**: 左侧对话+文档编辑，右侧 Canvas 展示
- **多视图模式**: 流程设计、应用预览、代码查看

### 完整的开发体验
- **文档编辑**: 内置 Markdown 编辑器，支持实时预览
- **代码生成**: 自动生成 Python、配置文件、数据库脚本
- **一键部署**: 支持应用的测试、发布和部署

## 🛠 技术栈

- **前端框架**: Next.js 15 + React 19
- **UI 组件**: shadcn/ui + Tailwind CSS
- **状态管理**: Zustand
- **流程图**: React Flow
- **代码编辑**: Monaco Editor
- **图标**: Lucide React

## 📦 安装和运行

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🎯 示例场景

当前演示以**订单运输调度系统**为例：

### 业务需求
根据用户上传的订单明细、物料信息、车辆信息，将销售订单中的物料合理分配给车辆进行配送，实现智能化的运输调度。

### 核心功能
1. **数据输入**: 支持 Excel 文件上传，包含订单、物料、车辆信息
2. **数据验证**: 自动检查数据格式和完整性
3. **智能调度**: AI 算法优化车辆分配和路线规划
4. **结果输出**: 生成详细的配送计划和调度报告

## 🏗 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 基础组件
│   ├── Header.tsx        # 顶部导航栏
│   ├── LeftPanel.tsx     # 左侧面板（对话+文件）
│   ├── Canvas.tsx        # 右侧 Canvas 区域
│   ├── FlowEditor.tsx    # 流程设计器
│   ├── AppPreview.tsx    # 应用预览
│   ├── CodeViewer.tsx    # 代码查看器
│   ├── ChatMessage.tsx   # 聊天消息组件
│   └── DocumentEditor.tsx # 文档编辑器
└── lib/
    ├── store.ts          # Zustand 状态管理
    └── utils.ts          # 工具函数
```

## 🎨 界面设计

### Header 区域
- **Logo**: ideaFlow（Flow 为蓝色）
- **应用信息**: 当前应用图标、名称、版本
- **操作菜单**: 配置、删除、复制等功能
- **状态显示**: 开发/测试/生产状态
- **功能按钮**: 导入、导出、测试运行、发布

### 左侧面板
- **对话标签**: AI 助手对话界面，支持文件上传和截图
- **文件标签**: 项目文件列表，支持文档编辑
- **文档编辑**: 内置编辑器，支持预览和编辑模式切换

### 右侧 Canvas
- **流程设计**: 可视化流程编辑器，支持节点拖拽和连接
- **应用预览**: 实时预览生成的应用界面和功能
- **代码查看**: 查看生成的 Python、配置文件、SQL 等代码

## 🔧 开发指南

### 添加新组件
```bash
npx shadcn@latest add [component-name]
```

### 状态管理
使用 Zustand 进行状态管理，主要状态包括：
- 当前应用信息
- 左侧面板状态
- Canvas 模式
- 聊天消息
- 项目文件

### 自定义样式
基于 Tailwind CSS，支持深色模式和响应式设计。

## 🚀 部署

### 构建生产版本
```bash
npm run build
```

### 启动生产服务器
```bash
npm start
```

### 部署到 Vercel
```bash
vercel --prod
```

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**ideaFlow** - 让想法流动，让创意实现 ✨
