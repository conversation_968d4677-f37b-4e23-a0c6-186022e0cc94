'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Copy, 
  Download, 
  FileCode, 
  Database, 
  Settings,
  Eye,
  EyeOff
} from "lucide-react"

export function CodeViewer() {
  const [showLineNumbers, setShowLineNumbers] = useState(true)

  const pythonCode = `# 订单运输调度系统
# 智能调度算法实现

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple

class TransportScheduler:
    """运输调度器"""
    
    def __init__(self):
        self.orders = []
        self.vehicles = []
        self.schedule_result = []
    
    def load_data(self, orders_file: str, vehicles_file: str):
        """加载订单和车辆数据"""
        try:
            self.orders = pd.read_excel(orders_file)
            self.vehicles = pd.read_excel(vehicles_file)
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def validate_data(self) -> Dict[str, bool]:
        """验证数据完整性"""
        validation_result = {
            'orders_valid': False,
            'vehicles_valid': False
        }
        
        # 验证订单数据
        required_order_columns = ['订单号', '客户', '物料', '重量', '交货日期']
        if all(col in self.orders.columns for col in required_order_columns):
            validation_result['orders_valid'] = True
        
        # 验证车辆数据
        required_vehicle_columns = ['车辆编号', '类型', '载重', '状态']
        if all(col in self.vehicles.columns for col in required_vehicle_columns):
            validation_result['vehicles_valid'] = True
        
        return validation_result
    
    def calculate_optimal_schedule(self) -> List[Dict]:
        """计算最优调度方案"""
        available_vehicles = self.vehicles[
            self.vehicles['状态'] == '可用'
        ].copy()
        
        schedule = []
        
        for _, vehicle in available_vehicles.iterrows():
            vehicle_capacity = float(vehicle['载重'].replace('吨', ''))
            assigned_orders = []
            current_load = 0
            
            for _, order in self.orders.iterrows():
                order_weight = float(order['重量'].replace('吨', ''))
                
                if current_load + order_weight <= vehicle_capacity:
                    assigned_orders.append(order['订单号'])
                    current_load += order_weight
            
            if assigned_orders:
                schedule.append({
                    'vehicle': vehicle['车辆编号'],
                    'orders': assigned_orders,
                    'total_weight': current_load,
                    'utilization': (current_load / vehicle_capacity) * 100
                })
        
        return schedule
    
    def export_schedule(self, filename: str):
        """导出调度结果"""
        if self.schedule_result:
            df = pd.DataFrame(self.schedule_result)
            df.to_excel(filename, index=False)
            return True
        return False

# 使用示例
if __name__ == "__main__":
    scheduler = TransportScheduler()
    
    # 加载数据
    if scheduler.load_data("orders.xlsx", "vehicles.xlsx"):
        print("数据加载成功")
        
        # 验证数据
        validation = scheduler.validate_data()
        if all(validation.values()):
            print("数据验证通过")
            
            # 计算调度方案
            schedule = scheduler.calculate_optimal_schedule()
            scheduler.schedule_result = schedule
            
            print(f"生成调度方案，共 {len(schedule)} 个分配")
            
            # 导出结果
            scheduler.export_schedule("schedule_result.xlsx")
            print("调度结果已导出")
        else:
            print("数据验证失败")
    else:
        print("数据加载失败")`

  const configCode = `# 配置文件
# config.yaml

# 数据库配置
database:
  host: localhost
  port: 5432
  name: transport_db
  user: admin
  password: password

# 调度算法参数
scheduler:
  # 车辆利用率目标 (%)
  target_utilization: 85
  
  # 最大配送距离 (km)
  max_delivery_distance: 200
  
  # 时间窗口容忍度 (小时)
  time_window_tolerance: 2
  
  # 优化目标权重
  weights:
    cost: 0.4
    time: 0.3
    utilization: 0.3

# API 配置
api:
  host: 0.0.0.0
  port: 8000
  debug: true
  
# 文件上传配置
upload:
  max_file_size: 10MB
  allowed_extensions:
    - xlsx
    - xls
    - csv
  
# 日志配置
logging:
  level: INFO
  file: logs/scheduler.log
  max_size: 100MB
  backup_count: 5`

  const sqlCode = `-- 数据库表结构
-- 订单表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(100) NOT NULL,
    weight DECIMAL(10,2) NOT NULL,
    delivery_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 车辆表
CREATE TABLE vehicles (
    id SERIAL PRIMARY KEY,
    vehicle_no VARCHAR(50) UNIQUE NOT NULL,
    vehicle_type VARCHAR(50) NOT NULL,
    capacity DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 调度结果表
CREATE TABLE schedules (
    id SERIAL PRIMARY KEY,
    vehicle_id INTEGER REFERENCES vehicles(id),
    order_ids INTEGER[] NOT NULL,
    route TEXT,
    scheduled_date DATE NOT NULL,
    total_weight DECIMAL(10,2),
    utilization_rate DECIMAL(5,2),
    status VARCHAR(20) DEFAULT 'planned',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_orders_delivery_date ON orders(delivery_date);
CREATE INDEX idx_vehicles_status ON vehicles(status);
CREATE INDEX idx_schedules_scheduled_date ON schedules(scheduled_date);

-- 插入示例数据
INSERT INTO orders (order_no, customer_name, material_type, weight, delivery_date) VALUES
('ORD001', '客户A', '钢材', 2.5, '2024-01-15'),
('ORD002', '客户B', '水泥', 3.2, '2024-01-16'),
('ORD003', '客户C', '砖块', 1.8, '2024-01-17');

INSERT INTO vehicles (vehicle_no, vehicle_type, capacity, status) VALUES
('VEH001', '大货车', 5.0, 'available'),
('VEH002', '中货车', 3.0, 'available'),
('VEH003', '小货车', 2.0, 'maintenance');`

  const handleCopy = (code: string) => {
    navigator.clipboard.writeText(code)
  }

  const CodeBlock = ({ code, language }: { code: string; language: string }) => (
    <div className="relative">
      <div className="absolute top-3 right-3 flex space-x-2">
        <Button
          size="sm"
          variant="outline"
          onClick={() => setShowLineNumbers(!showLineNumbers)}
        >
          {showLineNumbers ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleCopy(code)}
        >
          <Copy className="h-4 w-4" />
        </Button>
      </div>
      <ScrollArea className="h-[600px]">
        <pre className="p-4 text-sm font-mono bg-gray-900 text-gray-100 rounded-lg overflow-x-auto">
          {showLineNumbers && (
            <div className="float-left pr-4 text-gray-500 select-none">
              {code.split('\n').map((_, index) => (
                <div key={index} className="text-right">
                  {index + 1}
                </div>
              ))}
            </div>
          )}
          <code className={`language-${language}`}>{code}</code>
        </pre>
      </ScrollArea>
    </div>
  )

  return (
    <div className="h-full flex flex-col">
      {/* 工具栏 */}
      <div className="p-4 border-b flex items-center justify-between">
        <h3 className="font-medium">生成的应用代码</h3>
        <div className="flex space-x-2">
          <Button size="sm" variant="outline">
            <Download className="h-4 w-4 mr-2" />
            下载代码
          </Button>
          <Button size="sm">
            <FileCode className="h-4 w-4 mr-2" />
            部署应用
          </Button>
        </div>
      </div>

      {/* 代码标签页 */}
      <div className="flex-1 p-4">
        <Tabs defaultValue="python" className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="python" className="flex items-center space-x-2">
              <FileCode className="h-4 w-4" />
              <span>Python 算法</span>
            </TabsTrigger>
            <TabsTrigger value="config" className="flex items-center space-x-2">
              <Settings className="h-4 w-4" />
              <span>配置文件</span>
            </TabsTrigger>
            <TabsTrigger value="sql" className="flex items-center space-x-2">
              <Database className="h-4 w-4" />
              <span>数据库</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="python" className="flex-1 mt-4">
            <CodeBlock code={pythonCode} language="python" />
          </TabsContent>

          <TabsContent value="config" className="flex-1 mt-4">
            <CodeBlock code={configCode} language="yaml" />
          </TabsContent>

          <TabsContent value="sql" className="flex-1 mt-4">
            <CodeBlock code={sqlCode} language="sql" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
