'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import {
  MessageCircle,
  FileText,
  Send,
  Paperclip,
  Camera,
  Plus,
  File,
  Calendar
} from "lucide-react"
import { useAppStore } from "@/lib/store"
import { ChatMessage } from "./ChatMessage"
import { DocumentEditor } from "./DocumentEditor"

export function LeftPanel() {
  const { 
    leftPanel, 
    chat, 
    files, 
    setLeftPanelTab, 
    setDocumentEditing,
    addMessage,
    setLoading 
  } = useAppStore()
  
  const [inputMessage, setInputMessage] = useState('')
  const [selectedFile, setSelectedFile] = useState<string | null>(null)

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return
    
    // 添加用户消息
    addMessage({
      type: 'user',
      content: inputMessage
    })
    
    setInputMessage('')
    setLoading(true)
    
    // 模拟 AI 响应
    setTimeout(() => {
      addMessage({
        type: 'assistant',
        content: '我理解了你的需求。让我帮你分析一下这个工作流程...\n\n基于你的描述，我建议创建以下几个处理节点：\n1. 数据输入验证节点\n2. 智能匹配算法节点\n3. 优化调度节点\n4. 结果输出节点\n\n你希望我先从哪个部分开始构建？'
      })
      setLoading(false)
    }, 2000)
  }

  const handleFileSelect = (fileId: string) => {
    setSelectedFile(fileId)
    setDocumentEditing(true)
  }

  const handleBackToChat = () => {
    setSelectedFile(null)
    setDocumentEditing(false)
  }

  return (
    <div className="w-96 border-r bg-white flex flex-col h-full">
      {/* 如果正在编辑文档，显示文档编辑器 */}
      {leftPanel.isDocumentEditing && selectedFile ? (
        <DocumentEditor 
          fileId={selectedFile} 
          onBack={handleBackToChat}
        />
      ) : (
        <>
          {/* 标签页切换 */}
          <Tabs 
            value={leftPanel.activeTab} 
            onValueChange={(value) => setLeftPanelTab(value as 'chat' | 'files')}
            className="flex flex-col h-full"
          >
            <TabsList className="grid w-full grid-cols-2 m-4 mb-0">
              <TabsTrigger value="chat" className="flex items-center space-x-2">
                <MessageCircle className="h-4 w-4" />
                <span>对话</span>
              </TabsTrigger>
              <TabsTrigger value="files" className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>文件</span>
              </TabsTrigger>
            </TabsList>

            {/* 对话内容 */}
            <TabsContent value="chat" className="flex-1 flex flex-col m-4 mt-0">
              <ScrollArea className="flex-1 pr-4">
                <div className="space-y-4 py-4">
                  {chat.messages.map((message) => (
                    <ChatMessage key={message.id} message={message} />
                  ))}
                  {chat.isLoading && (
                    <div className="flex items-center space-x-2 text-gray-500">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-sm">AI 正在思考...</span>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            {/* 文件列表 */}
            <TabsContent value="files" className="flex-1 flex flex-col m-4 mt-0">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">项目文件</h3>
                <Button size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  添加
                </Button>
              </div>
              
              <ScrollArea className="flex-1">
                <div className="space-y-2">
                  {files.map((file) => (
                    <div
                      key={file.id}
                      className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => handleFileSelect(file.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <File className="h-5 w-5 text-blue-600 mt-0.5" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {file.name}
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
                            <Calendar className="h-3 w-3" />
                            <span>
                              {file.lastModified.toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>

          {/* 输入区域 - 只在对话标签页显示 */}
          {leftPanel.activeTab === 'chat' && (
            <div className="border-t p-4 space-y-3">
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Paperclip className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Camera className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex space-x-2">
                <Textarea
                  placeholder="描述你的工作流程需求..."
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  className="flex-1 min-h-[80px] resize-none"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault()
                      handleSendMessage()
                    }
                  }}
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || chat.isLoading}
                  className="self-end"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}
