'use client'

import { use<PERSON>allback, useState } from 'react'
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { Button } from "@/components/ui/button"
import { 
  Plus, 
  Database, 
  Cpu, 
  CheckCircle, 
  FileOutput,
  Upload,
  Settings
} from "lucide-react"

// 初始节点数据
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'input',
    position: { x: 100, y: 100 },
    data: { 
      label: (
        <div className="flex items-center space-x-2">
          <Upload className="h-4 w-4" />
          <span>数据输入</span>
        </div>
      )
    },
    style: {
      background: '#e3f2fd',
      border: '2px solid #2196f3',
      borderRadius: '8px',
      padding: '10px'
    }
  },
  {
    id: '2',
    position: { x: 300, y: 100 },
    data: { 
      label: (
        <div className="flex items-center space-x-2">
          <Database className="h-4 w-4" />
          <span>数据验证</span>
        </div>
      )
    },
    style: {
      background: '#fff3e0',
      border: '2px solid #ff9800',
      borderRadius: '8px',
      padding: '10px'
    }
  },
  {
    id: '3',
    position: { x: 500, y: 100 },
    data: { 
      label: (
        <div className="flex items-center space-x-2">
          <Cpu className="h-4 w-4" />
          <span>智能调度</span>
        </div>
      )
    },
    style: {
      background: '#f3e5f5',
      border: '2px solid #9c27b0',
      borderRadius: '8px',
      padding: '10px'
    }
  },
  {
    id: '4',
    position: { x: 700, y: 100 },
    data: { 
      label: (
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4" />
          <span>结果优化</span>
        </div>
      )
    },
    style: {
      background: '#e8f5e8',
      border: '2px solid #4caf50',
      borderRadius: '8px',
      padding: '10px'
    }
  },
  {
    id: '5',
    type: 'output',
    position: { x: 900, y: 100 },
    data: { 
      label: (
        <div className="flex items-center space-x-2">
          <FileOutput className="h-4 w-4" />
          <span>输出结果</span>
        </div>
      )
    },
    style: {
      background: '#ffebee',
      border: '2px solid #f44336',
      borderRadius: '8px',
      padding: '10px'
    }
  }
]

// 初始连接线
const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2', animated: true },
  { id: 'e2-3', source: '2', target: '3', animated: true },
  { id: 'e3-4', source: '3', target: '4', animated: true },
  { id: 'e4-5', source: '4', target: '5', animated: true },
]

export function FlowEditor() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
  }, [])

  const addNewNode = () => {
    const newNode: Node = {
      id: `${nodes.length + 1}`,
      position: { x: Math.random() * 400 + 100, y: Math.random() * 300 + 200 },
      data: { 
        label: (
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>新节点</span>
          </div>
        )
      },
      style: {
        background: '#f5f5f5',
        border: '2px solid #9e9e9e',
        borderRadius: '8px',
        padding: '10px'
      }
    }
    setNodes((nds) => nds.concat(newNode))
  }

  return (
    <div className="h-full relative">
      {/* 工具栏 */}
      <div className="absolute top-4 left-4 z-10 flex space-x-2">
        <Button size="sm" onClick={addNewNode}>
          <Plus className="h-4 w-4 mr-2" />
          添加节点
        </Button>
        <Button size="sm" variant="outline">
          <Database className="h-4 w-4 mr-2" />
          数据节点
        </Button>
        <Button size="sm" variant="outline">
          <Cpu className="h-4 w-4 mr-2" />
          处理节点
        </Button>
      </div>

      {/* 节点属性面板 */}
      {selectedNode && (
        <div className="absolute top-4 right-4 z-10 w-64 bg-white border rounded-lg shadow-lg p-4">
          <h3 className="font-medium mb-3">节点属性</h3>
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium">节点名称</label>
              <input 
                type="text" 
                className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                defaultValue={selectedNode.data.label}
              />
            </div>
            <div>
              <label className="text-sm font-medium">节点类型</label>
              <select className="w-full mt-1 px-3 py-2 border rounded-md text-sm">
                <option>数据输入</option>
                <option>数据处理</option>
                <option>逻辑判断</option>
                <option>结果输出</option>
              </select>
            </div>
            <div>
              <label className="text-sm font-medium">描述</label>
              <textarea 
                className="w-full mt-1 px-3 py-2 border rounded-md text-sm"
                rows={3}
                placeholder="节点功能描述..."
              />
            </div>
            <div className="flex space-x-2">
              <Button size="sm" className="flex-1">保存</Button>
              <Button size="sm" variant="outline" onClick={() => setSelectedNode(null)}>
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* React Flow */}
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        fitView
        className="bg-gray-50"
      >
        <Controls />
        <MiniMap 
          nodeColor={(node) => {
            switch (node.type) {
              case 'input': return '#2196f3'
              case 'output': return '#f44336'
              default: return '#9e9e9e'
            }
          }}
        />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>

      {/* 底部说明 */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 text-sm text-gray-600">
        <div className="font-medium mb-1">操作说明：</div>
        <div>• 点击节点查看/编辑属性</div>
        <div>• 拖拽节点连接点创建连接</div>
        <div>• 使用右下角小地图导航</div>
      </div>
    </div>
  )
}
