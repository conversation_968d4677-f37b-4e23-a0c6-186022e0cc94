'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { 
  Workflow, 
  Eye, 
  Code, 
  Play,
  Settings,
  Maximize2
} from "lucide-react"
import { useAppStore } from "@/lib/store"
import { FlowEditor } from "./FlowEditor"
import { AppPreview } from "./AppPreview"
import { CodeViewer } from "./CodeViewer"

export function Canvas() {
  const { canvas, setCanvasMode } = useAppStore()

  return (
    <div className="flex-1 flex flex-col bg-gray-50">
      {/* Canvas 工具栏 */}
      <div className="h-14 bg-white border-b flex items-center justify-between px-6">
        <div className="flex items-center space-x-4">
          <h2 className="font-medium">应用构建器</h2>
          <div className="h-4 w-px bg-gray-300" />
          <div className="text-sm text-gray-600">
            实时预览 • 自动保存
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            配置
          </Button>
          <Button variant="outline" size="sm">
            <Maximize2 className="h-4 w-4 mr-2" />
            全屏
          </Button>
          <Button size="sm" className="bg-green-600 hover:bg-green-700">
            <Play className="h-4 w-4 mr-2" />
            运行测试
          </Button>
        </div>
      </div>

      {/* Canvas 内容区域 */}
      <div className="flex-1">
        <Tabs 
          value={canvas.mode} 
          onValueChange={(value) => setCanvasMode(value as 'flow' | 'preview' | 'code')}
          className="h-full flex flex-col"
        >
          {/* 标签页切换 */}
          <TabsList className="grid w-full grid-cols-3 mx-6 mt-4 mb-0">
            <TabsTrigger value="flow" className="flex items-center space-x-2">
              <Workflow className="h-4 w-4" />
              <span>流程设计</span>
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center space-x-2">
              <Eye className="h-4 w-4" />
              <span>应用预览</span>
            </TabsTrigger>
            <TabsTrigger value="code" className="flex items-center space-x-2">
              <Code className="h-4 w-4" />
              <span>代码查看</span>
            </TabsTrigger>
          </TabsList>

          {/* 流程设计器 */}
          <TabsContent value="flow" className="flex-1 m-6 mt-4">
            <div className="h-full bg-white rounded-lg border shadow-sm">
              <FlowEditor />
            </div>
          </TabsContent>

          {/* 应用预览 */}
          <TabsContent value="preview" className="flex-1 m-6 mt-4">
            <div className="h-full bg-white rounded-lg border shadow-sm">
              <AppPreview />
            </div>
          </TabsContent>

          {/* 代码查看 */}
          <TabsContent value="code" className="flex-1 m-6 mt-4">
            <div className="h-full bg-white rounded-lg border shadow-sm">
              <CodeViewer />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
