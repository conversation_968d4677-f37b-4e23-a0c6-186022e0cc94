'use client'

import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Copy, ThumbsUp, ThumbsDown } from "lucide-react"
import { AppState } from "@/lib/store"

interface ChatMessageProps {
  message: AppState['chat']['messages'][0]
}

export function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.type === 'user'
  
  const handleCopy = () => {
    navigator.clipboard.writeText(message.content)
  }

  return (
    <div className={`flex space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
      {/* 头像 */}
      <Avatar className="h-8 w-8 flex-shrink-0">
        <AvatarFallback className={isUser ? 'bg-blue-600 text-white' : 'bg-gray-200'}>
          {isUser ? '你' : 'AI'}
        </AvatarFallback>
      </Avatar>

      {/* 消息内容 */}
      <div className={`flex-1 space-y-2 ${isUser ? 'items-end' : 'items-start'}`}>
        {/* 消息气泡 */}
        <div
          className={`max-w-[280px] p-3 rounded-lg ${
            isUser
              ? 'bg-blue-600 text-white ml-auto'
              : 'bg-gray-100 text-gray-900'
          }`}
        >
          <div className="text-sm whitespace-pre-wrap">
            {message.content}
          </div>
          
          {/* 附件 */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-2 space-y-1">
              {message.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className={`text-xs p-2 rounded ${
                    isUser ? 'bg-blue-500' : 'bg-gray-200'
                  }`}
                >
                  📎 {attachment.name}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 时间戳和操作按钮 */}
        <div className={`flex items-center space-x-2 text-xs text-gray-500 ${
          isUser ? 'flex-row-reverse space-x-reverse' : ''
        }`}>
          <span>
            {message.timestamp.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </span>
          
          {!isUser && (
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-gray-200"
                onClick={handleCopy}
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-gray-200"
              >
                <ThumbsUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 hover:bg-gray-200"
              >
                <ThumbsDown className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
