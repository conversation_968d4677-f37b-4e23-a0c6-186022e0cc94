'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { 
  Settings, 
  Copy, 
  Trash2, 
  MoreHorizontal,
  Play,
  Pause,
  Upload,
  Download
} from "lucide-react"
import { useAppStore } from "@/lib/store"

export function Header() {
  const { currentApp } = useAppStore()

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'development':
        return 'bg-yellow-500'
      case 'testing':
        return 'bg-blue-500'
      case 'production':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'development':
        return '开发中'
      case 'testing':
        return '测试中'
      case 'production':
        return '已发布'
      default:
        return '未知'
    }
  }

  return (
    <header className="h-16 border-b bg-white flex items-center justify-between px-6">
      {/* 左侧 Logo 和应用信息 */}
      <div className="flex items-center space-x-4">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="text-2xl font-bold">
            idea<span className="text-blue-600">Flow</span>
          </div>
        </div>

        {/* 分隔线 */}
        <div className="h-6 w-px bg-gray-300" />

        {/* 当前应用信息 */}
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-lg">
              {currentApp.icon}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-sm">{currentApp.name}</span>
              <Badge variant="secondary" className="text-xs">
                {currentApp.version}
              </Badge>
            </div>
          </div>

          {/* 应用操作菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                应用配置
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                复制应用
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <Trash2 className="mr-2 h-4 w-4" />
                删除应用
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 右侧状态和操作区域 */}
      <div className="flex items-center space-x-4">
        {/* 应用状态 */}
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor(currentApp.status)}`} />
          <span className="text-sm text-gray-600">
            {getStatusText(currentApp.status)}
          </span>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>

          {currentApp.status === 'development' ? (
            <Button size="sm">
              <Play className="mr-2 h-4 w-4" />
              测试运行
            </Button>
          ) : (
            <Button size="sm" variant="outline">
              <Pause className="mr-2 h-4 w-4" />
              停止
            </Button>
          )}

          <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
            发布应用
          </Button>
        </div>
      </div>
    </header>
  )
}
