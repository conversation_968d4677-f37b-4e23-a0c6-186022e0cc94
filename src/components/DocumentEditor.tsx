'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Save } from "lucide-react"
import { useAppStore } from "@/lib/store"
import { RichTextEditor } from "./RichTextEditor"
import { htmlToMarkdown } from "@/lib/markdown-utils"

// 简单的 Markdown 到 HTML 转换函数（备用）
function simpleMarkdownToHtml(markdown: string): string {
  return markdown
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

interface DocumentEditorProps {
  fileId: string
  onBack: () => void
}

export function DocumentEditor({ fileId, onBack }: DocumentEditorProps) {
  const { files, updateFile } = useAppStore()
  const [htmlContent, setHtmlContent] = useState('')
  const [hasChanges, setHasChanges] = useState(false)

  const file = files.find(f => f.id === fileId)

  useEffect(() => {
    if (file) {
      const fileContent = file.content
      console.log('File content:', fileContent)

      // 暂时使用简单的 Markdown 转换，确保内容能显示
      if (fileContent.includes('#') || fileContent.includes('*') || fileContent.includes('-')) {
        // 简单的 Markdown 转换
        const simpleHtml = simpleMarkdownToHtml(fileContent)
        console.log('Simple HTML:', simpleHtml)
        setHtmlContent(simpleHtml)
      } else {
        // 如果不是 Markdown，直接使用内容
        setHtmlContent(fileContent)
      }

      // 重置更改状态
      setHasChanges(false)
    }
  }, [file])

  const handleSave = () => {
    if (file && hasChanges) {
      try {
        // 将 HTML 内容转换为 Markdown 保存
        const contentToSave = htmlToMarkdown(htmlContent)
        updateFile(file.id, contentToSave)
        setHasChanges(false)
        console.log('Content saved successfully')
      } catch (error) {
        console.error('Save failed:', error)
        // 如果转换失败，直接保存 HTML 内容
        updateFile(file.id, htmlContent)
        setHasChanges(false)
      }
    }
  }

  const handleContentChange = (newContent: string) => {
    setHtmlContent(newContent)
    setHasChanges(true)
  }

  if (!file) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-500">文件未找到</div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h3 className="font-medium text-sm">{file.name}</h3>
            <p className="text-xs text-gray-500">
              最后修改: {file.lastModified.toLocaleString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {hasChanges && (
            <Button
              size="sm"
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              保存
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        <RichTextEditor
          content={htmlContent}
          onChange={handleContentChange}
          placeholder="开始编写文档..."
          editable={true}
        />
      </div>
    </div>
  )
}
