'use client'

import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { ArrowLeft, Save } from "lucide-react"
import { useAppStore } from "@/lib/store"
import { RichTextEditor } from "./RichTextEditor"
import { markdownToHtml, htmlToMarkdown, isMarkdown } from "@/lib/markdown-utils"

interface DocumentEditorProps {
  fileId: string
  onBack: () => void
}

export function DocumentEditor({ fileId, onBack }: DocumentEditorProps) {
  const { files, updateFile } = useAppStore()
  const [htmlContent, setHtmlContent] = useState('')
  const [hasChanges, setHasChanges] = useState(false)

  const file = files.find(f => f.id === fileId)

  useEffect(() => {
    if (file) {
      const fileContent = file.content

      // 检测内容格式并转换为 HTML
      if (isMarkdown(fileContent)) {
        setHtmlContent(markdownToHtml(fileContent))
      } else {
        setHtmlContent(fileContent)
      }
    }
  }, [file])

  const handleSave = () => {
    if (file && hasChanges) {
      // 将 HTML 内容转换为 Markdown 保存
      const contentToSave = htmlToMarkdown(htmlContent)
      updateFile(file.id, contentToSave)
      setHasChanges(false)
    }
  }

  const handleContentChange = (newContent: string) => {
    setHtmlContent(newContent)
    setHasChanges(true)
  }

  if (!file) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-500">文件未找到</div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h3 className="font-medium text-sm">{file.name}</h3>
            <p className="text-xs text-gray-500">
              最后修改: {file.lastModified.toLocaleString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {hasChanges && (
            <Button
              size="sm"
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              保存
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        <RichTextEditor
          content={htmlContent}
          onChange={handleContentChange}
          placeholder="开始编写文档..."
          editable={true}
        />
      </div>
    </div>
  )
}
