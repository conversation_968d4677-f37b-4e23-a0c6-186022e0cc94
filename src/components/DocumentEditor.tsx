'use client'

import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Save, Eye, Edit3 } from "lucide-react"
import { useAppStore } from "@/lib/store"

interface DocumentEditorProps {
  fileId: string
  onBack: () => void
}

export function DocumentEditor({ fileId, onBack }: DocumentEditorProps) {
  const { files, updateFile } = useAppStore()
  const [isEditing, setIsEditing] = useState(false)
  const [content, setContent] = useState('')
  const [hasChanges, setHasChanges] = useState(false)

  const file = files.find(f => f.id === fileId)

  useEffect(() => {
    if (file) {
      setContent(file.content)
    }
  }, [file])

  const handleSave = () => {
    if (file && hasChanges) {
      updateFile(file.id, content)
      setHasChanges(false)
    }
  }

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    setHasChanges(newContent !== file?.content)
  }

  if (!file) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-500">文件未找到</div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h3 className="font-medium text-sm">{file.name}</h3>
            <p className="text-xs text-gray-500">
              最后修改: {file.lastModified.toLocaleString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? (
              <>
                <Eye className="h-4 w-4 mr-2" />
                预览
              </>
            ) : (
              <>
                <Edit3 className="h-4 w-4 mr-2" />
                编辑
              </>
            )}
          </Button>
          
          {hasChanges && (
            <Button
              size="sm"
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              保存
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 p-4">
        {isEditing ? (
          <Textarea
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            className="w-full h-full resize-none font-mono text-sm"
            placeholder="开始编辑文档..."
          />
        ) : (
          <div className="h-full overflow-auto">
            <div className="prose prose-sm max-w-none">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                {content}
              </pre>
            </div>
          </div>
        )}
      </div>

      {/* 底部状态栏 */}
      <div className="flex items-center justify-between p-4 border-t bg-gray-50 text-xs text-gray-500">
        <div>
          {file.type === 'document' ? '📄' : file.type === 'data' ? '📊' : '⚙️'} {file.type}
        </div>
        <div>
          {content.length} 字符 | {content.split('\n').length} 行
        </div>
      </div>
    </div>
  )
}
