'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Save, Eye, Edit3, Code2 } from "lucide-react"
import { useAppStore } from "@/lib/store"
import { RichTextEditor } from "./RichTextEditor"
import { markdownToHtml, htmlToMarkdown, isMarkdown, getContentStats } from "@/lib/markdown-utils"

interface DocumentEditorProps {
  fileId: string
  onBack: () => void
}

export function DocumentEditor({ fileId, onBack }: DocumentEditorProps) {
  const { files, updateFile } = useAppStore()
  const [editMode, setEditMode] = useState<'visual' | 'markdown' | 'preview'>('visual')
  const [content, setContent] = useState('')
  const [htmlContent, setHtmlContent] = useState('')
  const [hasChanges, setHasChanges] = useState(false)

  const file = files.find(f => f.id === fileId)

  useEffect(() => {
    if (file) {
      const fileContent = file.content
      setContent(fileContent)

      // 检测内容格式并设置相应的编辑模式
      if (isMarkdown(fileContent)) {
        setHtmlContent(markdownToHtml(fileContent))
        setEditMode('visual')
      } else {
        setHtmlContent(fileContent)
        setEditMode('visual')
      }
    }
  }, [file])

  const handleSave = () => {
    if (file && hasChanges) {
      // 根据当前编辑模式保存内容
      const contentToSave = editMode === 'visual' ? htmlToMarkdown(htmlContent) : content
      updateFile(file.id, contentToSave)
      setHasChanges(false)
    }
  }

  const handleContentChange = (newContent: string) => {
    if (editMode === 'visual') {
      setHtmlContent(newContent)
      setContent(htmlToMarkdown(newContent))
    } else {
      setContent(newContent)
      setHtmlContent(markdownToHtml(newContent))
    }
    setHasChanges(true)
  }

  const handleModeChange = (mode: 'visual' | 'markdown' | 'preview') => {
    setEditMode(mode)
  }

  if (!file) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-500">文件未找到</div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h3 className="font-medium text-sm">{file.name}</h3>
            <p className="text-xs text-gray-500">
              最后修改: {file.lastModified.toLocaleString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center border rounded-md">
            <Button
              variant={editMode === 'visual' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleModeChange('visual')}
              className="rounded-r-none border-r"
            >
              <Edit3 className="h-4 w-4 mr-2" />
              可视化
            </Button>
            <Button
              variant={editMode === 'markdown' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleModeChange('markdown')}
              className="rounded-none border-r"
            >
              <Code2 className="h-4 w-4 mr-2" />
              Markdown
            </Button>
            <Button
              variant={editMode === 'preview' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleModeChange('preview')}
              className="rounded-l-none"
            >
              <Eye className="h-4 w-4 mr-2" />
              预览
            </Button>
          </div>
          
          {hasChanges && (
            <Button
              size="sm"
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              保存
            </Button>
          )}
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden">
        {editMode === 'visual' && (
          <div className="h-full p-4">
            <RichTextEditor
              content={htmlContent}
              onChange={handleContentChange}
              placeholder="开始编写文档..."
              editable={true}
            />
          </div>
        )}

        {editMode === 'markdown' && (
          <div className="h-full p-4">
            <Textarea
              value={content}
              onChange={(e) => handleContentChange(e.target.value)}
              className="w-full h-full resize-none font-mono text-sm"
              placeholder="# 开始编写 Markdown 文档..."
            />
          </div>
        )}

        {editMode === 'preview' && (
          <div className="h-full p-4 overflow-auto">
            <div
              className="prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: htmlContent }}
            />
          </div>
        )}
      </div>

      {/* 底部状态栏 */}
      <div className="flex items-center justify-between p-4 border-t bg-gray-50 text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>
            {file.type === 'document' ? '📄' : file.type === 'data' ? '📊' : '⚙️'} {file.type}
          </span>
          <span>
            编辑模式: {editMode === 'visual' ? '可视化' : editMode === 'markdown' ? 'Markdown' : '预览'}
          </span>
          {hasChanges && (
            <span className="text-orange-600">● 未保存</span>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {(() => {
            const stats = getContentStats(editMode === 'visual' ? htmlContent : content)
            return (
              <>
                <span>{stats.words} 词</span>
                <span>{stats.characters} 字符</span>
                <span>{stats.lines} 行</span>
                <span>约 {stats.readingTime} 分钟阅读</span>
              </>
            )
          })()}
        </div>
      </div>
    </div>
  )
}
