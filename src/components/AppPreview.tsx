'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Upload, 
  FileSpreadsheet, 
  Truck, 
  Package,
  Calendar,
  MapPin,
  CheckCircle,
  AlertCircle,
  Download
} from "lucide-react"

export function AppPreview() {
  const [currentStep, setCurrentStep] = useState(1)

  const steps = [
    { id: 1, title: '数据上传', icon: Upload },
    { id: 2, title: '数据验证', icon: CheckCircle },
    { id: 3, title: '智能调度', icon: Truck },
    { id: 4, title: '结果确认', icon: Download }
  ]

  const mockOrderData = [
    { id: 'ORD001', customer: '客户A', material: '钢材', weight: '2.5吨', date: '2024-01-15' },
    { id: 'ORD002', customer: '客户B', material: '水泥', weight: '3.2吨', date: '2024-01-16' },
    { id: 'ORD003', customer: '客户C', material: '砖块', weight: '1.8吨', date: '2024-01-17' }
  ]

  const mockVehicleData = [
    { id: 'VEH001', type: '大货车', capacity: '5吨', status: '可用' },
    { id: 'VEH002', type: '中货车', capacity: '3吨', status: '可用' },
    { id: 'VEH003', type: '小货车', capacity: '2吨', status: '维修中' }
  ]

  const mockScheduleResult = [
    { vehicle: 'VEH001', orders: ['ORD001', 'ORD003'], route: '客户A → 客户C', date: '2024-01-15' },
    { vehicle: 'VEH002', orders: ['ORD002'], route: '客户B', date: '2024-01-16' }
  ]

  return (
    <div className="h-full flex flex-col">
      {/* 步骤指示器 */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center space-x-2 ${
                currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  currentStep >= step.id ? 'bg-blue-600 text-white' : 'bg-gray-200'
                }`}>
                  <step.icon className="h-4 w-4" />
                </div>
                <span className="font-medium text-sm">{step.title}</span>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-px mx-4 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 p-6">
        {currentStep === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">数据上传</h2>
            <p className="text-gray-600">请上传订单明细、物料信息和车辆信息文件</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { name: '订单明细.xlsx', icon: FileSpreadsheet, uploaded: true },
                { name: '物料信息.xlsx', icon: Package, uploaded: true },
                { name: '车辆信息.xlsx', icon: Truck, uploaded: false }
              ].map((file, index) => (
                <div key={index} className={`border-2 border-dashed rounded-lg p-6 text-center ${
                  file.uploaded ? 'border-green-300 bg-green-50' : 'border-gray-300'
                }`}>
                  <file.icon className={`h-12 w-12 mx-auto mb-3 ${
                    file.uploaded ? 'text-green-600' : 'text-gray-400'
                  }`} />
                  <div className="font-medium">{file.name}</div>
                  {file.uploaded ? (
                    <div className="text-green-600 text-sm mt-2">✓ 已上传</div>
                  ) : (
                    <Button className="mt-3" size="sm">
                      <Upload className="h-4 w-4 mr-2" />
                      上传文件
                    </Button>
                  )}
                </div>
              ))}
            </div>

            <div className="flex justify-end">
              <Button onClick={() => setCurrentStep(2)}>
                下一步：数据验证
              </Button>
            </div>
          </div>
        )}

        {currentStep === 2 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">数据验证</h2>
            <p className="text-gray-600">系统正在验证上传的数据格式和完整性</p>

            <Tabs defaultValue="orders" className="w-full">
              <TabsList>
                <TabsTrigger value="orders">订单数据</TabsTrigger>
                <TabsTrigger value="vehicles">车辆数据</TabsTrigger>
              </TabsList>
              
              <TabsContent value="orders" className="space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 text-green-800">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">订单数据验证通过</span>
                  </div>
                  <div className="text-sm text-green-700 mt-1">
                    共 {mockOrderData.length} 条订单记录，格式正确
                  </div>
                </div>
                
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-medium">订单号</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">客户</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">物料</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">重量</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">交货日期</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockOrderData.map((order) => (
                        <tr key={order.id} className="border-t">
                          <td className="px-4 py-2 text-sm">{order.id}</td>
                          <td className="px-4 py-2 text-sm">{order.customer}</td>
                          <td className="px-4 py-2 text-sm">{order.material}</td>
                          <td className="px-4 py-2 text-sm">{order.weight}</td>
                          <td className="px-4 py-2 text-sm">{order.date}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </TabsContent>
              
              <TabsContent value="vehicles" className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2 text-yellow-800">
                    <AlertCircle className="h-5 w-5" />
                    <span className="font-medium">车辆数据需要注意</span>
                  </div>
                  <div className="text-sm text-yellow-700 mt-1">
                    1 辆车辆状态为维修中，不参与调度
                  </div>
                </div>
                
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-medium">车辆编号</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">类型</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">载重</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">状态</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockVehicleData.map((vehicle) => (
                        <tr key={vehicle.id} className="border-t">
                          <td className="px-4 py-2 text-sm">{vehicle.id}</td>
                          <td className="px-4 py-2 text-sm">{vehicle.type}</td>
                          <td className="px-4 py-2 text-sm">{vehicle.capacity}</td>
                          <td className="px-4 py-2 text-sm">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              vehicle.status === '可用' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {vehicle.status}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentStep(1)}>
                上一步
              </Button>
              <Button onClick={() => setCurrentStep(3)}>
                下一步：开始调度
              </Button>
            </div>
          </div>
        )}

        {currentStep === 3 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">智能调度</h2>
            <p className="text-gray-600">AI 正在为您生成最优的运输调度方案</p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="font-medium">正在计算最优调度方案...</span>
              </div>
              <div className="text-sm text-blue-700 space-y-1">
                <div>✓ 分析订单需求和时间约束</div>
                <div>✓ 匹配车辆容量和物料属性</div>
                <div>✓ 优化运输路线和成本</div>
                <div className="animate-pulse">⏳ 生成调度结果...</div>
              </div>
            </div>

            <div className="flex justify-center">
              <Button onClick={() => setCurrentStep(4)} className="px-8">
                查看调度结果
              </Button>
            </div>
          </div>
        )}

        {currentStep === 4 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">调度结果</h2>
            <p className="text-gray-600">以下是系统生成的最优运输调度方案</p>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">调度方案生成成功</span>
              </div>
              <div className="text-sm text-green-700 mt-1">
                预计节省运输成本 15%，提高车辆利用率 20%
              </div>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-sm font-medium">车辆</th>
                    <th className="px-4 py-2 text-left text-sm font-medium">分配订单</th>
                    <th className="px-4 py-2 text-left text-sm font-medium">运输路线</th>
                    <th className="px-4 py-2 text-left text-sm font-medium">配送日期</th>
                  </tr>
                </thead>
                <tbody>
                  {mockScheduleResult.map((result, index) => (
                    <tr key={index} className="border-t">
                      <td className="px-4 py-2 text-sm font-medium">{result.vehicle}</td>
                      <td className="px-4 py-2 text-sm">{result.orders.join(', ')}</td>
                      <td className="px-4 py-2 text-sm">
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{result.route}</span>
                        </div>
                      </td>
                      <td className="px-4 py-2 text-sm">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{result.date}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentStep(3)}>
                重新调度
              </Button>
              <div className="space-x-2">
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  导出方案
                </Button>
                <Button>
                  确认并执行
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
