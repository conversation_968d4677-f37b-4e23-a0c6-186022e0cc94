import { create } from 'zustand'

// 应用状态类型定义
export interface AppState {
  // 当前应用信息
  currentApp: {
    id: string
    name: string
    icon: string
    status: 'development' | 'testing' | 'production'
    version: string
  }
  
  // 左侧面板状态
  leftPanel: {
    activeTab: 'chat' | 'files'
    isDocumentEditing: boolean
  }
  
  // Canvas 状态
  canvas: {
    mode: 'flow' | 'preview' | 'code'
    flowData: unknown
  }
  
  // 聊天状态
  chat: {
    messages: Array<{
      id: string
      type: 'user' | 'assistant'
      content: string
      timestamp: Date
      attachments?: Array<{
        type: 'image' | 'file'
        url: string
        name: string
      }>
    }>
    isLoading: boolean
  }
  
  // 项目文件
  files: Array<{
    id: string
    name: string
    type: 'document' | 'data' | 'config'
    content: string
    lastModified: Date
  }>
}

// 应用操作类型定义
export interface AppActions {
  // 应用操作
  updateAppInfo: (info: Partial<AppState['currentApp']>) => void
  
  // 左侧面板操作
  setLeftPanelTab: (tab: AppState['leftPanel']['activeTab']) => void
  setDocumentEditing: (editing: boolean) => void
  
  // Canvas 操作
  setCanvasMode: (mode: AppState['canvas']['mode']) => void
  updateFlowData: (data: unknown) => void
  
  // 聊天操作
  addMessage: (message: Omit<AppState['chat']['messages'][0], 'id' | 'timestamp'>) => void
  setLoading: (loading: boolean) => void
  
  // 文件操作
  addFile: (file: Omit<AppState['files'][0], 'id' | 'lastModified'>) => void
  updateFile: (id: string, content: string) => void
  deleteFile: (id: string) => void
}

// 创建 Zustand store
export const useAppStore = create<AppState & AppActions>((set) => ({
  // 初始状态
  currentApp: {
    id: 'demo-app',
    name: '订单运输调度系统',
    icon: '🚛',
    status: 'development',
    version: 'v0.1.0'
  },
  
  leftPanel: {
    activeTab: 'chat',
    isDocumentEditing: false
  },
  
  canvas: {
    mode: 'flow',
    flowData: null
  },
  
  chat: {
    messages: [
      {
        id: '1',
        type: 'assistant',
        content: '你好！我是 ideaFlow AI 助手。我可以帮助你将自然语言描述的工作流程转化为可执行的业务应用。\n\n当前项目：订单运输调度系统\n\n你可以：\n- 描述你的业务流程需求\n- 上传相关文档或数据文件\n- 让我帮你构建和优化工作流程',
        timestamp: new Date()
      }
    ],
    isLoading: false
  },
  
  files: [
    {
      id: 'main-doc',
      name: '项目需求文档.md',
      type: 'document',
      content: `# 订单运输调度系统需求文档

## 项目概述
本系统旨在根据用户上传的订单明细、物料信息、车辆信息，将销售订单中的物料合理分配给车辆进行配送，实现智能化的运输调度。

## 核心功能

### 1. 数据输入
- 订单明细：包含客户信息、物料需求、交货日期等
- 物料信息：物料规格、重量、体积等属性
- 车辆信息：车辆载重、容积、可用时间等

### 2. 智能调度
- 根据物料属性和车辆容量进行最优匹配
- 考虑交货日期进行合理的时间安排
- 优化运输路线，降低成本

### 3. 结果输出
- 生成详细的配送计划
- 显示每辆车的装载清单
- 提供时间安排和路线建议

## 业务流程
1. 用户上传基础数据文件
2. 系统解析和验证数据
3. 执行智能调度算法
4. 生成配送方案
5. 用户确认并导出结果

## 技术要求
- 支持 Excel 文件导入
- 提供可视化的流程设计界面
- 实时预览调度结果
- 支持方案调整和优化
`,
      lastModified: new Date()
    }
  ],
  
  // 操作方法
  updateAppInfo: (info) => set((state) => ({
    currentApp: { ...state.currentApp, ...info }
  })),
  
  setLeftPanelTab: (tab) => set((state) => ({
    leftPanel: { ...state.leftPanel, activeTab: tab }
  })),
  
  setDocumentEditing: (editing) => set((state) => ({
    leftPanel: { ...state.leftPanel, isDocumentEditing: editing }
  })),
  
  setCanvasMode: (mode) => set((state) => ({
    canvas: { ...state.canvas, mode }
  })),
  
  updateFlowData: (data) => set((state) => ({
    canvas: { ...state.canvas, flowData: data }
  })),
  
  addMessage: (message) => set((state) => ({
    chat: {
      ...state.chat,
      messages: [
        ...state.chat.messages,
        {
          ...message,
          id: Date.now().toString(),
          timestamp: new Date()
        }
      ]
    }
  })),
  
  setLoading: (loading) => set((state) => ({
    chat: { ...state.chat, isLoading: loading }
  })),
  
  addFile: (file) => set((state) => ({
    files: [
      ...state.files,
      {
        ...file,
        id: Date.now().toString(),
        lastModified: new Date()
      }
    ]
  })),
  
  updateFile: (id, content) => set((state) => ({
    files: state.files.map(file =>
      file.id === id
        ? { ...file, content, lastModified: new Date() }
        : file
    )
  })),
  
  deleteFile: (id) => set((state) => ({
    files: state.files.filter(file => file.id !== id)
  }))
}))
