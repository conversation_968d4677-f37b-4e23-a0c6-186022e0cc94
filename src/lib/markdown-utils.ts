import TurndownService from 'turndown'
import MarkdownIt from 'markdown-it'

// 创建 Markdown 转换器实例
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  bulletListMarker: '-',
  emDelimiter: '*',
  strongDelimiter: '**',
})

// 创建 Markdown 解析器实例
const markdownIt = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
})

// 自定义转换规则
turndownService.addRule('table', {
  filter: 'table',
  replacement: function (content) {
    return '\n\n' + content + '\n\n'
  }
})

turndownService.addRule('tableRow', {
  filter: 'tr',
  replacement: function (content, node) {
    const borderCells = Array.from(node.childNodes).map(() => '---').join(' | ')
    const isHeaderRow = node.parentNode?.nodeName === 'THEAD'
    
    if (isHeaderRow) {
      return '| ' + content + ' |\n| ' + borderCells + ' |\n'
    }
    return '| ' + content + ' |\n'
  }
})

turndownService.addRule('tableCell', {
  filter: ['th', 'td'],
  replacement: function (content) {
    return content.trim() + ' |'
  }
})

// 将 Markdown 转换为 HTML
export function markdownToHtml(markdown: string): string {
  try {
    return markdownIt.render(markdown)
  } catch (error) {
    console.error('Markdown to HTML conversion error:', error)
    return markdown
  }
}

// 将 HTML 转换为 Markdown
export function htmlToMarkdown(html: string): string {
  try {
    return turndownService.turndown(html)
  } catch (error) {
    console.error('HTML to Markdown conversion error:', error)
    return html
  }
}

// 检测内容是否为 Markdown 格式
export function isMarkdown(content: string): boolean {
  // 简单的 Markdown 检测规则
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /^\*\s+/m,               // 无序列表
    /^\d+\.\s+/m,            // 有序列表
    /^\>\s+/m,               // 引用
    /```[\s\S]*?```/,        // 代码块
    /\*\*.*?\*\*/,           // 粗体
    /\*.*?\*/,               // 斜体
    /\[.*?\]\(.*?\)/,        // 链接
    /!\[.*?\]\(.*?\)/,       // 图片
  ]
  
  return markdownPatterns.some(pattern => pattern.test(content))
}

// 清理 HTML 内容
export function sanitizeHtml(html: string): string {
  // 移除危险的标签和属性
  const cleanHtml = html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/javascript:/gi, '')
  
  return cleanHtml
}

// 提取纯文本内容
export function extractPlainText(html: string): string {
  return html.replace(/<[^>]*>/g, '').trim()
}

// 计算内容统计信息
export function getContentStats(content: string) {
  const plainText = extractPlainText(content)
  const words = plainText.split(/\s+/).filter(word => word.length > 0)
  const characters = plainText.length
  const charactersNoSpaces = plainText.replace(/\s/g, '').length
  const lines = content.split('\n').length
  
  return {
    words: words.length,
    characters,
    charactersNoSpaces,
    lines,
    readingTime: Math.ceil(words.length / 200) // 假设每分钟阅读200字
  }
}
