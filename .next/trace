[{"name": "generate-buildid", "duration": 74, "timestamp": 1964017066995, "id": 4, "parentId": 1, "tags": {}, "startTime": 1749719359370, "traceId": "0fa0bc3ca259aadd"}, {"name": "load-custom-routes", "duration": 109, "timestamp": 1964017067101, "id": 5, "parentId": 1, "tags": {}, "startTime": 1749719359370, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-dist-dir", "duration": 102, "timestamp": 1964017087836, "id": 6, "parentId": 1, "tags": {}, "startTime": 1749719359391, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-pages-mapping", "duration": 78, "timestamp": 1964017094134, "id": 7, "parentId": 1, "tags": {}, "startTime": 1749719359397, "traceId": "0fa0bc3ca259aadd"}, {"name": "collect-app-paths", "duration": 882, "timestamp": 1964017094226, "id": 8, "parentId": 1, "tags": {}, "startTime": 1749719359398, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-app-mapping", "duration": 765, "timestamp": 1964017095117, "id": 9, "parentId": 1, "tags": {}, "startTime": 1749719359398, "traceId": "0fa0bc3ca259aadd"}, {"name": "public-dir-conflict-check", "duration": 273, "timestamp": 1964017096056, "id": 10, "parentId": 1, "tags": {}, "startTime": 1749719359399, "traceId": "0fa0bc3ca259aadd"}, {"name": "generate-routes-manifest", "duration": 876, "timestamp": 1964017096497, "id": 11, "parentId": 1, "tags": {}, "startTime": 1749719359400, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-entrypoints", "duration": 5469, "timestamp": 1964017352874, "id": 15, "parentId": 13, "tags": {}, "startTime": 1749719359656, "traceId": "0fa0bc3ca259aadd"}, {"name": "generate-webpack-config", "duration": 119774, "timestamp": 1964017358422, "id": 16, "parentId": 14, "tags": {}, "startTime": 1749719359662, "traceId": "0fa0bc3ca259aadd"}, {"name": "next-trace-entrypoint-plugin", "duration": 941, "timestamp": 1964017520618, "id": 18, "parentId": 17, "tags": {}, "startTime": 1749719359824, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 100564, "timestamp": 1964017524218, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1749719359828, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 109698, "timestamp": 1964017524228, "id": 23, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1749719359828, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 123649, "timestamp": 1964017524240, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1749719359828, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 133763, "timestamp": 1964017523850, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fsrc%2Fapp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1749719359827, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 133522, "timestamp": 1964017524114, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Ffavicon.ico%2Froute&name=app%2Ffavicon.ico%2Froute&pagePath=private-next-app-dir%2Ffavicon.ico&appDir=%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fsrc%2Fapp&appPaths=%2Ffavicon.ico&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1749719359827, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 134593, "timestamp": 1964017524235, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1749719359828, "traceId": "0fa0bc3ca259aadd"}, {"name": "build-module-tsx", "duration": 15528, "timestamp": 1964017745186, "id": 36, "parentId": 17, "tags": {"name": "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx", "layer": "ssr"}, "startTime": 1749719360049, "traceId": "0fa0bc3ca259aadd"}, {"name": "make", "duration": 298517, "timestamp": 1964017523725, "id": 19, "parentId": 17, "tags": {}, "startTime": 1749719359827, "traceId": "0fa0bc3ca259aadd"}, {"name": "get-entries", "duration": 327, "timestamp": 1964017822835, "id": 38, "parentId": 37, "tags": {}, "startTime": 1749719360126, "traceId": "0fa0bc3ca259aadd"}, {"name": "node-file-trace-plugin", "duration": 25163, "timestamp": 1964017824922, "id": 39, "parentId": 37, "tags": {"traceEntryCount": "8"}, "startTime": 1749719360128, "traceId": "0fa0bc3ca259aadd"}, {"name": "collect-traced-files", "duration": 217, "timestamp": 1964017850095, "id": 40, "parentId": 37, "tags": {}, "startTime": 1749719360153, "traceId": "0fa0bc3ca259aadd"}, {"name": "finish-modules", "duration": 27563, "timestamp": 1964017822753, "id": 37, "parentId": 18, "tags": {}, "startTime": 1749719360126, "traceId": "0fa0bc3ca259aadd"}, {"name": "chunk-graph", "duration": 4572, "timestamp": 1964017865919, "id": 42, "parentId": 41, "tags": {}, "startTime": 1749719360169, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-modules", "duration": 11, "timestamp": 1964017870550, "id": 44, "parentId": 41, "tags": {}, "startTime": 1749719360174, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-chunks", "duration": 4028, "timestamp": 1964017870592, "id": 45, "parentId": 41, "tags": {}, "startTime": 1749719360174, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-tree", "duration": 56, "timestamp": 1964017874657, "id": 46, "parentId": 41, "tags": {}, "startTime": 1749719360178, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-chunk-modules", "duration": 8182, "timestamp": 1964017874748, "id": 47, "parentId": 41, "tags": {}, "startTime": 1749719360178, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize", "duration": 12442, "timestamp": 1964017870526, "id": 43, "parentId": 41, "tags": {}, "startTime": 1749719360174, "traceId": "0fa0bc3ca259aadd"}, {"name": "module-hash", "duration": 8133, "timestamp": 1964017890075, "id": 48, "parentId": 41, "tags": {}, "startTime": 1749719360193, "traceId": "0fa0bc3ca259aadd"}, {"name": "code-generation", "duration": 132403, "timestamp": 1964017898273, "id": 49, "parentId": 41, "tags": {}, "startTime": 1749719360202, "traceId": "0fa0bc3ca259aadd"}, {"name": "hash", "duration": 3188, "timestamp": 1964018032456, "id": 50, "parentId": 41, "tags": {}, "startTime": 1749719360336, "traceId": "0fa0bc3ca259aadd"}, {"name": "code-generation-jobs", "duration": 109, "timestamp": 1964018035642, "id": 51, "parentId": 41, "tags": {}, "startTime": 1749719360339, "traceId": "0fa0bc3ca259aadd"}, {"name": "module-assets", "duration": 119, "timestamp": 1964018035737, "id": 52, "parentId": 41, "tags": {}, "startTime": 1749719360339, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-chunk-assets", "duration": 1511, "timestamp": 1964018035867, "id": 53, "parentId": 41, "tags": {}, "startTime": 1749719360339, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 10231, "timestamp": 1964018043105, "id": 55, "parentId": 54, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1749719360346, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 10176, "timestamp": 1964018043167, "id": 56, "parentId": 54, "tags": {"name": "../app/favicon.ico/route.js", "cache": "HIT"}, "startTime": 1749719360347, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 10170, "timestamp": 1964018043173, "id": 57, "parentId": 54, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1749719360347, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 10166, "timestamp": 1964018043178, "id": 58, "parentId": 54, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1749719360347, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 80, "timestamp": 1964018053264, "id": 60, "parentId": 54, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1749719360357, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 65, "timestamp": 1964018053281, "id": 61, "parentId": 54, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1749719360357, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 61, "timestamp": 1964018053284, "id": 62, "parentId": 54, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1749719360357, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 58, "timestamp": 1964018053288, "id": 63, "parentId": 54, "tags": {"name": "169.js", "cache": "HIT"}, "startTime": 1749719360357, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 56, "timestamp": 1964018053291, "id": 64, "parentId": 54, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1749719360357, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 112640, "timestamp": 1964018043186, "id": 59, "parentId": 54, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1749719360347, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-webpack-plugin-optimize", "duration": 116818, "timestamp": 1964018039020, "id": 54, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1749719360342, "traceId": "0fa0bc3ca259aadd"}, {"name": "css-minimizer-plugin", "duration": 104, "timestamp": 1964018155911, "id": 65, "parentId": 17, "tags": {}, "startTime": 1749719360459, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-trace-assets", "duration": 613, "timestamp": 1964018156107, "id": 66, "parentId": 18, "tags": {}, "startTime": 1749719360459, "traceId": "0fa0bc3ca259aadd"}, {"name": "seal", "duration": 301818, "timestamp": 1964017858065, "id": 41, "parentId": 17, "tags": {}, "startTime": 1749719360161, "traceId": "0fa0bc3ca259aadd"}, {"name": "webpack-compilation", "duration": 642545, "timestamp": 1964017519611, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1749719359823, "traceId": "0fa0bc3ca259aadd"}, {"name": "emit", "duration": 2397, "timestamp": 1964018162325, "id": 67, "parentId": 14, "tags": {}, "startTime": 1749719360466, "traceId": "0fa0bc3ca259aadd"}, {"name": "webpack-close", "duration": 88910, "timestamp": 1964018165288, "id": 68, "parentId": 14, "tags": {"name": "server"}, "startTime": 1749719360469, "traceId": "0fa0bc3ca259aadd"}, {"name": "webpack-generate-error-stats", "duration": 1159, "timestamp": 1964018254228, "id": 69, "parentId": 68, "tags": {}, "startTime": 1749719360558, "traceId": "0fa0bc3ca259aadd"}, {"name": "run-webpack-compiler", "duration": 902676, "timestamp": 1964017352872, "id": 14, "parentId": 13, "tags": {}, "startTime": 1749719359656, "traceId": "0fa0bc3ca259aadd"}, {"name": "format-webpack-messages", "duration": 35, "timestamp": 1964018255551, "id": 70, "parentId": 13, "tags": {}, "startTime": 1749719360559, "traceId": "0fa0bc3ca259aadd"}, {"name": "worker-main-server", "duration": 902913, "timestamp": 1964017352724, "id": 13, "parentId": 1, "tags": {}, "startTime": 1749719359656, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-entrypoints", "duration": 5834, "timestamp": 1964018512979, "id": 74, "parentId": 72, "tags": {}, "startTime": 1749719360816, "traceId": "0fa0bc3ca259aadd"}, {"name": "generate-webpack-config", "duration": 117492, "timestamp": 1964018518874, "id": 75, "parentId": 73, "tags": {}, "startTime": 1749719360822, "traceId": "0fa0bc3ca259aadd"}, {"name": "make", "duration": 308, "timestamp": 1964018683009, "id": 77, "parentId": 76, "tags": {}, "startTime": 1749719360986, "traceId": "0fa0bc3ca259aadd"}, {"name": "chunk-graph", "duration": 297, "timestamp": 1964018684730, "id": 79, "parentId": 78, "tags": {}, "startTime": 1749719360988, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-modules", "duration": 12, "timestamp": 1964018685073, "id": 81, "parentId": 78, "tags": {}, "startTime": 1749719360988, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-chunks", "duration": 365, "timestamp": 1964018685129, "id": 82, "parentId": 78, "tags": {}, "startTime": 1749719360988, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-tree", "duration": 58, "timestamp": 1964018685521, "id": 83, "parentId": 78, "tags": {}, "startTime": 1749719360989, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize-chunk-modules", "duration": 201, "timestamp": 1964018685658, "id": 84, "parentId": 78, "tags": {}, "startTime": 1749719360989, "traceId": "0fa0bc3ca259aadd"}, {"name": "optimize", "duration": 835, "timestamp": 1964018685053, "id": 80, "parentId": 78, "tags": {}, "startTime": 1749719360988, "traceId": "0fa0bc3ca259aadd"}, {"name": "module-hash", "duration": 32, "timestamp": 1964018686291, "id": 85, "parentId": 78, "tags": {}, "startTime": 1749719360990, "traceId": "0fa0bc3ca259aadd"}, {"name": "code-generation", "duration": 79, "timestamp": 1964018686337, "id": 86, "parentId": 78, "tags": {}, "startTime": 1749719360990, "traceId": "0fa0bc3ca259aadd"}, {"name": "hash", "duration": 166, "timestamp": 1964018686502, "id": 87, "parentId": 78, "tags": {}, "startTime": 1749719360990, "traceId": "0fa0bc3ca259aadd"}, {"name": "code-generation-jobs", "duration": 42, "timestamp": 1964018686668, "id": 88, "parentId": 78, "tags": {}, "startTime": 1749719360990, "traceId": "0fa0bc3ca259aadd"}, {"name": "module-assets", "duration": 26, "timestamp": 1964018686701, "id": 89, "parentId": 78, "tags": {}, "startTime": 1749719360990, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-chunk-assets", "duration": 70, "timestamp": 1964018686731, "id": 90, "parentId": 78, "tags": {}, "startTime": 1749719360990, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-js", "duration": 60, "timestamp": 1964018691353, "id": 92, "parentId": 91, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1749719360995, "traceId": "0fa0bc3ca259aadd"}, {"name": "minify-webpack-plugin-optimize", "duration": 990, "timestamp": 1964018690428, "id": 91, "parentId": 76, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1749719360994, "traceId": "0fa0bc3ca259aadd"}, {"name": "css-minimizer-plugin", "duration": 44, "timestamp": 1964018691449, "id": 93, "parentId": 76, "tags": {}, "startTime": 1749719360995, "traceId": "0fa0bc3ca259aadd"}, {"name": "seal", "duration": 8755, "timestamp": 1964018684388, "id": 78, "parentId": 76, "tags": {}, "startTime": 1749719360988, "traceId": "0fa0bc3ca259aadd"}, {"name": "webpack-compilation", "duration": 13235, "timestamp": 1964018680047, "id": 76, "parentId": 73, "tags": {"name": "edge-server"}, "startTime": 1749719360983, "traceId": "0fa0bc3ca259aadd"}, {"name": "emit", "duration": 947, "timestamp": 1964018693424, "id": 94, "parentId": 73, "tags": {}, "startTime": 1749719360997, "traceId": "0fa0bc3ca259aadd"}, {"name": "webpack-close", "duration": 254, "timestamp": 1964018694726, "id": 95, "parentId": 73, "tags": {"name": "edge-server"}, "startTime": 1749719360998, "traceId": "0fa0bc3ca259aadd"}, {"name": "webpack-generate-error-stats", "duration": 1072, "timestamp": 1964018694997, "id": 96, "parentId": 95, "tags": {}, "startTime": 1749719360998, "traceId": "0fa0bc3ca259aadd"}, {"name": "run-webpack-compiler", "duration": 183173, "timestamp": 1964018512977, "id": 73, "parentId": 72, "tags": {}, "startTime": 1749719360816, "traceId": "0fa0bc3ca259aadd"}, {"name": "format-webpack-messages", "duration": 30, "timestamp": 1964018696156, "id": 97, "parentId": 72, "tags": {}, "startTime": 1749719360999, "traceId": "0fa0bc3ca259aadd"}, {"name": "worker-main-edge-server", "duration": 183426, "timestamp": 1964018512826, "id": 72, "parentId": 1, "tags": {}, "startTime": 1749719360816, "traceId": "0fa0bc3ca259aadd"}, {"name": "create-entrypoints", "duration": 5515, "timestamp": 1964018946893, "id": 100, "parentId": 98, "tags": {}, "startTime": 1749719361250, "traceId": "0fa0bc3ca259aadd"}, {"name": "generate-webpack-config", "duration": 117488, "timestamp": 1964018952476, "id": 101, "parentId": 99, "tags": {}, "startTime": 1749719361256, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 109375, "timestamp": 1964019115303, "id": 107, "parentId": 103, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 120111, "timestamp": 1964019115359, "id": 108, "parentId": 103, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 122205, "timestamp": 1964019115373, "id": 110, "parentId": 103, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 129928, "timestamp": 1964019115368, "id": 109, "parentId": 103, "tags": {"request": "/Users/<USER>/Projects/awoprojects/ideaflow/node_modules/next/dist/client/router.js"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 137745, "timestamp": 1964019115294, "id": 106, "parentId": 103, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 138587, "timestamp": 1964019115281, "id": 105, "parentId": 103, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 143248, "timestamp": 1964019115134, "id": 104, "parentId": 103, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1749719361418, "traceId": "0fa0bc3ca259aadd"}, {"name": "postcss-process", "duration": 53174, "timestamp": 1964019359292, "id": 116, "parentId": 115, "tags": {}, "startTime": 1749719361663, "traceId": "0fa0bc3ca259aadd"}, {"name": "postcss-loader", "duration": 160659, "timestamp": 1964019251942, "id": 115, "parentId": 114, "tags": {}, "startTime": 1749719361555, "traceId": "0fa0bc3ca259aadd"}, {"name": "css-loader", "duration": 15062, "timestamp": 1964019412699, "id": 117, "parentId": 114, "tags": {"astUsed": "true"}, "startTime": 1749719361716, "traceId": "0fa0bc3ca259aadd"}, {"name": "build-module-tsx", "duration": 11973, "timestamp": 1964019435868, "id": 118, "parentId": 102, "tags": {"name": "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx", "layer": "app-pages-browser"}, "startTime": 1749719361739, "traceId": "0fa0bc3ca259aadd"}, {"name": "build-module-css", "duration": 210797, "timestamp": 1964019241090, "id": 114, "parentId": 113, "tags": {"name": "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/globals.css.webpack[javascript/auto]!=!/Users/<USER>/Projects/awoprojects/ideaflow/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!/Users/<USER>/Projects/awoprojects/ideaflow/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!/Users/<USER>/Projects/awoprojects/ideaflow/src/app/globals.css", "layer": null}, "startTime": 1749719361544, "traceId": "0fa0bc3ca259aadd"}, {"name": "build-module-css", "duration": 231328, "timestamp": 1964019230454, "id": 113, "parentId": 102, "tags": {"name": "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/globals.css", "layer": "app-pages-browser"}, "startTime": 1749719361534, "traceId": "0fa0bc3ca259aadd"}, {"name": "build-module", "duration": 61, "timestamp": 1964019463765, "id": 119, "parentId": 113, "tags": {}, "startTime": 1749719361767, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 348524, "timestamp": 1964019115377, "id": 111, "parentId": 103, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "add-entry", "duration": 386122, "timestamp": 1964019115380, "id": 112, "parentId": 103, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Flayne%2FProjects%2Fawoprojects%2Fideaflow%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1749719361419, "traceId": "0fa0bc3ca259aadd"}, {"name": "make", "duration": 386665, "timestamp": 1964019114974, "id": 103, "parentId": 102, "tags": {}, "startTime": 1749719361418, "traceId": "0fa0bc3ca259aadd"}]