(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function n(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return n}})},660:(e,t)=>{"use strict";function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)|0;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},847:()=>{},907:(e,t,n)=>{"use strict";var r=n(3210),o=n(7379),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var h=a(e,(d=c(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],d[1]);return s(function(){f.hasValue=!0,f.value=h},[h]),u(h),h}},1135:()=>{},1204:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx","default")},1437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let r=n(4722),o=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function a(e){let t,n,i;for(let r of e.split("/"))if(n=o.find(e=>r.startsWith(e))){[t,i]=e.split(n,2);break}if(!t||!n||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),n){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=a.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},1658:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillMetadataSegment:function(){return f},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return h}});let r=n(8304),o=function(e){return e&&e.__esModule?e:{default:e}}(n(8671)),i=n(6341),a=n(4396),l=n(660),s=n(4722),c=n(2958),u=n(5499);function d(e){let t=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let n="";return t.split("/").some(e=>(0,u.isGroupSegment)(e)||(0,u.isParallelRouteSegment)(e))&&(n=(0,l.djb2Hash)(t).toString(36).slice(0,6)),n}function f(e,t,n){let r=(0,s.normalizeAppPath)(e),l=(0,a.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),u=(0,i.interpolateDynamicPath)(r,t,l),{name:f,ext:h}=o.default.parse(n),p=d(o.default.posix.join(e,f)),m=p?`-${p}`:"";return(0,c.normalizePathSep)(o.default.join(u,`${f}${m}${h}`))}function h(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,n="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":n=d(e),!t.endsWith("/route")){let{dir:e,name:r,ext:i}=o.default.parse(t);t=o.default.posix.join(e,`${r}${n?`-${n}`:""}${i}`,"route")}return t}function p(e,t){let n=e.endsWith("/route"),r=n?e.slice(0,-6):e,o=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${o}`)+(n?"/route":"")}},1788:(e,t,n)=>{Promise.resolve().then(n.bind(n,6254))},2437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return o}});let r=n(5362);function o(e,t){let n=[],o=(0,r.pathToRegexp)(e,n,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),i=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(o.source),o.flags):o,n);return(e,r)=>{if("string"!=typeof e)return!1;let o=i(e);if(!o)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of n)"number"==typeof e.name&&delete o.params[e.name];return{...r,...o.params}}}},2785:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},2958:(e,t)=>{"use strict";function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function o(e){return n.test(e)?e.replace(r,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3332:(e,t,n)=>{"use strict";var r=n(3210),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,u=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&u({inst:o})},[e,n,t]),a(function(){return c(o)&&u({inst:o}),e(function(){c(o)&&u({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},3644:(e,t,n)=>{Promise.resolve().then(n.bind(n,1204))},3736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),n(4827);let r=n(2785);function o(e,t,n){void 0===n&&(n=!0);let o=new URL("http://n"),i=t?new URL(t,o):e.startsWith(".")?new URL("http://n"):o,{pathname:a,searchParams:l,search:s,hash:c,href:u,origin:d}=new URL(e,i);if(d!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:n?(0,r.searchParamsToUrlQuery)(l):void 0,search:s,hash:c,href:u.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return s}});let r=n(6143),o=n(1437),i=n(3293),a=n(2887),l=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function s(e){let t=e.match(l);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function u(e,t,n){let r={},s=1,u=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(l);if(e&&a&&a[2]){let{key:t,optional:n,repeat:o}=c(a[2]);r[t]={pos:s++,repeat:o,optional:n},u.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:o}=c(a[2]);r[e]={pos:s++,repeat:t,optional:o},n&&a[1]&&u.push("/"+(0,i.escapeStringRegexp)(a[1]));let l=t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";n&&a[1]&&(l=l.substring(1)),u.push(l)}else u.push("/"+(0,i.escapeStringRegexp)(d));t&&a&&a[3]&&u.push((0,i.escapeStringRegexp)(a[3]))}return{parameterizedRoute:u.join(""),groups:r}}function d(e,t){let{includeSuffix:n=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:o=!1}=void 0===t?{}:t,{parameterizedRoute:i,groups:a}=u(e,n,r),l=i;return o||(l+="(?:/)?"),{re:RegExp("^"+l+"$"),groups:a}}function f(e){let t,{interceptionMarker:n,getSafeRouteKey:r,segment:o,routeKeys:a,keyPrefix:l,backreferenceDuplicateKeys:s}=e,{key:u,optional:d,repeat:f}=c(o),h=u.replace(/\W/g,"");l&&(h=""+l+h);let p=!1;(0===h.length||h.length>30)&&(p=!0),isNaN(parseInt(h.slice(0,1)))||(p=!0),p&&(h=r());let m=h in a;l?a[h]=""+l+u:a[h]=u;let g=n?(0,i.escapeStringRegexp)(n):"";return t=m&&s?"\\k<"+h+">":f?"(?<"+h+">.+?)":"(?<"+h+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function h(e,t,n,s,c){let u,d=(u=0,()=>{let e="",t=++u;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),h={},p=[];for(let u of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)),a=u.match(l);if(e&&a&&a[2])p.push(f({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:h,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(a&&a[2]){s&&a[1]&&p.push("/"+(0,i.escapeStringRegexp)(a[1]));let e=f({getSafeRouteKey:d,segment:a[2],routeKeys:h,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});s&&a[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,i.escapeStringRegexp)(u));n&&a&&a[3]&&p.push((0,i.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:h}}function p(e,t){var n,r,o;let i=h(e,t.prefixRouteKeys,null!=(n=t.includeSuffix)&&n,null!=(r=t.includePrefix)&&r,null!=(o=t.backreferenceDuplicateKeys)&&o),a=i.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:i.routeKeys}}function m(e,t){let{parameterizedRoute:n}=u(e,!1,!1),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:o}=h(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(r?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c,metadata:()=>s});var r=n(7413),o=n(2202),i=n.n(o),a=n(4988),l=n.n(a);n(1135);let s={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:e})})}},4722:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let r=n(5531),o=n(5499);function i(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,n,r)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&n===r.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return x},MissingStaticPage:function(){return v},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return h},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return s},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return y}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,h=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class x extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},4852:()=>{},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var o="",i=n+1;i<e.length;){var a=e.charCodeAt(i);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){o+=e[i++];continue}break}if(!o)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:o}),n=i;continue}if("("===r){var l=1,s="",i=n+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){s+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--l){i++;break}}else if("("===e[i]&&(l++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);s+=e[i++]}if(l)throw TypeError("Unbalanced pattern at "+n);if(!s)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:s}),n=i;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,i=void 0===r?"./":r,a="[^"+o(t.delimiter||"/#?")+"]+?",l=[],s=0,c=0,u="",d=function(e){if(c<n.length&&n[c].type===e)return n[c++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var r=n[c];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},h=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};c<n.length;){var p=d("CHAR"),m=d("NAME"),g=d("PATTERN");if(m||g){var v=p||"";-1===i.indexOf(v)&&(u+=v,v=""),u&&(l.push(u),u=""),l.push({name:m||s++,prefix:v,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var x=p||d("ESCAPED_CHAR");if(x){u+=x;continue}if(u&&(l.push(u),u=""),d("OPEN")){var v=h(),y=d("NAME")||"",b=d("PATTERN")||"",w=h();f("CLOSE"),l.push({name:y||(b?s++:""),pattern:y&&!b?a:b,prefix:v,suffix:w,modifier:d("MODIFIER")||""});continue}f("END")}return l}function n(e,t){void 0===t&&(t={});var n=i(t),r=t.encode,o=void 0===r?function(e){return e}:r,a=t.validate,l=void 0===a||a,s=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",n)});return function(t){for(var n="",r=0;r<e.length;r++){var i=e[r];if("string"==typeof i){n+=i;continue}var a=t?t[i.name]:void 0,c="?"===i.modifier||"*"===i.modifier,u="*"===i.modifier||"+"===i.modifier;if(Array.isArray(a)){if(!u)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===a.length){if(c)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var d=0;d<a.length;d++){var f=o(a[d],i);if(l&&!s[r].test(f))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+f+'"');n+=i.prefix+f+i.suffix}continue}if("string"==typeof a||"number"==typeof a){var f=o(String(a),i);if(l&&!s[r].test(f))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+f+'"');n+=i.prefix+f+i.suffix;continue}if(!c){var h=u?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+h)}}return n}}function r(e,t,n){void 0===n&&(n={});var r=n.decode,o=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var i=r[0],a=r.index,l=Object.create(null),s=1;s<r.length;s++)!function(e){if(void 0!==r[e]){var n=t[e-1];"*"===n.modifier||"+"===n.modifier?l[n.name]=r[e].split(n.prefix+n.suffix).map(function(e){return o(e,n)}):l[n.name]=o(r[e],n)}}(s);return{path:i,index:a,params:l}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function a(e,t,n){void 0===n&&(n={});for(var r=n.strict,a=void 0!==r&&r,l=n.start,s=n.end,c=n.encode,u=void 0===c?function(e){return e}:c,d="["+o(n.endsWith||"")+"]|$",f="["+o(n.delimiter||"/#?")+"]",h=void 0===l||l?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)h+=o(u(m));else{var g=o(u(m.prefix)),v=o(u(m.suffix));if(m.pattern)if(t&&t.push(m),g||v)if("+"===m.modifier||"*"===m.modifier){var x="*"===m.modifier?"?":"";h+="(?:"+g+"((?:"+m.pattern+")(?:"+v+g+"(?:"+m.pattern+"))*)"+v+")"+x}else h+="(?:"+g+"("+m.pattern+")"+v+")"+m.modifier;else h+="("+m.pattern+")"+m.modifier;else h+="(?:"+g+v+")"+m.modifier}}if(void 0===s||s)a||(h+=f+"?"),h+=n.endsWith?"(?="+d+")":"$";else{var y=e[e.length-1],b="string"==typeof y?f.indexOf(y[y.length-1])>-1:void 0===y;a||(h+="(?:"+f+"(?="+d+"))?"),b||(h+="(?="+f+"|"+d+")")}return new RegExp(h,i(n))}function l(t,n,r){if(t instanceof RegExp){if(!n)return t;var o=t.source.match(/\((?!\?)/g);if(o)for(var s=0;s<o.length;s++)n.push({name:s,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return l(e,n,r).source}).join("|")+")",i(r)):a(e(t,r),n,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return n(e(t,r),r)},t.tokensToFunction=n,t.match=function(e,t){var n=[];return r(l(e,n,t),n,t)},t.regexpToFunction=r,t.tokensToRegexp=a,t.pathToRegexp=l})(),e.exports=t})()},5526:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{compileNonPath:function(){return u},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return f}});let r=n(5362),o=n(3293),i=n(6759),a=n(1437),l=n(8212);function s(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,n,r){void 0===n&&(n=[]),void 0===r&&(r=[]);let o={},i=n=>{let r,i=n.key;switch(n.type){case"header":i=i.toLowerCase(),r=e.headers[i];break;case"cookie":r="cookies"in e?e.cookies[n.key]:(0,l.getCookieParser)(e.headers)()[n.key];break;case"query":r=t[i];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&r)return o[function(e){let t="";for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);(r>64&&r<91||r>96&&r<123)&&(t+=e[n])}return t}(i)]=r,!0;if(r){let e=RegExp("^"+n.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{o[e]=t.groups[e]}):"host"===n.type&&t[0]&&(o.host=t[0])),!0}return!1};return!(!n.every(e=>i(e))||r.some(e=>i(e)))&&o}function u(e,t){if(!e.includes(":"))return e;for(let n of Object.keys(t))e.includes(":"+n)&&(e=e.replace(RegExp(":"+n+"\\*","g"),":"+n+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+n+"\\?","g"),":"+n+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+n+"\\+","g"),":"+n+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+n+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+n));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let n of Object.keys({...e.params,...e.query}))n&&(t=t.replace(RegExp(":"+(0,o.escapeStringRegexp)(n),"g"),"__ESC_COLON_"+n));let n=(0,i.parseUrl)(t),r=n.pathname;r&&(r=s(r));let a=n.href;a&&(a=s(a));let l=n.hostname;l&&(l=s(l));let c=n.hash;return c&&(c=s(c)),{...n,pathname:r,hostname:l,href:a,hash:c}}function f(e){let t,n,o=Object.assign({},e.query),i=d(e),{hostname:l,query:c}=i,f=i.pathname;i.hash&&(f=""+f+i.hash);let h=[],p=[];for(let e of((0,r.pathToRegexp)(f,p),p))h.push(e.name);if(l){let e=[];for(let t of((0,r.pathToRegexp)(l,e),e))h.push(t.name)}let m=(0,r.compile)(f,{validate:!1});for(let[n,o]of(l&&(t=(0,r.compile)(l,{validate:!1})),Object.entries(c)))Array.isArray(o)?c[n]=o.map(t=>u(s(t),e.params)):"string"==typeof o&&(c[n]=u(s(o),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>h.includes(e)))for(let t of g)t in c||(c[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let n=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(n){"(..)(..)"===n?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=n;break}}try{let[r,o]=(n=m(e.params)).split("#",2);t&&(i.hostname=t(e.params)),i.pathname=r,i.hash=(o?"#":"")+(o||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...o,...i.query},{newUrl:n,destQuery:c,parsedDestination:i}}},5531:(e,t)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},5792:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6444,23)),Promise.resolve().then(n.t.bind(n,6042,23)),Promise.resolve().then(n.t.bind(n,8170,23)),Promise.resolve().then(n.t.bind(n,9477,23)),Promise.resolve().then(n.t.bind(n,9345,23)),Promise.resolve().then(n.t.bind(n,2089,23)),Promise.resolve().then(n.t.bind(n,6577,23)),Promise.resolve().then(n.t.bind(n,1307,23))},6254:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>he});var r,o,i,a,l,s,c,u,d=n(687),f=n(3210),h=n.t(f,2);function p(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function m(...e){return t=>{let n=!1,r=e.map(e=>{let r=p(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():p(e[t],null)}}}}function g(...e){return f.useCallback(m(...e),e)}function v(e){let t=function(e){let t=f.forwardRef((e,t)=>{let{children:n,...r}=e;if(f.isValidElement(n)){var o;let e,i,a=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),l=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==f.Fragment&&(l.ref=t?m(t,a):a),f.cloneElement(n,l)}return f.Children.count(n)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=f.forwardRef((e,n)=>{let{children:r,...o}=e,i=f.Children.toArray(r),a=i.find(b);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:f.Children.count(e)>1?f.Children.only(null):f.isValidElement(e)?e.props.children:null);return(0,d.jsx)(t,{...o,ref:n,children:f.isValidElement(e)?f.cloneElement(e,void 0,r):null})}return(0,d.jsx)(t,{...o,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var x=v("Slot"),y=Symbol("radix.slottable");function b(e){return f.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===y}function w(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}let E=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,_=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return w(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:i}=t,a=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let a=E(t)||E(r);return o[e][a]}),l=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return w(e,a,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...l}[t]):({...i,...l})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)},N=e=>{let t=M(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),S(n,t)||j(e)},getConflictingClassGroupIds:(e,t)=>{let o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}},S=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),o=r?S(e.slice(1),r):void 0;if(o)return o;if(0===t.validators.length)return;let i=e.join("-");return t.validators.find(({validator:e})=>e(i))?.classGroupId},k=/^\[(.+)\]$/,j=e=>{if(k.test(e)){let t=k.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},M=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)R(n[e],r,e,t);return r},R=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:C(t,e)).classGroupId=n;return}if("function"==typeof e)return A(e)?void R(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,o])=>{R(o,C(t,e),n,r)})})},C=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},A=e=>e.isThemeGetter,P=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,o=(o,i)=>{n.set(o,i),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},T=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n=[],r=0,o=0,i=0;for(let a=0;a<e.length;a++){let l=e[a];if(0===r&&0===o){if(":"===l){n.push(e.slice(i,a)),i=a+1;continue}if("/"===l){t=a;continue}}"["===l?r++:"]"===l?r--:"("===l?o++:")"===l&&o--}let a=0===n.length?e:e.substring(i),l=O(a);return{modifiers:n,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},O=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,z=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},D=e=>({cache:P(e.cacheSize),parseClassName:T(e),sortModifiers:z(e),...N(e)}),I=/\s+/,L=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:i}=t,a=[],l=e.trim().split(I),s="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:h}=n(t);if(c){s=t+(s.length>0?" "+s:s);continue}let p=!!h,m=r(p?f.substring(0,h):f);if(!m){if(!p||!(m=r(f))){s=t+(s.length>0?" "+s:s);continue}p=!1}let g=i(u).join(":"),v=d?g+"!":g,x=v+m;if(a.includes(x))continue;a.push(x);let y=o(m,p);for(let e=0;e<y.length;++e){let t=y[e];a.push(v+t)}s=t+(s.length>0?" "+s:s)}return s};function $(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=H(e))&&(r&&(r+=" "),r+=t);return r}let H=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=H(e[r]))&&(n&&(n+=" "),n+=t);return n},F=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},B=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,V=/^\((?:(\w[\w-]*):)?(.+)\)$/i,U=/^\d+\/\d+$/,W=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,X=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,K=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Y=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,G=e=>U.test(e),Z=e=>!!e&&!Number.isNaN(Number(e)),Q=e=>!!e&&Number.isInteger(Number(e)),J=e=>e.endsWith("%")&&Z(e.slice(0,-1)),ee=e=>W.test(e),et=()=>!0,en=e=>X.test(e)&&!q.test(e),er=()=>!1,eo=e=>K.test(e),ei=e=>Y.test(e),ea=e=>!es(e)&&!ep(e),el=e=>ew(e,eS,er),es=e=>B.test(e),ec=e=>ew(e,ek,en),eu=e=>ew(e,ej,Z),ed=e=>ew(e,e_,er),ef=e=>ew(e,eN,ei),eh=e=>ew(e,eR,eo),ep=e=>V.test(e),em=e=>eE(e,ek),eg=e=>eE(e,eM),ev=e=>eE(e,e_),ex=e=>eE(e,eS),ey=e=>eE(e,eN),eb=e=>eE(e,eR,!0),ew=(e,t,n)=>{let r=B.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},eE=(e,t,n=!1)=>{let r=V.exec(e);return!!r&&(r[1]?t(r[1]):n)},e_=e=>"position"===e||"percentage"===e,eN=e=>"image"===e||"url"===e,eS=e=>"length"===e||"size"===e||"bg-size"===e,ek=e=>"length"===e,ej=e=>"number"===e,eM=e=>"family-name"===e,eR=e=>"shadow"===e;Symbol.toStringTag;let eC=function(e,...t){let n,r,o,i=function(l){return r=(n=D(t.reduce((e,t)=>t(e),e()))).cache.get,o=n.cache.set,i=a,a(l)};function a(e){let t=r(e);if(t)return t;let i=L(e,n);return o(e,i),i}return function(){return i($.apply(null,arguments))}}(()=>{let e=F("color"),t=F("font"),n=F("text"),r=F("font-weight"),o=F("tracking"),i=F("leading"),a=F("breakpoint"),l=F("container"),s=F("spacing"),c=F("radius"),u=F("shadow"),d=F("inset-shadow"),f=F("text-shadow"),h=F("drop-shadow"),p=F("blur"),m=F("perspective"),g=F("aspect"),v=F("ease"),x=F("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...b(),ep,es],E=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto","contain","none"],N=()=>[ep,es,s],S=()=>[G,"full","auto",...N()],k=()=>[Q,"none","subgrid",ep,es],j=()=>["auto",{span:["full",Q,ep,es]},Q,ep,es],M=()=>[Q,"auto",ep,es],R=()=>["auto","min","max","fr",ep,es],C=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],A=()=>["start","end","center","stretch","center-safe","end-safe"],P=()=>["auto",...N()],T=()=>[G,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],O=()=>[e,ep,es],z=()=>[...b(),ev,ed,{position:[ep,es]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],I=()=>["auto","cover","contain",ex,el,{size:[ep,es]}],L=()=>[J,em,ec],$=()=>["","none","full",c,ep,es],H=()=>["",Z,em,ec],B=()=>["solid","dashed","dotted","double"],V=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],U=()=>[Z,J,ev,ed],W=()=>["","none",p,ep,es],X=()=>["none",Z,ep,es],q=()=>["none",Z,ep,es],K=()=>[Z,ep,es],Y=()=>[G,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ee],breakpoint:[ee],color:[et],container:[ee],"drop-shadow":[ee],ease:["in","out","in-out"],font:[ea],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ee],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ee],shadow:[ee],spacing:["px",Z],text:[ee],"text-shadow":[ee],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",G,es,ep,g]}],container:["container"],columns:[{columns:[Z,es,ep,l]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:_()}],"overscroll-x":[{"overscroll-x":_()}],"overscroll-y":[{"overscroll-y":_()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[Q,"auto",ep,es]}],basis:[{basis:[G,"full","auto",l,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Z,G,"auto","initial","none",es]}],grow:[{grow:["",Z,ep,es]}],shrink:[{shrink:["",Z,ep,es]}],order:[{order:[Q,"first","last","none",ep,es]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:j()}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:j()}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":R()}],"auto-rows":[{"auto-rows":R()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...C(),"normal"]}],"justify-items":[{"justify-items":[...A(),"normal"]}],"justify-self":[{"justify-self":["auto",...A()]}],"align-content":[{content:["normal",...C()]}],"align-items":[{items:[...A(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...A(),{baseline:["","last"]}]}],"place-content":[{"place-content":C()}],"place-items":[{"place-items":[...A(),"baseline"]}],"place-self":[{"place-self":["auto",...A()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:P()}],mx:[{mx:P()}],my:[{my:P()}],ms:[{ms:P()}],me:[{me:P()}],mt:[{mt:P()}],mr:[{mr:P()}],mb:[{mb:P()}],ml:[{ml:P()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:T()}],w:[{w:[l,"screen",...T()]}],"min-w":[{"min-w":[l,"screen","none",...T()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...T()]}],h:[{h:["screen","lh",...T()]}],"min-h":[{"min-h":["screen","lh","none",...T()]}],"max-h":[{"max-h":["screen","lh",...T()]}],"font-size":[{text:["base",n,em,ec]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,ep,eu]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",J,es]}],"font-family":[{font:[eg,es,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,ep,es]}],"line-clamp":[{"line-clamp":[Z,"none",ep,eu]}],leading:[{leading:[i,...N()]}],"list-image":[{"list-image":["none",ep,es]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ep,es]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:O()}],"text-color":[{text:O()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...B(),"wavy"]}],"text-decoration-thickness":[{decoration:[Z,"from-font","auto",ep,ec]}],"text-decoration-color":[{decoration:O()}],"underline-offset":[{"underline-offset":[Z,"auto",ep,es]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ep,es]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ep,es]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:z()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:I()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Q,ep,es],radial:["",ep,es],conic:[Q,ep,es]},ey,ef]}],"bg-color":[{bg:O()}],"gradient-from-pos":[{from:L()}],"gradient-via-pos":[{via:L()}],"gradient-to-pos":[{to:L()}],"gradient-from":[{from:O()}],"gradient-via":[{via:O()}],"gradient-to":[{to:O()}],rounded:[{rounded:$()}],"rounded-s":[{"rounded-s":$()}],"rounded-e":[{"rounded-e":$()}],"rounded-t":[{"rounded-t":$()}],"rounded-r":[{"rounded-r":$()}],"rounded-b":[{"rounded-b":$()}],"rounded-l":[{"rounded-l":$()}],"rounded-ss":[{"rounded-ss":$()}],"rounded-se":[{"rounded-se":$()}],"rounded-ee":[{"rounded-ee":$()}],"rounded-es":[{"rounded-es":$()}],"rounded-tl":[{"rounded-tl":$()}],"rounded-tr":[{"rounded-tr":$()}],"rounded-br":[{"rounded-br":$()}],"rounded-bl":[{"rounded-bl":$()}],"border-w":[{border:H()}],"border-w-x":[{"border-x":H()}],"border-w-y":[{"border-y":H()}],"border-w-s":[{"border-s":H()}],"border-w-e":[{"border-e":H()}],"border-w-t":[{"border-t":H()}],"border-w-r":[{"border-r":H()}],"border-w-b":[{"border-b":H()}],"border-w-l":[{"border-l":H()}],"divide-x":[{"divide-x":H()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":H()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...B(),"hidden","none"]}],"divide-style":[{divide:[...B(),"hidden","none"]}],"border-color":[{border:O()}],"border-color-x":[{"border-x":O()}],"border-color-y":[{"border-y":O()}],"border-color-s":[{"border-s":O()}],"border-color-e":[{"border-e":O()}],"border-color-t":[{"border-t":O()}],"border-color-r":[{"border-r":O()}],"border-color-b":[{"border-b":O()}],"border-color-l":[{"border-l":O()}],"divide-color":[{divide:O()}],"outline-style":[{outline:[...B(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Z,ep,es]}],"outline-w":[{outline:["",Z,em,ec]}],"outline-color":[{outline:O()}],shadow:[{shadow:["","none",u,eb,eh]}],"shadow-color":[{shadow:O()}],"inset-shadow":[{"inset-shadow":["none",d,eb,eh]}],"inset-shadow-color":[{"inset-shadow":O()}],"ring-w":[{ring:H()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:O()}],"ring-offset-w":[{"ring-offset":[Z,ec]}],"ring-offset-color":[{"ring-offset":O()}],"inset-ring-w":[{"inset-ring":H()}],"inset-ring-color":[{"inset-ring":O()}],"text-shadow":[{"text-shadow":["none",f,eb,eh]}],"text-shadow-color":[{"text-shadow":O()}],opacity:[{opacity:[Z,ep,es]}],"mix-blend":[{"mix-blend":[...V(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":V()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Z]}],"mask-image-linear-from-pos":[{"mask-linear-from":U()}],"mask-image-linear-to-pos":[{"mask-linear-to":U()}],"mask-image-linear-from-color":[{"mask-linear-from":O()}],"mask-image-linear-to-color":[{"mask-linear-to":O()}],"mask-image-t-from-pos":[{"mask-t-from":U()}],"mask-image-t-to-pos":[{"mask-t-to":U()}],"mask-image-t-from-color":[{"mask-t-from":O()}],"mask-image-t-to-color":[{"mask-t-to":O()}],"mask-image-r-from-pos":[{"mask-r-from":U()}],"mask-image-r-to-pos":[{"mask-r-to":U()}],"mask-image-r-from-color":[{"mask-r-from":O()}],"mask-image-r-to-color":[{"mask-r-to":O()}],"mask-image-b-from-pos":[{"mask-b-from":U()}],"mask-image-b-to-pos":[{"mask-b-to":U()}],"mask-image-b-from-color":[{"mask-b-from":O()}],"mask-image-b-to-color":[{"mask-b-to":O()}],"mask-image-l-from-pos":[{"mask-l-from":U()}],"mask-image-l-to-pos":[{"mask-l-to":U()}],"mask-image-l-from-color":[{"mask-l-from":O()}],"mask-image-l-to-color":[{"mask-l-to":O()}],"mask-image-x-from-pos":[{"mask-x-from":U()}],"mask-image-x-to-pos":[{"mask-x-to":U()}],"mask-image-x-from-color":[{"mask-x-from":O()}],"mask-image-x-to-color":[{"mask-x-to":O()}],"mask-image-y-from-pos":[{"mask-y-from":U()}],"mask-image-y-to-pos":[{"mask-y-to":U()}],"mask-image-y-from-color":[{"mask-y-from":O()}],"mask-image-y-to-color":[{"mask-y-to":O()}],"mask-image-radial":[{"mask-radial":[ep,es]}],"mask-image-radial-from-pos":[{"mask-radial-from":U()}],"mask-image-radial-to-pos":[{"mask-radial-to":U()}],"mask-image-radial-from-color":[{"mask-radial-from":O()}],"mask-image-radial-to-color":[{"mask-radial-to":O()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":b()}],"mask-image-conic-pos":[{"mask-conic":[Z]}],"mask-image-conic-from-pos":[{"mask-conic-from":U()}],"mask-image-conic-to-pos":[{"mask-conic-to":U()}],"mask-image-conic-from-color":[{"mask-conic-from":O()}],"mask-image-conic-to-color":[{"mask-conic-to":O()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:z()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:I()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ep,es]}],filter:[{filter:["","none",ep,es]}],blur:[{blur:W()}],brightness:[{brightness:[Z,ep,es]}],contrast:[{contrast:[Z,ep,es]}],"drop-shadow":[{"drop-shadow":["","none",h,eb,eh]}],"drop-shadow-color":[{"drop-shadow":O()}],grayscale:[{grayscale:["",Z,ep,es]}],"hue-rotate":[{"hue-rotate":[Z,ep,es]}],invert:[{invert:["",Z,ep,es]}],saturate:[{saturate:[Z,ep,es]}],sepia:[{sepia:["",Z,ep,es]}],"backdrop-filter":[{"backdrop-filter":["","none",ep,es]}],"backdrop-blur":[{"backdrop-blur":W()}],"backdrop-brightness":[{"backdrop-brightness":[Z,ep,es]}],"backdrop-contrast":[{"backdrop-contrast":[Z,ep,es]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Z,ep,es]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Z,ep,es]}],"backdrop-invert":[{"backdrop-invert":["",Z,ep,es]}],"backdrop-opacity":[{"backdrop-opacity":[Z,ep,es]}],"backdrop-saturate":[{"backdrop-saturate":[Z,ep,es]}],"backdrop-sepia":[{"backdrop-sepia":["",Z,ep,es]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ep,es]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Z,"initial",ep,es]}],ease:[{ease:["linear","initial",v,ep,es]}],delay:[{delay:[Z,ep,es]}],animate:[{animate:["none",x,ep,es]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,ep,es]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:X()}],"rotate-x":[{"rotate-x":X()}],"rotate-y":[{"rotate-y":X()}],"rotate-z":[{"rotate-z":X()}],scale:[{scale:q()}],"scale-x":[{"scale-x":q()}],"scale-y":[{"scale-y":q()}],"scale-z":[{"scale-z":q()}],"scale-3d":["scale-3d"],skew:[{skew:K()}],"skew-x":[{"skew-x":K()}],"skew-y":[{"skew-y":K()}],transform:[{transform:[ep,es,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Y()}],"translate-x":[{"translate-x":Y()}],"translate-y":[{"translate-y":Y()}],"translate-z":[{"translate-z":Y()}],"translate-none":["translate-none"],accent:[{accent:O()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:O()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ep,es]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ep,es]}],fill:[{fill:["none",...O()]}],"stroke-w":[{stroke:[Z,em,ec,eu]}],stroke:[{stroke:["none",...O()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eA(...e){return eC(w(e))}let eP=_("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function eT({className:e,variant:t,size:n,asChild:r=!1,...o}){return(0,d.jsx)(r?x:"button",{"data-slot":"button",className:eA(eP({variant:t,size:n,className:e})),...o})}function eO(e,t=[]){let n=[],r=()=>{let t=n.map(e=>f.createContext(e));return function(n){let r=n?.[e]||t;return f.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=f.createContext(r),i=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,l=n?.[e]?.[i]||o,s=f.useMemo(()=>a,Object.values(a));return(0,d.jsx)(l.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let l=a?.[e]?.[i]||o,s=f.useContext(l);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function ez(e){let t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...e)=>t.current?.(...e),[])}var eD=globalThis?.document?f.useLayoutEffect:()=>{},eI=n(1215),eL=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=v(`Primitive.${t}`),r=f.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function e$(e,t){e&&eI.flushSync(()=>e.dispatchEvent(t))}var eH=n(7379);function eF(){return()=>{}}var eB="Avatar",[eV,eU]=eO(eB),[eW,eX]=eV(eB),eq=f.forwardRef((e,t)=>{let{__scopeAvatar:n,...r}=e,[o,i]=f.useState("idle");return(0,d.jsx)(eW,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:i,children:(0,d.jsx)(eL.span,{...r,ref:t})})});eq.displayName=eB;var eK="AvatarImage";f.forwardRef((e,t)=>{let{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...i}=e,a=eX(eK,n),l=function(e,{referrerPolicy:t,crossOrigin:n}){let r=(0,eH.useSyncExternalStore)(eF,()=>!0,()=>!1),o=f.useRef(null),i=r?(o.current||(o.current=new window.Image),o.current):null,[a,l]=f.useState(()=>eZ(i,e));return eD(()=>{l(eZ(i,e))},[i,e]),eD(()=>{let e=e=>()=>{l(e)};if(!i)return;let r=e("loaded"),o=e("error");return i.addEventListener("load",r),i.addEventListener("error",o),t&&(i.referrerPolicy=t),"string"==typeof n&&(i.crossOrigin=n),()=>{i.removeEventListener("load",r),i.removeEventListener("error",o)}},[i,n,t]),a}(r,i),s=ez(e=>{o(e),a.onImageLoadingStatusChange(e)});return eD(()=>{"idle"!==l&&s(l)},[l,s]),"loaded"===l?(0,d.jsx)(eL.img,{...i,ref:t,src:r}):null}).displayName=eK;var eY="AvatarFallback",eG=f.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:r,...o}=e,i=eX(eY,n),[a,l]=f.useState(void 0===r);return f.useEffect(()=>{if(void 0!==r){let e=window.setTimeout(()=>l(!0),r);return()=>window.clearTimeout(e)}},[r]),a&&"loaded"!==i.imageLoadingStatus?(0,d.jsx)(eL.span,{...o,ref:t}):null});function eZ(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function eQ({className:e,...t}){return(0,d.jsx)(eq,{"data-slot":"avatar",className:eA("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function eJ({className:e,...t}){return(0,d.jsx)(eG,{"data-slot":"avatar-fallback",className:eA("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function e0(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}eG.displayName=eY;var e1=h[" useInsertionEffect ".trim().toString()]||eD;function e2({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=f.useState(e),o=f.useRef(n),i=f.useRef(t);return e1(()=>{i.current=t},[t]),f.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),l=void 0!==e,s=l?e:o;{let t=f.useRef(void 0!==e);f.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[s,f.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[l,e,i,a])]}function e3(e){let t=e+"CollectionProvider",[n,r]=eO(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=f.useRef(null),i=f.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let l=e+"CollectionSlot",s=v(l),c=f.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=g(t,i(l,n).collectionRef);return(0,d.jsx)(s,{ref:o,children:r})});c.displayName=l;let u=e+"CollectionItemSlot",h="data-radix-collection-item",p=v(u),m=f.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=f.useRef(null),l=g(t,a),s=i(u,n);return f.useEffect(()=>(s.itemMap.set(a,{ref:a,...o}),()=>void s.itemMap.delete(a))),(0,d.jsx)(p,{...{[h]:""},ref:l,children:r})});return m.displayName=u,[{Provider:a,Slot:c,ItemSlot:m},function(t){let n=i(e+"CollectionConsumer",t);return f.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}Symbol("RADIX:SYNC_STATE");var e4=new WeakMap;function e5(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=e6(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function e6(e){return e!=e||0===e?0:Math.trunc(e)}var e8=f.createContext(void 0);function e7(e){let t=f.useContext(e8);return e||t||"ltr"}var e9="dismissableLayer.update",te=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),tt=f.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:a,onDismiss:l,...s}=e,c=f.useContext(te),[u,h]=f.useState(null),p=u?.ownerDocument??globalThis?.document,[,m]=f.useState({}),v=g(t,e=>h(e)),x=Array.from(c.layers),[y]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),b=x.indexOf(y),w=u?x.indexOf(u):-1,E=c.layersWithOutsidePointerEventsDisabled.size>0,_=w>=b,N=function(e,t=globalThis?.document){let n=ez(e),r=f.useRef(!1),o=f.useRef(()=>{});return f.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){tr("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...c.branches].some(e=>e.contains(t));_&&!n&&(o?.(e),a?.(e),e.defaultPrevented||l?.())},p),S=function(e,t=globalThis?.document){let n=ez(e),r=f.useRef(!1);return f.useEffect(()=>{let e=e=>{e.target&&!r.current&&tr("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...c.branches].some(e=>e.contains(t))&&(i?.(e),a?.(e),e.defaultPrevented||l?.())},p);return!function(e,t=globalThis?.document){let n=ez(e);f.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{w===c.layers.size-1&&(r?.(e),!e.defaultPrevented&&l&&(e.preventDefault(),l()))},p),f.useEffect(()=>{if(u)return n&&(0===c.layersWithOutsidePointerEventsDisabled.size&&(l$=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(u)),c.layers.add(u),tn(),()=>{n&&1===c.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=l$)}},[u,p,n,c]),f.useEffect(()=>()=>{u&&(c.layers.delete(u),c.layersWithOutsidePointerEventsDisabled.delete(u),tn())},[u,c]),f.useEffect(()=>{let e=()=>m({});return document.addEventListener(e9,e),()=>document.removeEventListener(e9,e)},[]),(0,d.jsx)(eL.div,{...s,ref:v,style:{pointerEvents:E?_?"auto":"none":void 0,...e.style},onFocusCapture:e0(e.onFocusCapture,S.onFocusCapture),onBlurCapture:e0(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:e0(e.onPointerDownCapture,N.onPointerDownCapture)})});function tn(){let e=new CustomEvent(e9);document.dispatchEvent(e)}function tr(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?e$(o,i):o.dispatchEvent(i)}tt.displayName="DismissableLayer",f.forwardRef((e,t)=>{let n=f.useContext(te),r=f.useRef(null),o=g(t,r);return f.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,d.jsx)(eL.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var to=0;function ti(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ta="focusScope.autoFocusOnMount",tl="focusScope.autoFocusOnUnmount",ts={bubbles:!1,cancelable:!0},tc=f.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...a}=e,[l,s]=f.useState(null),c=ez(o),u=ez(i),h=f.useRef(null),p=g(t,e=>s(e)),m=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let e=function(e){if(m.paused||!l)return;let t=e.target;l.contains(t)?h.current=t:tf(h.current,{select:!0})},t=function(e){if(m.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||tf(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&tf(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,m.paused]),f.useEffect(()=>{if(l){th.add(m);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(ta,ts);l.addEventListener(ta,c),l.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(tf(r,{select:t}),document.activeElement!==n)return}(tu(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&tf(l))}return()=>{l.removeEventListener(ta,c),setTimeout(()=>{let t=new CustomEvent(tl,ts);l.addEventListener(tl,u),l.dispatchEvent(t),t.defaultPrevented||tf(e??document.body,{select:!0}),l.removeEventListener(tl,u),th.remove(m)},0)}}},[l,c,u,m]);let v=f.useCallback(e=>{if(!n&&!r||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=tu(e);return[td(t,e),td(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&tf(i,{select:!0})):(e.preventDefault(),n&&tf(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,m.paused]);return(0,d.jsx)(eL.div,{tabIndex:-1,...a,ref:p,onKeyDown:v})});function tu(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function td(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function tf(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}tc.displayName="FocusScope";var th=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=tp(e,t)).unshift(t)},remove(t){e=tp(e,t),e[0]?.resume()}}}();function tp(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var tm=h[" useId ".trim().toString()]||(()=>void 0),tg=0;function tv(e){let[t,n]=f.useState(tm());return eD(()=>{e||n(e=>e??String(tg++))},[e]),e||(t?`radix-${t}`:"")}let tx=["top","right","bottom","left"],ty=Math.min,tb=Math.max,tw=Math.round,tE=Math.floor,t_=e=>({x:e,y:e}),tN={left:"right",right:"left",bottom:"top",top:"bottom"},tS={start:"end",end:"start"};function tk(e,t){return"function"==typeof e?e(t):e}function tj(e){return e.split("-")[0]}function tM(e){return e.split("-")[1]}function tR(e){return"x"===e?"y":"x"}function tC(e){return"y"===e?"height":"width"}function tA(e){return["top","bottom"].includes(tj(e))?"y":"x"}function tP(e){return e.replace(/start|end/g,e=>tS[e])}function tT(e){return e.replace(/left|right|bottom|top/g,e=>tN[e])}function tO(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function tz(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function tD(e,t,n){let r,{reference:o,floating:i}=e,a=tA(t),l=tR(tA(t)),s=tC(l),c=tj(t),u="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,h=o[s]/2-i[s]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(tM(t)){case"start":r[l]-=h*(n&&u?-1:1);break;case"end":r[l]+=h*(n&&u?-1:1)}return r}let tI=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=tD(c,r,s),f=r,h={},p=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:g,y:v,data:x,reset:y}=await m({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:h,rects:c,platform:a,elements:{reference:e,floating:t}});u=null!=g?g:u,d=null!=v?v:d,h={...h,[i]:{...h[i],...x}},y&&p<=50&&(p++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(c=!0===y.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:u,y:d}=tD(c,f,s)),n=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:h}};async function tL(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=tk(t,e),p=tO(h),m=l[f?"floating"===d?"reference":"floating":d],g=tz(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:u,strategy:s})),v="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,x=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),y=await (null==i.isElement?void 0:i.isElement(x))&&await (null==i.getScale?void 0:i.getScale(x))||{x:1,y:1},b=tz(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:v,offsetParent:x,strategy:s}):v);return{top:(g.top-b.top+p.top)/y.y,bottom:(b.bottom-g.bottom+p.bottom)/y.y,left:(g.left-b.left+p.left)/y.x,right:(b.right-g.right+p.right)/y.x}}function t$(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function tH(e){return tx.some(t=>e[t]>=0)}async function tF(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=tj(n),l=tM(n),s="y"===tA(n),c=["left","top"].includes(a)?-1:1,u=i&&s?-1:1,d=tk(t,e),{mainAxis:f,crossAxis:h,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof p&&(h="end"===l?-1*p:p),s?{x:h*u,y:f*c}:{x:f*c,y:h*u}}function tB(){return"undefined"!=typeof window}function tV(e){return tX(e)?(e.nodeName||"").toLowerCase():"#document"}function tU(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function tW(e){var t;return null==(t=(tX(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function tX(e){return!!tB()&&(e instanceof Node||e instanceof tU(e).Node)}function tq(e){return!!tB()&&(e instanceof Element||e instanceof tU(e).Element)}function tK(e){return!!tB()&&(e instanceof HTMLElement||e instanceof tU(e).HTMLElement)}function tY(e){return!!tB()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof tU(e).ShadowRoot)}function tG(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=t1(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function tZ(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function tQ(e){let t=tJ(),n=tq(e)?t1(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function tJ(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function t0(e){return["html","body","#document"].includes(tV(e))}function t1(e){return tU(e).getComputedStyle(e)}function t2(e){return tq(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function t3(e){if("html"===tV(e))return e;let t=e.assignedSlot||e.parentNode||tY(e)&&e.host||tW(e);return tY(t)?t.host:t}function t4(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=t3(t);return t0(n)?t.ownerDocument?t.ownerDocument.body:t.body:tK(n)&&tG(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=tU(o);if(i){let e=t5(a);return t.concat(a,a.visualViewport||[],tG(o)?o:[],e&&n?t4(e):[])}return t.concat(o,t4(o,[],n))}function t5(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function t6(e){let t=t1(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=tK(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=tw(n)!==i||tw(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function t8(e){return tq(e)?e:e.contextElement}function t7(e){let t=t8(e);if(!tK(t))return t_(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=t6(t),a=(i?tw(n.width):n.width)/r,l=(i?tw(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let t9=t_(0);function ne(e){let t=tU(e);return tJ()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:t9}function nt(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=t8(e),l=t_(1);t&&(r?tq(r)&&(l=t7(r)):l=t7(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===tU(a))&&o)?ne(a):t_(0),c=(i.left+s.x)/l.x,u=(i.top+s.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=tU(a),t=r&&tq(r)?tU(r):r,n=e,o=t5(n);for(;o&&r&&t!==n;){let e=t7(o),t=o.getBoundingClientRect(),r=t1(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=a,o=t5(n=tU(o))}}return tz({width:d,height:f,x:c,y:u})}function nn(e,t){let n=t2(e).scrollLeft;return t?t.left+n:nt(tW(e)).left+n}function nr(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:nn(e,r)),y:r.top+t.scrollTop}}function no(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=tU(e),r=tW(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=tJ();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=tW(e),n=t2(e),r=e.ownerDocument.body,o=tb(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=tb(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+nn(e),l=-n.scrollTop;return"rtl"===t1(r).direction&&(a+=tb(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(tW(e));else if(tq(t))r=function(e,t){let n=nt(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=tK(e)?t7(e):t_(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ne(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return tz(r)}function ni(e){return"static"===t1(e).position}function na(e,t){if(!tK(e)||"fixed"===t1(e).position)return null;if(t)return t(e);let n=e.offsetParent;return tW(e)===n&&(n=n.ownerDocument.body),n}function nl(e,t){let n=tU(e);if(tZ(e))return n;if(!tK(e)){let t=t3(e);for(;t&&!t0(t);){if(tq(t)&&!ni(t))return t;t=t3(t)}return n}let r=na(e,t);for(;r&&["table","td","th"].includes(tV(r))&&ni(r);)r=na(r,t);return r&&t0(r)&&ni(r)&&!tQ(r)?n:r||function(e){let t=t3(e);for(;tK(t)&&!t0(t);){if(tQ(t))return t;if(tZ(t))break;t=t3(t)}return null}(e)||n}let ns=async function(e){let t=this.getOffsetParent||nl,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=tK(t),o=tW(t),i="fixed"===n,a=nt(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=t_(0);if(r||!r&&!i)if(("body"!==tV(t)||tG(o))&&(l=t2(t)),r){let e=nt(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=nn(o));i&&!r&&o&&(s.x=nn(o));let c=!o||r||i?t_(0):nr(o,l);return{x:a.left+l.scrollLeft-s.x-c.x,y:a.top+l.scrollTop-s.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},nc={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=tW(r),l=!!t&&tZ(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},c=t_(1),u=t_(0),d=tK(r);if((d||!d&&!i)&&(("body"!==tV(r)||tG(a))&&(s=t2(r)),tK(r))){let e=nt(r);c=t7(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}let f=!a||d||i?t_(0):nr(a,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:tW,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?tZ(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=t4(e,[],!1).filter(e=>tq(e)&&"body"!==tV(e)),o=null,i="fixed"===t1(e).position,a=i?t3(e):e;for(;tq(a)&&!t0(a);){let t=t1(a),n=tQ(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||tG(a)&&!n&&function e(t,n){let r=t3(t);return!(r===n||!tq(r)||t0(r))&&("fixed"===t1(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=t3(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=no(t,n,o);return e.top=tb(r.top,e.top),e.right=ty(r.right,e.right),e.bottom=ty(r.bottom,e.bottom),e.left=tb(r.left,e.left),e},no(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:nl,getElementRects:ns,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=t6(e);return{width:t,height:n}},getScale:t7,isElement:tq,isRTL:function(e){return"rtl"===t1(e).direction}};function nu(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let nd=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:c,padding:u=0}=tk(e,t)||{};if(null==c)return{};let d=tO(u),f={x:n,y:r},h=tR(tA(o)),p=tC(h),m=await a.getDimensions(c),g="y"===h,v=g?"clientHeight":"clientWidth",x=i.reference[p]+i.reference[h]-f[h]-i.floating[p],y=f[h]-i.reference[h],b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(c)),w=b?b[v]:0;w&&await (null==a.isElement?void 0:a.isElement(b))||(w=l.floating[v]||i.floating[p]);let E=w/2-m[p]/2-1,_=ty(d[g?"top":"left"],E),N=ty(d[g?"bottom":"right"],E),S=w-m[p]-N,k=w/2-m[p]/2+(x/2-y/2),j=tb(_,ty(k,S)),M=!s.arrow&&null!=tM(o)&&k!==j&&i.reference[p]/2-(k<_?_:N)-m[p]/2<0,R=M?k<_?k-_:k-S:0;return{[h]:f[h]+R,data:{[h]:j,centerOffset:k-j-R,...M&&{alignmentOffset:R}},reset:M}}}),nf=(e,t,n)=>{let r=new Map,o={platform:nc,...n},i={...o.platform,_c:r};return tI(e,t,{...o,platform:i})};var nh="undefined"!=typeof document?f.useLayoutEffect:function(){};function np(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!np(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!np(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function nm(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ng(e,t){let n=nm(e);return Math.round(t*n)/n}function nv(e){let t=f.useRef(e);return nh(()=>{t.current=e}),t}let nx=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?nd({element:n.current,padding:r}).fn(t):{}:n?nd({element:n,padding:r}).fn(t):{}}}),ny=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await tF(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),nb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=tk(e,t),c={x:n,y:r},u=await tL(t,s),d=tA(tj(o)),f=tR(d),h=c[f],p=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+u[e],r=h-u[t];h=tb(n,ty(h,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+u[e],r=p-u[t];p=tb(n,ty(p,r))}let m=l.fn({...t,[f]:h,[d]:p});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),nw=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:c=!0}=tk(e,t),u={x:n,y:r},d=tA(o),f=tR(d),h=u[f],p=u[d],m=tk(l,t),g="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+g.mainAxis,n=i.reference[f]+i.reference[e]-g.mainAxis;h<t?h=t:h>n&&(h=n)}if(c){var v,x;let e="y"===f?"width":"height",t=["top","left"].includes(tj(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(v=a.offset)?void 0:v[d])||0)+(t?0:g.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(x=a.offset)?void 0:x[d])||0)-(t?g.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[f]:h,[d]:p}}}}(e),options:[e,t]}),nE=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:h=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:x=!0,...y}=tk(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let b=tj(l),w=tA(u),E=tj(u)===u,_=await (null==d.isRTL?void 0:d.isRTL(f.floating)),N=m||(E||!x?[tT(u)]:function(e){let t=tT(e);return[tP(e),t,tP(t)]}(u)),S="none"!==v;!m&&S&&N.push(...function(e,t,n,r){let o=tM(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(tj(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(tP)))),i}(u,x,v,_));let k=[u,...N],j=await tL(t,y),M=[],R=(null==(r=s.flip)?void 0:r.overflows)||[];if(h&&M.push(j[b]),p){let e=function(e,t,n){void 0===n&&(n=!1);let r=tM(e),o=tR(tA(e)),i=tC(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=tT(a)),[a,tT(a)]}(l,c,_);M.push(j[e[0]],j[e[1]])}if(R=[...R,{placement:l,overflows:M}],!M.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=k[e];if(t&&("alignment"!==p||w===tA(t)||R.every(e=>e.overflows[0]>0&&tA(e.placement)===w)))return{data:{index:e,overflows:R},reset:{placement:t}};let n=null==(i=R.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(g){case"bestFit":{let e=null==(a=R.filter(e=>{if(S){let t=tA(e.placement);return t===w||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=u}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),n_=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:s,elements:c}=t,{apply:u=()=>{},...d}=tk(e,t),f=await tL(t,d),h=tj(a),p=tM(a),m="y"===tA(a),{width:g,height:v}=l.floating;"top"===h||"bottom"===h?(o=h,i=p===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(i=h,o="end"===p?"top":"bottom");let x=v-f.top-f.bottom,y=g-f.left-f.right,b=ty(v-f[o],x),w=ty(g-f[i],y),E=!t.middlewareData.shift,_=b,N=w;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=y),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(_=x),E&&!p){let e=tb(f.left,0),t=tb(f.right,0),n=tb(f.top,0),r=tb(f.bottom,0);m?N=g-2*(0!==e||0!==t?e+t:tb(f.left,f.right)):_=v-2*(0!==n||0!==r?n+r:tb(f.top,f.bottom))}await u({...t,availableWidth:N,availableHeight:_});let S=await s.getDimensions(c.floating);return g!==S.width||v!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),nN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=tk(e,t);switch(r){case"referenceHidden":{let e=t$(await tL(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:tH(e)}}}case"escaped":{let e=t$(await tL(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:tH(e)}}}default:return{}}}}}(e),options:[e,t]}),nS=(e,t)=>({...nx(e),options:[e,t]});var nk=f.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,d.jsx)(eL.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});nk.displayName="Arrow";var nj="Popper",[nM,nR]=eO(nj),[nC,nA]=nM(nj),nP=e=>{let{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return(0,d.jsx)(nC,{scope:t,anchor:r,onAnchorChange:o,children:n})};nP.displayName=nj;var nT="PopperAnchor",nO=f.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=nA(nT,n),a=f.useRef(null),l=g(t,a);return f.useEffect(()=>{i.onAnchorChange(r?.current||a.current)}),r?null:(0,d.jsx)(eL.div,{...o,ref:l})});nO.displayName=nT;var nz="PopperContent",[nD,nI]=nM(nz),nL=f.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:a=0,arrowPadding:l=0,avoidCollisions:s=!0,collisionBoundary:c=[],collisionPadding:u=0,sticky:h="partial",hideWhenDetached:p=!1,updatePositionStrategy:m="optimized",onPlaced:v,...x}=e,y=nA(nz,n),[b,w]=f.useState(null),E=g(t,e=>w(e)),[_,N]=f.useState(null),S=function(e){let[t,n]=f.useState(void 0);return eD(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(_),k=S?.width??0,j=S?.height??0,M="number"==typeof u?u:{top:0,right:0,bottom:0,left:0,...u},R=Array.isArray(c)?c:[c],C=R.length>0,A={padding:M,boundary:R.filter(nB),altBoundary:C},{refs:P,floatingStyles:T,placement:O,isPositioned:z,middlewareData:D}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:a}={},transform:l=!0,whileElementsMounted:s,open:c}=e,[u,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=f.useState(r);np(h,r)||p(r);let[m,g]=f.useState(null),[v,x]=f.useState(null),y=f.useCallback(e=>{e!==_.current&&(_.current=e,g(e))},[]),b=f.useCallback(e=>{e!==N.current&&(N.current=e,x(e))},[]),w=i||m,E=a||v,_=f.useRef(null),N=f.useRef(null),S=f.useRef(u),k=null!=s,j=nv(s),M=nv(o),R=nv(c),C=f.useCallback(()=>{if(!_.current||!N.current)return;let e={placement:t,strategy:n,middleware:h};M.current&&(e.platform=M.current),nf(_.current,N.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};A.current&&!np(S.current,t)&&(S.current=t,eI.flushSync(()=>{d(t)}))})},[h,t,n,M,R]);nh(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let A=f.useRef(!1);nh(()=>(A.current=!0,()=>{A.current=!1}),[]),nh(()=>{if(w&&(_.current=w),E&&(N.current=E),w&&E){if(j.current)return j.current(w,E,C);C()}},[w,E,C,j,k]);let P=f.useMemo(()=>({reference:_,floating:N,setReference:y,setFloating:b}),[y,b]),T=f.useMemo(()=>({reference:w,floating:E}),[w,E]),O=f.useMemo(()=>{let e={position:n,left:0,top:0};if(!T.floating)return e;let t=ng(T.floating,u.x),r=ng(T.floating,u.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...nm(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,T.floating,u.x,u.y]);return f.useMemo(()=>({...u,update:C,refs:P,elements:T,floatingStyles:O}),[u,C,P,T,O])}({strategy:"fixed",placement:r+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,u=t8(e),d=i||a?[...u?t4(u):[],...t4(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=u&&s?function(e,t){let n,r=null,o=tW(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:h}=c;if(l||t(),!f||!h)return;let p=tE(d),m=tE(o.clientWidth-(u+f)),g={rootMargin:-p+"px "+-m+"px "+-tE(o.clientHeight-(d+h))+"px "+-tE(u)+"px",threshold:tb(0,ty(1,s))||1},v=!0;function x(t){let r=t[0].intersectionRatio;if(r!==s){if(!v)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||nu(c,e.getBoundingClientRect())||a(),v=!1}try{r=new IntersectionObserver(x,{...g,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,g)}r.observe(e)}(!0),i}(u,n):null,h=-1,p=null;l&&(p=new ResizeObserver(e=>{let[r]=e;r&&r.target===u&&p&&(p.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),n()}),u&&!c&&p.observe(u),p.observe(t));let m=c?nt(e):null;return c&&function t(){let r=nt(e);m&&!nu(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=p)||e.disconnect(),p=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===m}),elements:{reference:y.anchor},middleware:[ny({mainAxis:o+j,alignmentAxis:a}),s&&nb({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?nw():void 0,...A}),s&&nE({...A}),n_({...A,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),_&&nS({element:_,padding:l}),nV({arrowWidth:k,arrowHeight:j}),p&&nN({strategy:"referenceHidden",...A})]}),[I,L]=nU(O),$=ez(v);eD(()=>{z&&$?.()},[z,$]);let H=D.arrow?.x,F=D.arrow?.y,B=D.arrow?.centerOffset!==0,[V,U]=f.useState();return eD(()=>{b&&U(window.getComputedStyle(b).zIndex)},[b]),(0,d.jsx)("div",{ref:P.setFloating,"data-radix-popper-content-wrapper":"",style:{...T,transform:z?T.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:V,"--radix-popper-transform-origin":[D.transformOrigin?.x,D.transformOrigin?.y].join(" "),...D.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(nD,{scope:n,placedSide:I,onArrowChange:N,arrowX:H,arrowY:F,shouldHideArrow:B,children:(0,d.jsx)(eL.div,{"data-side":I,"data-align":L,...x,ref:E,style:{...x.style,animation:z?void 0:"none"}})})})});nL.displayName=nz;var n$="PopperArrow",nH={top:"bottom",right:"left",bottom:"top",left:"right"},nF=f.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=nI(n$,n),i=nH[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(nk,{...r,ref:t,style:{...r.style,display:"block"}})})});function nB(e){return null!==e}nF.displayName=n$;var nV=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[s,c]=nU(n),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,h="",p="";return"bottom"===s?(h=i?u:`${d}px`,p=`${-l}px`):"top"===s?(h=i?u:`${d}px`,p=`${r.floating.height+l}px`):"right"===s?(h=`${-l}px`,p=i?u:`${f}px`):"left"===s&&(h=`${r.floating.width+l}px`,p=i?u:`${f}px`),{data:{x:h,y:p}}}});function nU(e){let[t,n="center"]=e.split("-");return[t,n]}var nW=f.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=f.useState(!1);eD(()=>i(!0),[]);let a=n||o&&globalThis?.document?.body;return a?eI.createPortal((0,d.jsx)(eL.div,{...r,ref:t}),a):null});nW.displayName="Portal";var nX=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=f.useState(),i=f.useRef(null),a=f.useRef(e),l=f.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},f.useReducer((e,t)=>n[e][t]??e,t));return f.useEffect(()=>{let e=nq(i.current);l.current="mounted"===s?e:"none"},[s]),eD(()=>{let t=i.current,n=a.current;if(n!==e){let r=l.current,o=nq(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),eD(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,n=n=>{let o=nq(i.current).includes(n.animationName);if(n.target===r&&o&&(c("ANIMATION_END"),!a.current)){let n=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=n)})}},o=e=>{e.target===r&&(l.current=nq(i.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",n),r.addEventListener("animationend",n),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",n),r.removeEventListener("animationend",n)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:f.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):f.Children.only(n),i=g(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?f.cloneElement(o,{ref:i}):null};function nq(e){return e?.animationName||"none"}nX.displayName="Presence";var nK="rovingFocusGroup.onEntryFocus",nY={bubbles:!1,cancelable:!0},nG="RovingFocusGroup",[nZ,nQ,nJ]=e3(nG),[n0,n1]=eO(nG,[nJ]),[n2,n3]=n0(nG),n4=f.forwardRef((e,t)=>(0,d.jsx)(nZ.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(nZ.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(n5,{...e,ref:t})})}));n4.displayName=nG;var n5=f.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:a,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:s,onEntryFocus:c,preventScrollOnEntryFocus:u=!1,...h}=e,p=f.useRef(null),m=g(t,p),v=e7(i),[x,y]=e2({prop:a,defaultProp:l??null,onChange:s,caller:nG}),[b,w]=f.useState(!1),E=ez(c),_=nQ(n),N=f.useRef(!1),[S,k]=f.useState(0);return f.useEffect(()=>{let e=p.current;if(e)return e.addEventListener(nK,E),()=>e.removeEventListener(nK,E)},[E]),(0,d.jsx)(n2,{scope:n,orientation:r,dir:v,loop:o,currentTabStopId:x,onItemFocus:f.useCallback(e=>y(e),[y]),onItemShiftTab:f.useCallback(()=>w(!0),[]),onFocusableItemAdd:f.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:f.useCallback(()=>k(e=>e-1),[]),children:(0,d.jsx)(eL.div,{tabIndex:b||0===S?-1:0,"data-orientation":r,...h,ref:m,style:{outline:"none",...e.style},onMouseDown:e0(e.onMouseDown,()=>{N.current=!0}),onFocus:e0(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!b){let t=new CustomEvent(nK,nY);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);n9([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),u)}}N.current=!1}),onBlur:e0(e.onBlur,()=>w(!1))})})}),n6="RovingFocusGroupItem",n8=f.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i,children:a,...l}=e,s=tv(),c=i||s,u=n3(n6,n),h=u.currentTabStopId===c,p=nQ(n),{onFocusableItemAdd:m,onFocusableItemRemove:g,currentTabStopId:v}=u;return f.useEffect(()=>{if(r)return m(),()=>g()},[r,m,g]),(0,d.jsx)(nZ.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:(0,d.jsx)(eL.span,{tabIndex:h?0:-1,"data-orientation":u.orientation,...l,ref:t,onMouseDown:e0(e.onMouseDown,e=>{r?u.onItemFocus(c):e.preventDefault()}),onFocus:e0(e.onFocus,()=>u.onItemFocus(c)),onKeyDown:e0(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void u.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return n7[o]}(e,u.orientation,u.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=p().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=u.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>n9(n))}}),children:"function"==typeof a?a({isCurrentTabStop:h,hasTabStop:null!=v}):a})})});n8.displayName=n6;var n7={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function n9(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var re=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},rt=new WeakMap,rn=new WeakMap,rr={},ro=0,ri=function(e){return e&&(e.host||ri(e.parentNode))},ra=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=ri(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});rr[n]||(rr[n]=new WeakMap);var i=rr[n],a=[],l=new Set,s=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(rt.get(e)||0)+1,c=(i.get(e)||0)+1;rt.set(e,s),i.set(e,c),a.push(e),1===s&&o&&rn.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return u(t),l.clear(),ro++,function(){a.forEach(function(e){var t=rt.get(e)-1,o=i.get(e)-1;rt.set(e,t),i.set(e,o),t||(rn.has(e)||e.removeAttribute(r),rn.delete(e)),o||e.removeAttribute(n)}),--ro||(rt=new WeakMap,rt=new WeakMap,rn=new WeakMap,rr={})}},rl=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||re(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),ra(r,o,n,"aria-hidden")):function(){return null}},rs=function(){return(rs=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function rc(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var ru=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),rd="width-before-scroll-bar";function rf(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var rh="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,rp=new WeakMap;function rm(e){return e}var rg=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=rm),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return i.options=rs({async:!0,ssr:!1},e),i}(),rv=function(){},rx=f.forwardRef(function(e,t){var n,r,o,i,a=f.useRef(null),l=f.useState({onScrollCapture:rv,onWheelCapture:rv,onTouchMoveCapture:rv}),s=l[0],c=l[1],u=e.forwardProps,d=e.children,h=e.className,p=e.removeScrollBar,m=e.enabled,g=e.shards,v=e.sideCar,x=e.noRelative,y=e.noIsolation,b=e.inert,w=e.allowPinchZoom,E=e.as,_=e.gapMode,N=rc(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[a,t],r=function(e){return n.forEach(function(t){return rf(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,rh(function(){var e=rp.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||rf(e,null)}),r.forEach(function(e){t.has(e)||rf(e,o)})}rp.set(i,n)},[n]),i),k=rs(rs({},N),s);return f.createElement(f.Fragment,null,m&&f.createElement(v,{sideCar:rg,removeScrollBar:p,shards:g,noRelative:x,noIsolation:y,inert:b,setCallbacks:c,allowPinchZoom:!!w,lockRef:a,gapMode:_}),u?f.cloneElement(f.Children.only(d),rs(rs({},k),{ref:S})):f.createElement(void 0===E?"div":E,rs({},k,{className:h,ref:S}),d))});rx.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},rx.classNames={fullWidth:rd,zeroRight:ru};var ry=function(e){var t=e.sideCar,n=rc(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,rs({},n))};ry.isSideCarExport=!0;var rb=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=lH||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},rw=function(){var e=rb();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},rE=function(){var e=rw();return function(t){return e(t.styles,t.dynamic),null}},r_={left:0,top:0,right:0,gap:0},rN=function(e){return parseInt(e||"",10)||0},rS=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[rN(n),rN(r),rN(o)]},rk=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return r_;var t=rS(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},rj=rE(),rM="data-scroll-locked",rR=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(rM,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(ru," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(rd," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(ru," .").concat(ru," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(rd," .").concat(rd," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(rM,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},rC=function(){var e=parseInt(document.body.getAttribute(rM)||"0",10);return isFinite(e)?e:0},rA=function(){f.useEffect(function(){return document.body.setAttribute(rM,(rC()+1).toString()),function(){var e=rC()-1;e<=0?document.body.removeAttribute(rM):document.body.setAttribute(rM,e.toString())}},[])},rP=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;rA();var i=f.useMemo(function(){return rk(o)},[o]);return f.createElement(rj,{styles:rR(i,!t,o,n?"":"!important")})},rT=!1;if("undefined"!=typeof window)try{var rO=Object.defineProperty({},"passive",{get:function(){return rT=!0,!0}});window.addEventListener("test",rO,rO),window.removeEventListener("test",rO,rO)}catch(e){rT=!1}var rz=!!rT&&{passive:!1},rD=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},rI=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),rL(e,r)){var o=r$(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},rL=function(e,t){return"v"===e?rD(t,"overflowY"):rD(t,"overflowX")},r$=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},rH=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,c=t.contains(s),u=!1,d=l>0,f=0,h=0;do{if(!s)break;var p=r$(e,s),m=p[0],g=p[1]-p[2]-a*m;(m||g)&&rL(e,s)&&(f+=g,h+=m);var v=s.parentNode;s=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?u=!0:!d&&(o&&1>Math.abs(h)||!o&&-l>h)&&(u=!0),u},rF=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},rB=function(e){return[e.deltaX,e.deltaY]},rV=function(e){return e&&"current"in e?e.current:e},rU=0,rW=[];let rX=(lL=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(rU++)[0],i=f.useState(rE)[0],a=f.useRef(e);f.useEffect(function(){a.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(rV),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=rF(e),l=n.current,s="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=rI(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=rI(d,u)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||c)&&(r.current=o),!o)return!0;var h=r.current||o;return rH(h,t,e,"h"===h?s:c,!0)},[]),s=f.useCallback(function(e){if(rW.length&&rW[rW.length-1]===i){var n="deltaY"in e?rB(e):rF(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(rV).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),u=f.useCallback(function(e){n.current=rF(e),r.current=void 0},[]),d=f.useCallback(function(t){c(t.type,rB(t),t.target,l(t,e.lockRef.current))},[]),h=f.useCallback(function(t){c(t.type,rF(t),t.target,l(t,e.lockRef.current))},[]);f.useEffect(function(){return rW.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:h}),document.addEventListener("wheel",s,rz),document.addEventListener("touchmove",s,rz),document.addEventListener("touchstart",u,rz),function(){rW=rW.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,rz),document.removeEventListener("touchmove",s,rz),document.removeEventListener("touchstart",u,rz)}},[]);var p=e.removeScrollBar,m=e.inert;return f.createElement(f.Fragment,null,m?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?f.createElement(rP,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},rg.useMedium(lL),ry);var rq=f.forwardRef(function(e,t){return f.createElement(rx,rs({},e,{ref:t,sideCar:rX}))});rq.classNames=rx.classNames;var rK=["Enter"," "],rY=["ArrowUp","PageDown","End"],rG=["ArrowDown","PageUp","Home",...rY],rZ={ltr:[...rK,"ArrowRight"],rtl:[...rK,"ArrowLeft"]},rQ={ltr:["ArrowLeft"],rtl:["ArrowRight"]},rJ="Menu",[r0,r1,r2]=e3(rJ),[r3,r4]=eO(rJ,[r2,nR,n1]),r5=nR(),r6=n1(),[r8,r7]=r3(rJ),[r9,oe]=r3(rJ),ot=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:a=!0}=e,l=r5(t),[s,c]=f.useState(null),u=f.useRef(!1),h=ez(i),p=e7(o);return f.useEffect(()=>{let e=()=>{u.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>u.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,d.jsx)(nP,{...l,children:(0,d.jsx)(r8,{scope:t,open:n,onOpenChange:h,content:s,onContentChange:c,children:(0,d.jsx)(r9,{scope:t,onClose:f.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:u,dir:p,modal:a,children:r})})})};ot.displayName=rJ;var on=f.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=r5(n);return(0,d.jsx)(nO,{...o,...r,ref:t})});on.displayName="MenuAnchor";var or="MenuPortal",[oo,oi]=r3(or,{forceMount:void 0}),oa=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=r7(or,t);return(0,d.jsx)(oo,{scope:t,forceMount:n,children:(0,d.jsx)(nX,{present:n||i.open,children:(0,d.jsx)(nW,{asChild:!0,container:o,children:r})})})};oa.displayName=or;var ol="MenuContent",[os,oc]=r3(ol),ou=f.forwardRef((e,t)=>{let n=oi(ol,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=r7(ol,e.__scopeMenu),a=oe(ol,e.__scopeMenu);return(0,d.jsx)(r0.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(nX,{present:r||i.open,children:(0,d.jsx)(r0.Slot,{scope:e.__scopeMenu,children:a.modal?(0,d.jsx)(od,{...o,ref:t}):(0,d.jsx)(of,{...o,ref:t})})})})}),od=f.forwardRef((e,t)=>{let n=r7(ol,e.__scopeMenu),r=f.useRef(null),o=g(t,r);return f.useEffect(()=>{let e=r.current;if(e)return rl(e)},[]),(0,d.jsx)(op,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:e0(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),of=f.forwardRef((e,t)=>{let n=r7(ol,e.__scopeMenu);return(0,d.jsx)(op,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),oh=v("MenuContent.ScrollLock"),op=f.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEntryFocus:s,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:p,onDismiss:m,disableOutsideScroll:v,...x}=e,y=r7(ol,n),b=oe(ol,n),w=r5(n),E=r6(n),_=r1(n),[N,S]=f.useState(null),k=f.useRef(null),j=g(t,k,y.onContentChange),M=f.useRef(0),R=f.useRef(""),C=f.useRef(0),A=f.useRef(null),P=f.useRef("right"),T=f.useRef(0),O=v?rq:f.Fragment,z=e=>{let t=R.current+e,n=_().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){R.current=t,window.clearTimeout(M.current),""!==t&&(M.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};f.useEffect(()=>()=>window.clearTimeout(M.current),[]),f.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ti()),document.body.insertAdjacentElement("beforeend",e[1]??ti()),to++,()=>{1===to&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),to--}},[]);let D=f.useCallback(e=>P.current===A.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],s=a.x,c=a.y,u=l.x,d=l.y;c>r!=d>r&&n<(u-s)*(r-c)/(d-c)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,A.current?.area),[]);return(0,d.jsx)(os,{scope:n,searchRef:R,onItemEnter:f.useCallback(e=>{D(e)&&e.preventDefault()},[D]),onItemLeave:f.useCallback(e=>{D(e)||(k.current?.focus(),S(null))},[D]),onTriggerLeave:f.useCallback(e=>{D(e)&&e.preventDefault()},[D]),pointerGraceTimerRef:C,onPointerGraceIntentChange:f.useCallback(e=>{A.current=e},[]),children:(0,d.jsx)(O,{...v?{as:oh,allowPinchZoom:!0}:void 0,children:(0,d.jsx)(tc,{asChild:!0,trapped:o,onMountAutoFocus:e0(i,e=>{e.preventDefault(),k.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:(0,d.jsx)(tt,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:p,onDismiss:m,children:(0,d.jsx)(n4,{asChild:!0,...E,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:N,onCurrentTabStopIdChange:S,onEntryFocus:e0(s,e=>{b.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,d.jsx)(nL,{role:"menu","aria-orientation":"vertical","data-state":oH(y.open),"data-radix-menu-content":"",dir:b.dir,...w,...x,ref:j,style:{outline:"none",...x.style},onKeyDown:e0(x.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&z(e.key));let o=k.current;if(e.target!==o||!rG.includes(e.key))return;e.preventDefault();let i=_().filter(e=>!e.disabled).map(e=>e.ref.current);rY.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:e0(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(M.current),R.current="")}),onPointerMove:e0(e.onPointerMove,oV(e=>{let t=e.target,n=T.current!==e.clientX;e.currentTarget.contains(t)&&n&&(P.current=e.clientX>T.current?"right":"left",T.current=e.clientX)}))})})})})})})});ou.displayName=ol;var om=f.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,d.jsx)(eL.div,{role:"group",...r,ref:t})});om.displayName="MenuGroup";var og=f.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,d.jsx)(eL.div,{...r,ref:t})});og.displayName="MenuLabel";var ov="MenuItem",ox="menu.itemSelect",oy=f.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,i=f.useRef(null),a=oe(ov,e.__scopeMenu),l=oc(ov,e.__scopeMenu),s=g(t,i),c=f.useRef(!1);return(0,d.jsx)(ob,{...o,ref:s,disabled:n,onClick:e0(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(ox,{bubbles:!0,cancelable:!0});e.addEventListener(ox,e=>r?.(e),{once:!0}),e$(e,t),t.defaultPrevented?c.current=!1:a.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),c.current=!0},onPointerUp:e0(e.onPointerUp,e=>{c.current||e.currentTarget?.click()}),onKeyDown:e0(e.onKeyDown,e=>{let t=""!==l.searchRef.current;n||t&&" "===e.key||rK.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});oy.displayName=ov;var ob=f.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,a=oc(ov,n),l=r6(n),s=f.useRef(null),c=g(t,s),[u,h]=f.useState(!1),[p,m]=f.useState("");return f.useEffect(()=>{let e=s.current;e&&m((e.textContent??"").trim())},[i.children]),(0,d.jsx)(r0.ItemSlot,{scope:n,disabled:r,textValue:o??p,children:(0,d.jsx)(n8,{asChild:!0,...l,focusable:!r,children:(0,d.jsx)(eL.div,{role:"menuitem","data-highlighted":u?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:c,onPointerMove:e0(e.onPointerMove,oV(e=>{r?a.onItemLeave(e):(a.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:e0(e.onPointerLeave,oV(e=>a.onItemLeave(e))),onFocus:e0(e.onFocus,()=>h(!0)),onBlur:e0(e.onBlur,()=>h(!1))})})})}),ow=f.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,d.jsx)(oR,{scope:e.__scopeMenu,checked:n,children:(0,d.jsx)(oy,{role:"menuitemcheckbox","aria-checked":oF(n)?"mixed":n,...o,ref:t,"data-state":oB(n),onSelect:e0(o.onSelect,()=>r?.(!!oF(n)||!n),{checkForDefaultPrevented:!1})})})});ow.displayName="MenuCheckboxItem";var oE="MenuRadioGroup",[o_,oN]=r3(oE,{value:void 0,onValueChange:()=>{}}),oS=f.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=ez(r);return(0,d.jsx)(o_,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,d.jsx)(om,{...o,ref:t})})});oS.displayName=oE;var ok="MenuRadioItem",oj=f.forwardRef((e,t)=>{let{value:n,...r}=e,o=oN(ok,e.__scopeMenu),i=n===o.value;return(0,d.jsx)(oR,{scope:e.__scopeMenu,checked:i,children:(0,d.jsx)(oy,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":oB(i),onSelect:e0(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});oj.displayName=ok;var oM="MenuItemIndicator",[oR,oC]=r3(oM,{checked:!1}),oA=f.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=oC(oM,n);return(0,d.jsx)(nX,{present:r||oF(i.checked)||!0===i.checked,children:(0,d.jsx)(eL.span,{...o,ref:t,"data-state":oB(i.checked)})})});oA.displayName=oM;var oP=f.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,d.jsx)(eL.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});oP.displayName="MenuSeparator";var oT=f.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=r5(n);return(0,d.jsx)(nF,{...o,...r,ref:t})});oT.displayName="MenuArrow";var[oO,oz]=r3("MenuSub"),oD="MenuSubTrigger",oI=f.forwardRef((e,t)=>{let n=r7(oD,e.__scopeMenu),r=oe(oD,e.__scopeMenu),o=oz(oD,e.__scopeMenu),i=oc(oD,e.__scopeMenu),a=f.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:s}=i,c={__scopeMenu:e.__scopeMenu},u=f.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return f.useEffect(()=>u,[u]),f.useEffect(()=>{let e=l.current;return()=>{window.clearTimeout(e),s(null)}},[l,s]),(0,d.jsx)(on,{asChild:!0,...c,children:(0,d.jsx)(ob,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":oH(n.open),...e,ref:m(t,o.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:e0(e.onPointerMove,oV(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||a.current||(i.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),u()},100)))})),onPointerLeave:e0(e.onPointerLeave,oV(e=>{u();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,a=t[o?"left":"right"],s=t[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:s,y:t.top},{x:s,y:t.bottom},{x:a,y:t.bottom}],side:r}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:e0(e.onKeyDown,t=>{let o=""!==i.searchRef.current;e.disabled||o&&" "===t.key||rZ[r.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});oI.displayName=oD;var oL="MenuSubContent",o$=f.forwardRef((e,t)=>{let n=oi(ol,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=r7(ol,e.__scopeMenu),a=oe(ol,e.__scopeMenu),l=oz(oL,e.__scopeMenu),s=f.useRef(null),c=g(t,s);return(0,d.jsx)(r0.Provider,{scope:e.__scopeMenu,children:(0,d.jsx)(nX,{present:r||i.open,children:(0,d.jsx)(r0.Slot,{scope:e.__scopeMenu,children:(0,d.jsx)(op,{id:l.contentId,"aria-labelledby":l.triggerId,...o,ref:c,align:"start",side:"rtl"===a.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{a.isUsingKeyboardRef.current&&s.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:e0(e.onFocusOutside,e=>{e.target!==l.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:e0(e.onEscapeKeyDown,e=>{a.onClose(),e.preventDefault()}),onKeyDown:e0(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=rQ[a.dir].includes(e.key);t&&n&&(i.onOpenChange(!1),l.trigger?.focus(),e.preventDefault())})})})})})});function oH(e){return e?"open":"closed"}function oF(e){return"indeterminate"===e}function oB(e){return oF(e)?"indeterminate":e?"checked":"unchecked"}function oV(e){return t=>"mouse"===t.pointerType?e(t):void 0}o$.displayName=oL;var oU="DropdownMenu",[oW,oX]=eO(oU,[r4]),oq=r4(),[oK,oY]=oW(oU),oG=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:a,modal:l=!0}=e,s=oq(t),c=f.useRef(null),[u,h]=e2({prop:o,defaultProp:i??!1,onChange:a,caller:oU});return(0,d.jsx)(oK,{scope:t,triggerId:tv(),triggerRef:c,contentId:tv(),open:u,onOpenChange:h,onOpenToggle:f.useCallback(()=>h(e=>!e),[h]),modal:l,children:(0,d.jsx)(ot,{...s,open:u,onOpenChange:h,dir:r,modal:l,children:n})})};oG.displayName=oU;var oZ="DropdownMenuTrigger",oQ=f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=oY(oZ,n),a=oq(n);return(0,d.jsx)(on,{asChild:!0,...a,children:(0,d.jsx)(eL.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:m(t,i.triggerRef),onPointerDown:e0(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:e0(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});oQ.displayName=oZ;var oJ=e=>{let{__scopeDropdownMenu:t,...n}=e,r=oq(t);return(0,d.jsx)(oa,{...r,...n})};oJ.displayName="DropdownMenuPortal";var o0="DropdownMenuContent",o1=f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oY(o0,n),i=oq(n),a=f.useRef(!1);return(0,d.jsx)(ou,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:e0(e.onCloseAutoFocus,e=>{a.current||o.triggerRef.current?.focus(),a.current=!1,e.preventDefault()}),onInteractOutside:e0(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});o1.displayName=o0,f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(om,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(og,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var o2=f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oy,{...o,...r,ref:t})});o2.displayName="DropdownMenuItem",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(ow,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oS,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oj,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oA,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var o3=f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oP,{...o,...r,ref:t})});function o4({...e}){return(0,d.jsx)(oG,{"data-slot":"dropdown-menu",...e})}function o5({...e}){return(0,d.jsx)(oQ,{"data-slot":"dropdown-menu-trigger",...e})}function o6({className:e,sideOffset:t=4,...n}){return(0,d.jsx)(oJ,{children:(0,d.jsx)(o1,{"data-slot":"dropdown-menu-content",sideOffset:t,className:eA("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n})})}function o8({className:e,inset:t,variant:n="default",...r}){return(0,d.jsx)(o2,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:eA("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function o7({className:e,...t}){return(0,d.jsx)(o3,{"data-slot":"dropdown-menu-separator",className:eA("bg-border -mx-1 my-1 h-px",e),...t})}o3.displayName="DropdownMenuSeparator",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oT,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(oI,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",f.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=oq(n);return(0,d.jsx)(o$,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";let o9=_("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function ie({className:e,variant:t,asChild:n=!1,...r}){return(0,d.jsx)(n?x:"span",{"data-slot":"badge",className:eA(o9({variant:t}),e),...r})}let it=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ir=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),io=e=>{let t=ir(e);return t.charAt(0).toUpperCase()+t.slice(1)},ii=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),ia=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var il={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let is=(0,f.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:a,...l},s)=>(0,f.createElement)("svg",{ref:s,...il,width:t,height:t,stroke:e,strokeWidth:r?24*Number(n)/Number(t):n,className:ii("lucide",o),...!i&&!ia(l)&&{"aria-hidden":"true"},...l},[...a.map(([e,t])=>(0,f.createElement)(e,t)),...Array.isArray(i)?i:[i]])),ic=(e,t)=>{let n=(0,f.forwardRef)(({className:n,...r},o)=>(0,f.createElement)(is,{ref:o,iconNode:t,className:ii(`lucide-${it(io(e))}`,`lucide-${e}`,n),...r}));return n.displayName=io(e),n},iu=ic("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),id=ic("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ih=ic("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),ip=ic("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),im=ic("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),ig=ic("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),iv=ic("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),ix=ic("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),iy=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},ib=e=>e?iy(e):iy,iw=e=>e,iE=e=>{let t=ib(e),n=e=>(function(e,t=iw){let n=f.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return f.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},i_=(e=>e?iE(e):iE)((e,t)=>({currentApp:{id:"demo-app",name:"订单运输调度系统",icon:"\uD83D\uDE9B",status:"development",version:"v0.1.0"},leftPanel:{activeTab:"chat",isDocumentEditing:!1},canvas:{mode:"flow",flowData:null},chat:{messages:[{id:"1",type:"assistant",content:"你好！我是 ideaFlow AI 助手。我可以帮助你将自然语言描述的工作流程转化为可执行的业务应用。\n\n当前项目：订单运输调度系统\n\n你可以：\n- 描述你的业务流程需求\n- 上传相关文档或数据文件\n- 让我帮你构建和优化工作流程",timestamp:new Date}],isLoading:!1},files:[{id:"main-doc",name:"项目需求文档.md",type:"document",content:`# 订单运输调度系统需求文档

## 项目概述
本系统旨在根据用户上传的订单明细、物料信息、车辆信息，将销售订单中的物料合理分配给车辆进行配送，实现智能化的运输调度。

## 核心功能

### 1. 数据输入
- 订单明细：包含客户信息、物料需求、交货日期等
- 物料信息：物料规格、重量、体积等属性
- 车辆信息：车辆载重、容积、可用时间等

### 2. 智能调度
- 根据物料属性和车辆容量进行最优匹配
- 考虑交货日期进行合理的时间安排
- 优化运输路线，降低成本

### 3. 结果输出
- 生成详细的配送计划
- 显示每辆车的装载清单
- 提供时间安排和路线建议

## 业务流程
1. 用户上传基础数据文件
2. 系统解析和验证数据
3. 执行智能调度算法
4. 生成配送方案
5. 用户确认并导出结果

## 技术要求
- 支持 Excel 文件导入
- 提供可视化的流程设计界面
- 实时预览调度结果
- 支持方案调整和优化
`,lastModified:new Date}],updateAppInfo:t=>e(e=>({currentApp:{...e.currentApp,...t}})),setLeftPanelTab:t=>e(e=>({leftPanel:{...e.leftPanel,activeTab:t}})),setDocumentEditing:t=>e(e=>({leftPanel:{...e.leftPanel,isDocumentEditing:t}})),setCanvasMode:t=>e(e=>({canvas:{...e.canvas,mode:t}})),updateFlowData:t=>e(e=>({canvas:{...e.canvas,flowData:t}})),addMessage:t=>e(e=>({chat:{...e.chat,messages:[...e.chat.messages,{...t,id:Date.now().toString(),timestamp:new Date}]}})),setLoading:t=>e(e=>({chat:{...e.chat,isLoading:t}})),addFile:t=>e(e=>({files:[...e.files,{...t,id:Date.now().toString(),lastModified:new Date}]})),updateFile:(t,n)=>e(e=>({files:e.files.map(e=>e.id===t?{...e,content:n,lastModified:new Date}:e)})),deleteFile:t=>e(e=>({files:e.files.filter(e=>e.id!==t)}))}));function iN(){let{currentApp:e,updateAppInfo:t}=i_();return(0,d.jsxs)("header",{className:"h-16 border-b bg-white flex items-center justify-between px-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)("div",{className:"text-2xl font-bold",children:["idea",(0,d.jsx)("span",{className:"text-blue-600",children:"Flow"})]})}),(0,d.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(eQ,{className:"h-8 w-8",children:(0,d.jsx)(eJ,{className:"text-lg",children:e.icon})}),(0,d.jsx)("div",{className:"flex flex-col",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"font-medium text-sm",children:e.name}),(0,d.jsx)(ie,{variant:"secondary",className:"text-xs",children:e.version})]})}),(0,d.jsxs)(o4,{children:[(0,d.jsx)(o5,{asChild:!0,children:(0,d.jsx)(eT,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,d.jsx)(iu,{className:"h-4 w-4"})})}),(0,d.jsxs)(o6,{align:"start",children:[(0,d.jsxs)(o8,{children:[(0,d.jsx)(id,{className:"mr-2 h-4 w-4"}),"应用配置"]}),(0,d.jsxs)(o8,{children:[(0,d.jsx)(ih,{className:"mr-2 h-4 w-4"}),"复制应用"]}),(0,d.jsx)(o7,{}),(0,d.jsxs)(o8,{className:"text-red-600",children:[(0,d.jsx)(ip,{className:"mr-2 h-4 w-4"}),"删除应用"]})]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:`w-2 h-2 rounded-full ${(e=>{switch(e){case"development":return"bg-yellow-500";case"testing":return"bg-blue-500";case"production":return"bg-green-500";default:return"bg-gray-500"}})(e.status)}`}),(0,d.jsx)("span",{className:"text-sm text-gray-600",children:(e=>{switch(e){case"development":return"开发中";case"testing":return"测试中";case"production":return"已发布";default:return"未知"}})(e.status)})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(eT,{variant:"outline",size:"sm",children:[(0,d.jsx)(im,{className:"mr-2 h-4 w-4"}),"导入"]}),(0,d.jsxs)(eT,{variant:"outline",size:"sm",children:[(0,d.jsx)(ig,{className:"mr-2 h-4 w-4"}),"导出"]}),"development"===e.status?(0,d.jsxs)(eT,{size:"sm",children:[(0,d.jsx)(iv,{className:"mr-2 h-4 w-4"}),"测试运行"]}):(0,d.jsxs)(eT,{size:"sm",variant:"outline",children:[(0,d.jsx)(ix,{className:"mr-2 h-4 w-4"}),"停止"]}),(0,d.jsx)(eT,{size:"sm",className:"bg-blue-600 hover:bg-blue-700",children:"发布应用"})]})]})]})}var iS="Tabs",[ik,ij]=eO(iS,[n1]),iM=n1(),[iR,iC]=ik(iS),iA=f.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:s="automatic",...c}=e,u=e7(l),[f,h]=e2({prop:r,onChange:o,defaultProp:i??"",caller:iS});return(0,d.jsx)(iR,{scope:n,baseId:tv(),value:f,onValueChange:h,orientation:a,dir:u,activationMode:s,children:(0,d.jsx)(eL.div,{dir:u,"data-orientation":a,...c,ref:t})})});iA.displayName=iS;var iP="TabsList",iT=f.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=iC(iP,n),a=iM(n);return(0,d.jsx)(n4,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,d.jsx)(eL.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});iT.displayName=iP;var iO="TabsTrigger",iz=f.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...i}=e,a=iC(iO,n),l=iM(n),s=iL(a.baseId,r),c=i$(a.baseId,r),u=r===a.value;return(0,d.jsx)(n8,{asChild:!0,...l,focusable:!o,active:u,children:(0,d.jsx)(eL.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:s,...i,ref:t,onMouseDown:e0(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(r)}),onKeyDown:e0(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(r)}),onFocus:e0(e.onFocus,()=>{let e="manual"!==a.activationMode;u||o||!e||a.onValueChange(r)})})})});iz.displayName=iO;var iD="TabsContent",iI=f.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,forceMount:o,children:i,...a}=e,l=iC(iD,n),s=iL(l.baseId,r),c=i$(l.baseId,r),u=r===l.value,h=f.useRef(u);return f.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(nX,{present:o||u,children:({present:n})=>(0,d.jsx)(eL.div,{"data-state":u?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":s,hidden:!n,id:c,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&i})})});function iL(e,t){return`${e}-trigger-${t}`}function i$(e,t){return`${e}-content-${t}`}function iH({className:e,...t}){return(0,d.jsx)(iA,{"data-slot":"tabs",className:eA("flex flex-col gap-2",e),...t})}function iF({className:e,...t}){return(0,d.jsx)(iT,{"data-slot":"tabs-list",className:eA("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function iB({className:e,...t}){return(0,d.jsx)(iz,{"data-slot":"tabs-trigger",className:eA("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function iV({className:e,...t}){return(0,d.jsx)(iI,{"data-slot":"tabs-content",className:eA("flex-1 outline-none",e),...t})}iI.displayName=iD;var iU="ScrollArea",[iW,iX]=eO(iU),[iq,iK]=iW(iU),iY=f.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:r="hover",dir:o,scrollHideDelay:i=600,...a}=e,[l,s]=f.useState(null),[c,u]=f.useState(null),[h,p]=f.useState(null),[m,v]=f.useState(null),[x,y]=f.useState(null),[b,w]=f.useState(0),[E,_]=f.useState(0),[N,S]=f.useState(!1),[k,j]=f.useState(!1),M=g(t,e=>s(e)),R=e7(o);return(0,d.jsx)(iq,{scope:n,type:r,dir:R,scrollHideDelay:i,scrollArea:l,viewport:c,onViewportChange:u,content:h,onContentChange:p,scrollbarX:m,onScrollbarXChange:v,scrollbarXEnabled:N,onScrollbarXEnabledChange:S,scrollbarY:x,onScrollbarYChange:y,scrollbarYEnabled:k,onScrollbarYEnabledChange:j,onCornerWidthChange:w,onCornerHeightChange:_,children:(0,d.jsx)(eL.div,{dir:R,...a,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":b+"px","--radix-scroll-area-corner-height":E+"px",...e.style}})})});iY.displayName=iU;var iG="ScrollAreaViewport",iZ=f.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:r,nonce:o,...i}=e,a=iK(iG,n),l=g(t,f.useRef(null),a.onViewportChange);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,d.jsx)(eL.div,{"data-radix-scroll-area-viewport":"",...i,ref:l,style:{overflowX:a.scrollbarXEnabled?"scroll":"hidden",overflowY:a.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,d.jsx)("div",{ref:a.onContentChange,style:{minWidth:"100%",display:"table"},children:r})})]})});iZ.displayName=iG;var iQ="ScrollAreaScrollbar",iJ=f.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=iK(iQ,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=o,l="horizontal"===e.orientation;return f.useEffect(()=>(l?i(!0):a(!0),()=>{l?i(!1):a(!1)}),[l,i,a]),"hover"===o.type?(0,d.jsx)(i0,{...r,ref:t,forceMount:n}):"scroll"===o.type?(0,d.jsx)(i1,{...r,ref:t,forceMount:n}):"auto"===o.type?(0,d.jsx)(i2,{...r,ref:t,forceMount:n}):"always"===o.type?(0,d.jsx)(i3,{...r,ref:t}):null});iJ.displayName=iQ;var i0=f.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=iK(iQ,e.__scopeScrollArea),[i,a]=f.useState(!1);return f.useEffect(()=>{let e=o.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),a(!0)},r=()=>{t=window.setTimeout(()=>a(!1),o.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[o.scrollArea,o.scrollHideDelay]),(0,d.jsx)(nX,{present:n||i,children:(0,d.jsx)(i2,{"data-state":i?"visible":"hidden",...r,ref:t})})}),i1=f.forwardRef((e,t)=>{var n,r;let{forceMount:o,...i}=e,a=iK(iQ,e.__scopeScrollArea),l="horizontal"===e.orientation,s=ad(()=>u("SCROLL_END"),100),[c,u]=(n="hidden",r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},f.useReducer((e,t)=>r[e][t]??e,n));return f.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>u("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,a.scrollHideDelay,u]),f.useEffect(()=>{let e=a.viewport,t=l?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(u("SCROLL"),s()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[a.viewport,l,u,s]),(0,d.jsx)(nX,{present:o||"hidden"!==c,children:(0,d.jsx)(i3,{"data-state":"hidden"===c?"hidden":"visible",...i,ref:t,onPointerEnter:e0(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:e0(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),i2=f.forwardRef((e,t)=>{let n=iK(iQ,e.__scopeScrollArea),{forceMount:r,...o}=e,[i,a]=f.useState(!1),l="horizontal"===e.orientation,s=ad(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;a(l?e:t)}},10);return af(n.viewport,s),af(n.content,s),(0,d.jsx)(nX,{present:r||i,children:(0,d.jsx)(i3,{"data-state":i?"visible":"hidden",...o,ref:t})})}),i3=f.forwardRef((e,t)=>{let{orientation:n="vertical",...r}=e,o=iK(iQ,e.__scopeScrollArea),i=f.useRef(null),a=f.useRef(0),[l,s]=f.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=aa(l.viewport,l.content),u={...r,sizes:l,onSizesChange:s,hasThumb:!!(c>0&&c<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function h(e,t){return function(e,t,n,r="ltr"){let o=al(n),i=t||o/2,a=n.scrollbar.paddingStart+i,l=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),s=n.content-n.viewport;return ac([a,l],"ltr"===r?[0,s]:[-1*s,0])(e)}(e,a.current,l,t)}return"horizontal"===n?(0,d.jsx)(i4,{...u,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){let e=as(o.viewport.scrollLeft,l,o.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=h(e,o.dir))}}):"vertical"===n?(0,d.jsx)(i5,{...u,ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){let e=as(o.viewport.scrollTop,l);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=h(e))}}):null}),i4=f.forwardRef((e,t)=>{let{sizes:n,onSizesChange:r,...o}=e,i=iK(iQ,e.__scopeScrollArea),[a,l]=f.useState(),s=f.useRef(null),c=g(t,s,i.onScrollbarXChange);return f.useEffect(()=>{s.current&&l(getComputedStyle(s.current))},[s]),(0,d.jsx)(i7,{"data-orientation":"horizontal",...o,ref:c,sizes:n,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":al(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(i.viewport){let r=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{s.current&&i.viewport&&a&&r({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:ai(a.paddingLeft),paddingEnd:ai(a.paddingRight)}})}})}),i5=f.forwardRef((e,t)=>{let{sizes:n,onSizesChange:r,...o}=e,i=iK(iQ,e.__scopeScrollArea),[a,l]=f.useState(),s=f.useRef(null),c=g(t,s,i.onScrollbarYChange);return f.useEffect(()=>{s.current&&l(getComputedStyle(s.current))},[s]),(0,d.jsx)(i7,{"data-orientation":"vertical",...o,ref:c,sizes:n,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":al(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(i.viewport){let r=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{s.current&&i.viewport&&a&&r({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:ai(a.paddingTop),paddingEnd:ai(a.paddingBottom)}})}})}),[i6,i8]=iW(iQ),i7=f.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:r,hasThumb:o,onThumbChange:i,onThumbPointerUp:a,onThumbPointerDown:l,onThumbPositionChange:s,onDragScroll:c,onWheelScroll:u,onResize:h,...p}=e,m=iK(iQ,n),[v,x]=f.useState(null),y=g(t,e=>x(e)),b=f.useRef(null),w=f.useRef(""),E=m.viewport,_=r.content-r.viewport,N=ez(u),S=ez(s),k=ad(h,10);function j(e){b.current&&c({x:e.clientX-b.current.left,y:e.clientY-b.current.top})}return f.useEffect(()=>{let e=e=>{let t=e.target;v?.contains(t)&&N(e,_)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[E,v,_,N]),f.useEffect(S,[r,S]),af(v,k),af(m.content,k),(0,d.jsx)(i6,{scope:n,scrollbar:v,hasThumb:o,onThumbChange:ez(i),onThumbPointerUp:ez(a),onThumbPositionChange:S,onThumbPointerDown:ez(l),children:(0,d.jsx)(eL.div,{...p,ref:y,style:{position:"absolute",...p.style},onPointerDown:e0(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),b.current=v.getBoundingClientRect(),w.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",m.viewport&&(m.viewport.style.scrollBehavior="auto"),j(e))}),onPointerMove:e0(e.onPointerMove,j),onPointerUp:e0(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=w.current,m.viewport&&(m.viewport.style.scrollBehavior=""),b.current=null})})})}),i9="ScrollAreaThumb",ae=f.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=i8(i9,e.__scopeScrollArea);return(0,d.jsx)(nX,{present:n||o.hasThumb,children:(0,d.jsx)(at,{ref:t,...r})})}),at=f.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:r,...o}=e,i=iK(i9,n),a=i8(i9,n),{onThumbPositionChange:l}=a,s=g(t,e=>a.onThumbChange(e)),c=f.useRef(void 0),u=ad(()=>{c.current&&(c.current(),c.current=void 0)},100);return f.useEffect(()=>{let e=i.viewport;if(e){let t=()=>{u(),c.current||(c.current=au(e,l),l())};return l(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[i.viewport,u,l]),(0,d.jsx)(eL.div,{"data-state":a.hasThumb?"visible":"hidden",...o,ref:s,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...r},onPointerDownCapture:e0(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;a.onThumbPointerDown({x:n,y:r})}),onPointerUp:e0(e.onPointerUp,a.onThumbPointerUp)})});ae.displayName=i9;var an="ScrollAreaCorner",ar=f.forwardRef((e,t)=>{let n=iK(an,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,d.jsx)(ao,{...e,ref:t}):null});ar.displayName=an;var ao=f.forwardRef((e,t)=>{let{__scopeScrollArea:n,...r}=e,o=iK(an,n),[i,a]=f.useState(0),[l,s]=f.useState(0),c=!!(i&&l);return af(o.scrollbarX,()=>{let e=o.scrollbarX?.offsetHeight||0;o.onCornerHeightChange(e),s(e)}),af(o.scrollbarY,()=>{let e=o.scrollbarY?.offsetWidth||0;o.onCornerWidthChange(e),a(e)}),c?(0,d.jsx)(eL.div,{...r,ref:t,style:{width:i,height:l,position:"absolute",right:"ltr"===o.dir?0:void 0,left:"rtl"===o.dir?0:void 0,bottom:0,...e.style}}):null});function ai(e){return e?parseInt(e,10):0}function aa(e,t){let n=e/t;return isNaN(n)?0:n}function al(e){let t=aa(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function as(e,t,n="ltr"){let r=al(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,l=function(e,[t,n]){return Math.min(n,Math.max(t,e))}(e,"ltr"===n?[0,a]:[-1*a,0]);return ac([0,a],[0,i-r])(l)}function ac(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var au=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,l=n.top!==i.top;(a||l)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function ad(e,t){let n=ez(e),r=f.useRef(0);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),f.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function af(e,t){let n=ez(t);eD(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}function ah({className:e,children:t,...n}){return(0,d.jsxs)(iY,{"data-slot":"scroll-area",className:eA("relative",e),...n,children:[(0,d.jsx)(iZ,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:t}),(0,d.jsx)(ap,{}),(0,d.jsx)(ar,{})]})}function ap({className:e,orientation:t="vertical",...n}){return(0,d.jsx)(iJ,{"data-slot":"scroll-area-scrollbar",orientation:t,className:eA("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...n,children:(0,d.jsx)(ae,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function am({className:e,...t}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:eA("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}let ag=ic("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),av=ic("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),ax=ic("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),ay=ic("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),ab=ic("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),aw=ic("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]]),aE=ic("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]),a_=ic("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),aN=ic("thumbs-up",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]),aS=ic("thumbs-down",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]);function ak({message:e}){let t="user"===e.type;return(0,d.jsxs)("div",{className:`flex space-x-3 ${t?"flex-row-reverse space-x-reverse":""}`,children:[(0,d.jsx)(eQ,{className:"h-8 w-8 flex-shrink-0",children:(0,d.jsx)(eJ,{className:t?"bg-blue-600 text-white":"bg-gray-200",children:t?"你":"AI"})}),(0,d.jsxs)("div",{className:`flex-1 space-y-2 ${t?"items-end":"items-start"}`,children:[(0,d.jsxs)("div",{className:`max-w-[280px] p-3 rounded-lg ${t?"bg-blue-600 text-white ml-auto":"bg-gray-100 text-gray-900"}`,children:[(0,d.jsx)("div",{className:"text-sm whitespace-pre-wrap",children:e.content}),e.attachments&&e.attachments.length>0&&(0,d.jsx)("div",{className:"mt-2 space-y-1",children:e.attachments.map((e,n)=>(0,d.jsxs)("div",{className:`text-xs p-2 rounded ${t?"bg-blue-500":"bg-gray-200"}`,children:["\uD83D\uDCCE ",e.name]},n))})]}),(0,d.jsxs)("div",{className:`flex items-center space-x-2 text-xs text-gray-500 ${t?"flex-row-reverse space-x-reverse":""}`,children:[(0,d.jsx)("span",{children:e.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),!t&&(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(eT,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-gray-200",onClick:()=>{navigator.clipboard.writeText(e.content)},children:(0,d.jsx)(ih,{className:"h-3 w-3"})}),(0,d.jsx)(eT,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-gray-200",children:(0,d.jsx)(aN,{className:"h-3 w-3"})}),(0,d.jsx)(eT,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-gray-200",children:(0,d.jsx)(aS,{className:"h-3 w-3"})})]})]})]})]})}let aj=ic("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),aM=ic("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),aR=ic("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]),aC=ic("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);function aA({fileId:e,onBack:t}){let{files:n,updateFile:r}=i_(),[o,i]=(0,f.useState)(!1),[a,l]=(0,f.useState)(""),[s,c]=(0,f.useState)(!1),u=n.find(t=>t.id===e),h=e=>{l(e),c(e!==u?.content)};return u?(0,d.jsxs)("div",{className:"flex flex-col h-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(eT,{variant:"ghost",size:"sm",onClick:t,className:"p-2",children:(0,d.jsx)(aj,{className:"h-4 w-4"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-sm",children:u.name}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:["最后修改: ",u.lastModified.toLocaleString()]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(eT,{variant:"outline",size:"sm",onClick:()=>i(!o),children:o?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(aM,{className:"h-4 w-4 mr-2"}),"预览"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(aR,{className:"h-4 w-4 mr-2"}),"编辑"]})}),s&&(0,d.jsxs)(eT,{size:"sm",onClick:()=>{u&&s&&(r(u.id,a),c(!1))},className:"bg-blue-600 hover:bg-blue-700",children:[(0,d.jsx)(aC,{className:"h-4 w-4 mr-2"}),"保存"]})]})]}),(0,d.jsx)("div",{className:"flex-1 p-4",children:o?(0,d.jsx)(am,{value:a,onChange:e=>h(e.target.value),className:"w-full h-full resize-none font-mono text-sm",placeholder:"开始编辑文档..."}):(0,d.jsx)("div",{className:"h-full overflow-auto",children:(0,d.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap text-sm leading-relaxed",children:a})})})}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border-t bg-gray-50 text-xs text-gray-500",children:[(0,d.jsxs)("div",{children:["document"===u.type?"\uD83D\uDCC4":"data"===u.type?"\uD83D\uDCCA":"⚙️"," ",u.type]}),(0,d.jsxs)("div",{children:[a.length," 字符 | ",a.split("\n").length," 行"]})]})]}):(0,d.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,d.jsx)("div",{className:"text-gray-500",children:"文件未找到"})})}function aP(){let{leftPanel:e,chat:t,files:n,setLeftPanelTab:r,setDocumentEditing:o,addMessage:i,setLoading:a}=i_(),[l,s]=(0,f.useState)(""),[c,u]=(0,f.useState)(null),h=async()=>{l.trim()&&(i({type:"user",content:l}),s(""),a(!0),setTimeout(()=>{i({type:"assistant",content:"我理解了你的需求。让我帮你分析一下这个工作流程...\n\n基于你的描述，我建议创建以下几个处理节点：\n1. 数据输入验证节点\n2. 智能匹配算法节点\n3. 优化调度节点\n4. 结果输出节点\n\n你希望我先从哪个部分开始构建？"}),a(!1)},2e3))},p=e=>{u(e),o(!0)};return(0,d.jsx)("div",{className:"w-96 border-r bg-white flex flex-col h-full",children:e.isDocumentEditing&&c?(0,d.jsx)(aA,{fileId:c,onBack:()=>{u(null),o(!1)}}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(iH,{value:e.activeTab,onValueChange:e=>r(e),className:"flex flex-col h-full",children:[(0,d.jsxs)(iF,{className:"grid w-full grid-cols-2 m-4 mb-0",children:[(0,d.jsxs)(iB,{value:"chat",className:"flex items-center space-x-2",children:[(0,d.jsx)(ag,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"对话"})]}),(0,d.jsxs)(iB,{value:"files",className:"flex items-center space-x-2",children:[(0,d.jsx)(av,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"文件"})]})]}),(0,d.jsx)(iV,{value:"chat",className:"flex-1 flex flex-col m-4 mt-0",children:(0,d.jsx)(ah,{className:"flex-1 pr-4",children:(0,d.jsxs)("div",{className:"space-y-4 py-4",children:[t.messages.map(e=>(0,d.jsx)(ak,{message:e},e.id)),t.isLoading&&(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"text-sm",children:"AI 正在思考..."})]})]})})}),(0,d.jsxs)(iV,{value:"files",className:"flex-1 flex flex-col m-4 mt-0",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"font-medium",children:"项目文件"}),(0,d.jsxs)(eT,{size:"sm",variant:"outline",children:[(0,d.jsx)(ax,{className:"h-4 w-4 mr-2"}),"添加"]})]}),(0,d.jsx)(ah,{className:"flex-1",children:(0,d.jsx)("div",{className:"space-y-2",children:n.map(e=>(0,d.jsx)("div",{className:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>p(e.id),children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(ay,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"font-medium text-sm truncate",children:e.name}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500 mt-1",children:[(0,d.jsx)(ab,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:e.lastModified.toLocaleDateString()})]})]})]})},e.id))})})]})]}),"chat"===e.activeTab&&(0,d.jsxs)("div",{className:"border-t p-4 space-y-3",children:[(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(eT,{variant:"outline",size:"sm",children:(0,d.jsx)(aw,{className:"h-4 w-4"})}),(0,d.jsx)(eT,{variant:"outline",size:"sm",children:(0,d.jsx)(aE,{className:"h-4 w-4"})})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(am,{placeholder:"描述你的工作流程需求...",value:l,onChange:e=>s(e.target.value),className:"flex-1 min-h-[80px] resize-none",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),h())}}),(0,d.jsx)(eT,{onClick:h,disabled:!l.trim()||t.isLoading,className:"self-end",children:(0,d.jsx)(a_,{className:"h-4 w-4"})})]})]})]})})}let aT=ic("maximize-2",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]]),aO=ic("workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]]),az=ic("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]]);function aD(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n=0,r;n<e.length;n++)""!==(r=aD(e[n]))&&(t+=(t&&" ")+r);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var aI=n(9733);let aL=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},a$=e=>e?aL(e):aL,{useDebugValue:aH}=f,{useSyncExternalStoreWithSelector:aF}=aI,aB=e=>e;function aV(e,t=aB,n){let r=aF(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return aH(r),r}let aU=(e,t)=>{let n=a$(e),r=(e,r=t)=>aV(n,e,r);return Object.assign(r,n),r},aW=(e,t)=>e?aU(e,t):aU;function aX(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var aq={value:()=>{}};function aK(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw Error("illegal type: "+e);r[e]=[]}return new aY(r)}function aY(e){this._=e}function aG(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=aq,e=e.slice(0,r).concat(e.slice(r+1));break}return null!=n&&e.push({name:t,value:n}),e}function aZ(){}function aQ(e){return null==e?aZ:function(){return this.querySelector(e)}}function aJ(){return[]}function a0(e){return null==e?aJ:function(){return this.querySelectorAll(e)}}function a1(e){return function(){return this.matches(e)}}function a2(e){return function(t){return t.matches(e)}}aY.prototype=aK.prototype={constructor:aY,on:function(e,t){var n,r=this._,o=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),i=-1,a=o.length;if(arguments.length<2){for(;++i<a;)if((n=(e=o[i]).type)&&(n=function(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}(r[n],e.name)))return n;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++i<a;)if(n=(e=o[i]).type)r[n]=aG(r[n],e.name,t);else if(null==t)for(n in r)r[n]=aG(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new aY(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=Array(n),i=0;i<n;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(r=this._[e],i=0,n=r.length;i<n;++i)r[i].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};var a3=Array.prototype.find;function a4(){return this.firstElementChild}var a5=Array.prototype.filter;function a6(){return Array.from(this.children)}function a8(e){return Array(e.length)}function a7(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function a9(e,t,n,r,o,i){for(var a,l=0,s=t.length,c=i.length;l<c;++l)(a=t[l])?(a.__data__=i[l],r[l]=a):n[l]=new a7(e,i[l]);for(;l<s;++l)(a=t[l])&&(o[l]=a)}function le(e,t,n,r,o,i,a){var l,s,c,u=new Map,d=t.length,f=i.length,h=Array(d);for(l=0;l<d;++l)(s=t[l])&&(h[l]=c=a.call(s,s.__data__,l,t)+"",u.has(c)?o[l]=s:u.set(c,s));for(l=0;l<f;++l)c=a.call(e,i[l],l,i)+"",(s=u.get(c))?(r[l]=s,s.__data__=i[l],u.delete(c)):n[l]=new a7(e,i[l]);for(l=0;l<d;++l)(s=t[l])&&u.get(h[l])===s&&(o[l]=s)}function lt(e){return e.__data__}function ln(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}a7.prototype={constructor:a7,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var lr="http://www.w3.org/1999/xhtml";let lo={svg:"http://www.w3.org/2000/svg",xhtml:lr,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function li(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),lo.hasOwnProperty(t)?{space:lo[t],local:e}:e}function la(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function ll(e,t){return e.style.getPropertyValue(t)||la(e).getComputedStyle(e,null).getPropertyValue(t)}function ls(e){return e.trim().split(/^|\s+/)}function lc(e){return e.classList||new lu(e)}function lu(e){this._node=e,this._names=ls(e.getAttribute("class")||"")}function ld(e,t){for(var n=lc(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function lf(e,t){for(var n=lc(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function lh(){this.textContent=""}function lp(){this.innerHTML=""}function lm(){this.nextSibling&&this.parentNode.appendChild(this)}function lg(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function lv(e){var t=li(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===lr&&t.documentElement.namespaceURI===lr?t.createElement(e):t.createElementNS(n,e)}})(t)}function lx(){return null}function ly(){var e=this.parentNode;e&&e.removeChild(this)}function lb(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lw(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lE(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,i=t.length;r<i;++r)(n=t[r],e.type&&n.type!==e.type||n.name!==e.name)?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function l_(e,t,n){return function(){var r,o=this.__on,i=function(e){t.call(this,e,this.__data__)};if(o){for(var a=0,l=o.length;a<l;++a)if((r=o[a]).type===e.type&&r.name===e.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),r.value=t;return}}this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},o?o.push(r):this.__on=[r]}}function lN(e,t,n){var r=la(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}lu.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var lS=[null];function lk(e,t){this._groups=e,this._parents=t}function lj(){return new lk([[document.documentElement]],lS)}function lM(e){return"string"==typeof e?new lk([[document.querySelector(e)]],[document.documentElement]):new lk([[e]],lS)}lk.prototype=lj.prototype={constructor:lk,select:function(e){"function"!=typeof e&&(e=aQ(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a,l=t[o],s=l.length,c=r[o]=Array(s),u=0;u<s;++u)(i=l[u])&&(a=e.call(i,i.__data__,u,l))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new lk(r,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=a0(e);for(var n=this._groups,r=n.length,o=[],i=[],a=0;a<r;++a)for(var l,s=n[a],c=s.length,u=0;u<c;++u)(l=s[u])&&(o.push(e.call(l,l.__data__,u,s)),i.push(l));return new lk(o,i)},selectChild:function(e){var t;return this.select(null==e?a4:(t="function"==typeof e?e:a2(e),function(){return a3.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?a6:(t="function"==typeof e?e:a2(e),function(){return a5.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=a1(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],l=a.length,s=r[o]=[],c=0;c<l;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&s.push(i);return new lk(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,lt);var n=t?le:a9,r=this._parents,o=this._groups;"function"!=typeof e&&(x=e,e=function(){return x});for(var i=o.length,a=Array(i),l=Array(i),s=Array(i),c=0;c<i;++c){var u=r[c],d=o[c],f=d.length,h="object"==typeof(v=e.call(u,u&&u.__data__,c,r))&&"length"in v?v:Array.from(v),p=h.length,m=l[c]=Array(p),g=a[c]=Array(p);n(u,d,m,g,s[c]=Array(f),h,t);for(var v,x,y,b,w=0,E=0;w<p;++w)if(y=m[w]){for(w>=E&&(E=w+1);!(b=g[E])&&++E<p;);y._next=b||null}}return(a=new lk(a,r))._enter=l,a._exit=s,a},enter:function(){return new lk(this._enter||this._groups.map(a8),this._parents)},exit:function(){return new lk(this._exit||this._groups.map(a8),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?i.remove():n(i),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),l=Array(o),s=0;s<a;++s)for(var c,u=n[s],d=r[s],f=u.length,h=l[s]=Array(f),p=0;p<f;++p)(c=u[p]||d[p])&&(h[p]=c);for(;s<o;++s)l[s]=n[s];return new lk(l,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],i=o.length-1,a=o[i];--i>=0;)(r=o[i])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=ln);for(var n=this._groups,r=n.length,o=Array(r),i=0;i<r;++i){for(var a,l=n[i],s=l.length,c=o[i]=Array(s),u=0;u<s;++u)(a=l[u])&&(c[u]=a);c.sort(t)}return new lk(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,i=t[n],a=0,l=i.length;a<l;++a)(o=i[a])&&e.call(o,o.__data__,a,i);return this},attr:function(e,t){var n=li(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?n.local?function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}:function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}:n.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}:function(e,t,n){return function(){this.style.setProperty(e,t,n)}})(e,t,null==n?"":n)):ll(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var n=ls(e+"");if(arguments.length<2){for(var r=lc(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?ld:lf)(this,e)}}:t?function(e){return function(){ld(this,e)}}:function(e){return function(){lf(this,e)}})(n,t))},text:function(e){return arguments.length?this.each(null==e?lh:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?lp:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(lm)},lower:function(){return this.each(lg)},append:function(e){var t="function"==typeof e?e:lv(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n="function"==typeof e?e:lv(e),r=null==t?lx:"function"==typeof t?t:aQ(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(ly)},clone:function(e){return this.select(e?lw:lb)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,i=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}),a=i.length;if(arguments.length<2){var l=this.node().__on;if(l){for(var s,c=0,u=l.length;c<u;++c)for(r=0,s=l[c];r<a;++r)if((o=i[r]).type===s.type&&o.name===s.name)return s.value}return}for(r=0,l=t?l_:lE;r<a;++r)this.each(l(i[r],t,n));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return lN(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return lN(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],i=0,a=o.length;i<a;++i)(r=o[i])&&(yield r)}};let lR={passive:!1},lC={capture:!0,passive:!1};function lA(e){e.stopImmediatePropagation()}function lP(e){e.preventDefault(),e.stopImmediatePropagation()}function lT(e){var t=e.document.documentElement,n=lM(e).on("dragstart.drag",lP,lC);"onselectstart"in t?n.on("selectstart.drag",lP,lC):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function lO(e,t){var n=e.document.documentElement,r=lM(e).on("dragstart.drag",null);t&&(r.on("click.drag",lP,lC),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}function lz(e){return((e=Math.exp(e))+1/e)/2}let lD=function e(t,n,r){function o(e,o){var i,a,l=e[0],s=e[1],c=e[2],u=o[0],d=o[1],f=o[2],h=u-l,p=d-s,m=h*h+p*p;if(m<1e-12)a=Math.log(f/c)/t,i=function(e){return[l+e*h,s+e*p,c*Math.exp(t*e*a)]};else{var g=Math.sqrt(m),v=(f*f-c*c+r*m)/(2*c*n*g),x=(f*f-c*c-r*m)/(2*f*n*g),y=Math.log(Math.sqrt(v*v+1)-v);a=(Math.log(Math.sqrt(x*x+1)-x)-y)/t,i=function(e){var r,o,i=e*a,u=lz(y),d=c/(n*g)*(u*(((r=Math.exp(2*(r=t*i+y)))-1)/(r+1))-((o=Math.exp(o=y))-1/o)/2);return[l+d*h,s+d*p,c*u/lz(t*i+y)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4);function lI(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}var lL,l$,lH,lF,lB,lV=0,lU=0,lW=0,lX=0,lq=0,lK=0,lY="object"==typeof performance&&performance.now?performance:Date,lG="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function lZ(){return lq||(lG(lQ),lq=lY.now()+lK)}function lQ(){lq=0}function lJ(){this._call=this._time=this._next=null}function l0(e,t,n){var r=new lJ;return r.restart(e,t,n),r}function l1(){lq=(lX=lY.now())+lK,lV=lU=0;try{lZ(),++lV;for(var e,t=lF;t;)(e=lq-t._time)>=0&&t._call.call(void 0,e),t=t._next;--lV}finally{lV=0,function(){for(var e,t,n=lF,r=1/0;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:lF=t);lB=e,l3(r)}(),lq=0}}function l2(){var e=lY.now(),t=e-lX;t>1e3&&(lK-=t,lX=e)}function l3(e){!lV&&(lU&&(lU=clearTimeout(lU)),e-lq>24?(e<1/0&&(lU=setTimeout(l1,e-lY.now()-lK)),lW&&(lW=clearInterval(lW))):(lW||(lX=lY.now(),lW=setInterval(l2,1e3)),lV=1,lG(l1)))}function l4(e,t,n){var r=new lJ;return t=null==t?0:+t,r.restart(n=>{r.stop(),e(n+t)},t,n),r}lJ.prototype=l0.prototype={constructor:lJ,restart:function(e,t,n){if("function"!=typeof e)throw TypeError("callback is not a function");n=(null==n?lZ():+n)+(null==t?0:+t),this._next||lB===this||(lB?lB._next=this:lF=this,lB=this),this._call=e,this._time=n,l3()},stop:function(){this._call&&(this._call=null,this._time=1/0,l3())}};var l5=aK("start","end","cancel","interrupt"),l6=[];function l8(e,t,n,r,o,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var r,o=e.__transition;function i(s){var c,u,d,f;if(1!==n.state)return l();for(c in o)if((f=o[c]).name===n.name){if(3===f.state)return l4(i);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",e,e.__data__,f.index,f.group),delete o[c]):+c<t&&(f.state=6,f.timer.stop(),f.on.call("cancel",e,e.__data__,f.index,f.group),delete o[c])}if(l4(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(c=0,n.state=3,r=Array(d=n.tween.length),u=-1;c<d;++c)(f=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(r[++u]=f);r.length=u+1}}function a(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=r.length;++i<a;)r[i].call(e,o);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var r in n.state=6,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=l0(function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)},0,n.time)}(e,n,{name:t,index:r,group:o,on:l5,tween:l6,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function l7(e,t){var n=se(e,t);if(n.state>0)throw Error("too late; already scheduled");return n}function l9(e,t){var n=se(e,t);if(n.state>3)throw Error("too late; already running");return n}function se(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw Error("transition not found");return n}function st(e,t){var n,r,o,i=e.__transition,a=!0;if(i){for(o in t=null==t?null:t+"",i){if((n=i[o]).name!==t){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[o]}a&&delete e.__transition}}function sn(e,t){return e*=1,t*=1,function(n){return e*(1-n)+t*n}}var sr=180/Math.PI,so={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function si(e,t,n,r,o,i){var a,l,s;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(s=e*n+t*r)&&(n-=e*s,r-=t*s),(l=Math.sqrt(n*n+r*r))&&(n/=l,r/=l,s/=l),e*r<t*n&&(e=-e,t=-t,s=-s,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*sr,skewX:Math.atan(s)*sr,scaleX:a,scaleY:l}}function sa(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(i,a){var l,s,c,u,d=[],f=[];return i=e(i),a=e(a),!function(e,r,o,i,a,l){if(e!==o||r!==i){var s=a.push("translate(",null,t,null,n);l.push({i:s-4,x:sn(e,o)},{i:s-2,x:sn(r,i)})}else(o||i)&&a.push("translate("+o+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,d,f),l=i.rotate,s=a.rotate,l!==s?(l-s>180?s+=360:s-l>180&&(l+=360),f.push({i:d.push(o(d)+"rotate(",null,r)-2,x:sn(l,s)})):s&&d.push(o(d)+"rotate("+s+r),c=i.skewX,u=a.skewX,c!==u?f.push({i:d.push(o(d)+"skewX(",null,r)-2,x:sn(c,u)}):u&&d.push(o(d)+"skewX("+u+r),!function(e,t,n,r,i,a){if(e!==n||t!==r){var l=i.push(o(i)+"scale(",null,",",null,")");a.push({i:l-4,x:sn(e,n)},{i:l-2,x:sn(t,r)})}else(1!==n||1!==r)&&i.push(o(i)+"scale("+n+","+r+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,d,f),i=a=null,function(e){for(var t,n=-1,r=f.length;++n<r;)d[(t=f[n]).i]=t.x(e);return d.join("")}}}var sl=sa(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?so:si(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),ss=sa(function(e){return null==e?so:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",e),e=r.transform.baseVal.consolidate())?si((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):so},", ",")",")");function sc(e,t,n){var r=e._id;return e.each(function(){var e=l9(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)}),function(e){return se(e,r).value[t]}}function su(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function sd(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function sf(){}var sh="\\s*([+-]?\\d+)\\s*",sp="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",sm="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",sg=/^#([0-9a-f]{3,8})$/,sv=RegExp(`^rgb\\(${sh},${sh},${sh}\\)$`),sx=RegExp(`^rgb\\(${sm},${sm},${sm}\\)$`),sy=RegExp(`^rgba\\(${sh},${sh},${sh},${sp}\\)$`),sb=RegExp(`^rgba\\(${sm},${sm},${sm},${sp}\\)$`),sw=RegExp(`^hsl\\(${sp},${sm},${sm}\\)$`),sE=RegExp(`^hsla\\(${sp},${sm},${sm},${sp}\\)$`),s_={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function sN(){return this.rgb().formatHex()}function sS(){return this.rgb().formatRgb()}function sk(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=sg.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?sj(t):3===n?new sC(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?sM(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?sM(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=sv.exec(e))?new sC(t[1],t[2],t[3],1):(t=sx.exec(e))?new sC(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=sy.exec(e))?sM(t[1],t[2],t[3],t[4]):(t=sb.exec(e))?sM(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=sw.exec(e))?sD(t[1],t[2]/100,t[3]/100,1):(t=sE.exec(e))?sD(t[1],t[2]/100,t[3]/100,t[4]):s_.hasOwnProperty(e)?sj(s_[e]):"transparent"===e?new sC(NaN,NaN,NaN,0):null}function sj(e){return new sC(e>>16&255,e>>8&255,255&e,1)}function sM(e,t,n,r){return r<=0&&(e=t=n=NaN),new sC(e,t,n,r)}function sR(e,t,n,r){var o;return 1==arguments.length?((o=e)instanceof sf||(o=sk(o)),o)?new sC((o=o.rgb()).r,o.g,o.b,o.opacity):new sC:new sC(e,t,n,null==r?1:r)}function sC(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function sA(){return`#${sz(this.r)}${sz(this.g)}${sz(this.b)}`}function sP(){let e=sT(this.opacity);return`${1===e?"rgb(":"rgba("}${sO(this.r)}, ${sO(this.g)}, ${sO(this.b)}${1===e?")":`, ${e})`}`}function sT(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function sO(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function sz(e){return((e=sO(e))<16?"0":"")+e.toString(16)}function sD(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new sL(e,t,n,r)}function sI(e){if(e instanceof sL)return new sL(e.h,e.s,e.l,e.opacity);if(e instanceof sf||(e=sk(e)),!e)return new sL;if(e instanceof sL)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=NaN,l=i-o,s=(i+o)/2;return l?(a=t===i?(n-r)/l+(n<r)*6:n===i?(r-t)/l+2:(t-n)/l+4,l/=s<.5?i+o:2-i-o,a*=60):l=s>0&&s<1?0:a,new sL(a,l,s,e.opacity)}function sL(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function s$(e){return(e=(e||0)%360)<0?e+360:e}function sH(e){return Math.max(0,Math.min(1,e||0))}function sF(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}function sB(e,t,n,r,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*n+(1+3*e+3*i-3*a)*r+a*o)/6}su(sf,sk,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:sN,formatHex:sN,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return sI(this).formatHsl()},formatRgb:sS,toString:sS}),su(sC,sR,sd(sf,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new sC(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new sC(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new sC(sO(this.r),sO(this.g),sO(this.b),sT(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:sA,formatHex:sA,formatHex8:function(){return`#${sz(this.r)}${sz(this.g)}${sz(this.b)}${sz((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:sP,toString:sP})),su(sL,function(e,t,n,r){return 1==arguments.length?sI(e):new sL(e,t,n,null==r?1:r)},sd(sf,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new sL(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new sL(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new sC(sF(e>=240?e-240:e+120,o,r),sF(e,o,r),sF(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new sL(s$(this.h),sH(this.s),sH(this.l),sT(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=sT(this.opacity);return`${1===e?"hsl(":"hsla("}${s$(this.h)}, ${100*sH(this.s)}%, ${100*sH(this.l)}%${1===e?")":`, ${e})`}`}}));let sV=e=>()=>e;function sU(e,t){var n,r,o=t-e;return o?(n=e,r=o,function(e){return n+e*r}):sV(isNaN(e)?t:e)}let sW=function e(t){var n,r=1==(n=+t)?sU:function(e,t){var r,o,i;return t-e?(r=e,o=t,r=Math.pow(r,i=n),o=Math.pow(o,i)-r,i=1/i,function(e){return Math.pow(r+e*o,i)}):sV(isNaN(e)?t:e)};function o(e,t){var n=r((e=sR(e)).r,(t=sR(t)).r),o=r(e.g,t.g),i=r(e.b,t.b),a=sU(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function sX(e){return function(t){var n,r,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(n=0;n<o;++n)r=sR(t[n]),i[n]=r.r||0,a[n]=r.g||0,l[n]=r.b||0;return i=e(i),a=e(a),l=e(l),r.opacity=1,function(e){return r.r=i(e),r.g=a(e),r.b=l(e),r+""}}}sX(function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),o=e[r],i=e[r+1],a=r>0?e[r-1]:2*o-i,l=r<t-1?e[r+2]:2*i-o;return sB((n-r/t)*t,a,o,i,l)}}),sX(function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),o=e[(r+t-1)%t],i=e[r%t],a=e[(r+1)%t],l=e[(r+2)%t];return sB((n-r/t)*t,o,i,a,l)}});var sq=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,sK=RegExp(sq.source,"g");function sY(e,t){var n;return("number"==typeof t?sn:t instanceof sk?sW:(n=sk(t))?(t=n,sW):function(e,t){var n,r,o,i,a,l=sq.lastIndex=sK.lastIndex=0,s=-1,c=[],u=[];for(e+="",t+="";(o=sq.exec(e))&&(i=sK.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),c[s]?c[s]+=a:c[++s]=a),(o=o[0])===(i=i[0])?c[s]?c[s]+=i:c[++s]=i:(c[++s]=null,u.push({i:s,x:sn(o,i)})),l=sK.lastIndex;return l<t.length&&(a=t.slice(l),c[s]?c[s]+=a:c[++s]=a),c.length<2?u[0]?(n=u[0].x,function(e){return n(e)+""}):(r=t,function(){return r}):(t=u.length,function(e){for(var n,r=0;r<t;++r)c[(n=u[r]).i]=n.x(e);return c.join("")})})(e,t)}var sG=lj.prototype.constructor;function sZ(e){return function(){this.style.removeProperty(e)}}var sQ=0;function sJ(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}var s0=lj.prototype;sJ.prototype=(function(e){return lj().transition(e)}).prototype={constructor:sJ,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=aQ(e));for(var r=this._groups,o=r.length,i=Array(o),a=0;a<o;++a)for(var l,s,c=r[a],u=c.length,d=i[a]=Array(u),f=0;f<u;++f)(l=c[f])&&(s=e.call(l,l.__data__,f,c))&&("__data__"in l&&(s.__data__=l.__data__),d[f]=s,l8(d[f],t,n,f,d,se(l,n)));return new sJ(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=a0(e));for(var r=this._groups,o=r.length,i=[],a=[],l=0;l<o;++l)for(var s,c=r[l],u=c.length,d=0;d<u;++d)if(s=c[d]){for(var f,h=e.call(s,s.__data__,d,c),p=se(s,n),m=0,g=h.length;m<g;++m)(f=h[m])&&l8(f,t,n,m,h,p);i.push(h),a.push(s)}return new sJ(i,a,t,n)},selectChild:s0.selectChild,selectChildren:s0.selectChildren,filter:function(e){"function"!=typeof e&&(e=a1(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],l=a.length,s=r[o]=[],c=0;c<l;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&s.push(i);return new sJ(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=Array(r),l=0;l<i;++l)for(var s,c=t[l],u=n[l],d=c.length,f=a[l]=Array(d),h=0;h<d;++h)(s=c[h]||u[h])&&(f[h]=s);for(;l<r;++l)a[l]=t[l];return new sJ(a,this._parents,this._name,this._id)},selection:function(){return new sG(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=++sQ,r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],s=l.length,c=0;c<s;++c)if(a=l[c]){var u=se(a,t);l8(a,e,n,c,l,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new sJ(r,this._parents,e,n)},call:s0.call,nodes:s0.nodes,node:s0.node,size:s0.size,empty:s0.empty,each:s0.each,on:function(e,t){var n,r,o,i,a,l,s=this._id;return arguments.length<2?se(this.node(),s).on.on(e):this.each((n=s,r=e,o=t,l=(r+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?l7:l9,function(){var e=l(this,n),t=e.on;t!==i&&(a=(i=t).copy()).on(r,o),e.on=a}))},attr:function(e,t){var n=li(e),r="transform"===n?ss:sY;return this.attrTween(e,"function"==typeof t?(n.local?function(e,t,n){var r,o,i;return function(){var a,l,s=n(this);return null==s?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local))===(l=s+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,s))}}:function(e,t,n){var r,o,i;return function(){var a,l,s=n(this);return null==s?void this.removeAttribute(e):(a=this.getAttribute(e))===(l=s+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,s))}})(n,r,sc(this,"attr."+e,t)):null==t?(n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(n):(n.local?function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===r?o:o=t(r=a,n)}}:function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===r?o:o=t(r=a,n)}})(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();var r=li(e);return this.tween(n,(r.local?function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttributeNS(e.space,e.local,o.call(this,t))}),n}return o._value=t,o}:function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttribute(e,o.call(this,t))}),n}return o._value=t,o})(r,t))},style:function(e,t,n){var r,o,i,a,l,s,c,u,d,f,h,p,m,g,v,x,y,b,w,E,_,N="transform"==(e+="")?sl:sY;return null==t?this.styleTween(e,(r=e,function(){var e=ll(this,r),t=(this.style.removeProperty(r),ll(this,r));return e===t?null:e===o&&t===i?a:a=N(o=e,i=t)})).on("end.style."+e,sZ(e)):"function"==typeof t?this.styleTween(e,(l=e,s=sc(this,"style."+e,t),function(){var e=ll(this,l),t=s(this),n=t+"";return null==t&&(this.style.removeProperty(l),n=t=ll(this,l)),e===n?null:e===c&&n===u?d:(u=n,d=N(c=e,t))})).each((f=this._id,y="end."+(x="style."+(h=e)),function(){var e=l9(this,f),t=e.on,n=null==e.value[x]?v||(v=sZ(h)):void 0;(t!==p||g!==n)&&(m=(p=t).copy()).on(y,g=n),e.on=m})):this.styleTween(e,(b=e,_=t+"",function(){var e=ll(this,b);return e===_?null:e===w?E:E=N(w=e,t)}),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw Error();return this.tween(r,function(e,t,n){var r,o;function i(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&function(t){this.style.setProperty(e,i.call(this,t),n)}),r}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){var t,n;return this.tween("text","function"==typeof e?(t=sc(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(n=null==e?"":e+"",function(){this.textContent=n}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){this.textContent=r.call(this,e)}),t}return r._value=e,r}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=se(this.node(),n).tween,i=0,a=o.length;i<a;++i)if((r=o[i]).name===e)return r.value;return null}return this.each((null==t?function(e,t){var n,r;return function(){var o=l9(this,e),i=o.tween;if(i!==n){r=n=i;for(var a=0,l=r.length;a<l;++a)if(r[a].name===t){(r=r.slice()).splice(a,1);break}}o.tween=r}}:function(e,t,n){var r,o;if("function"!=typeof n)throw Error();return function(){var i=l9(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var l={name:t,value:n},s=0,c=o.length;s<c;++s)if(o[s].name===t){o[s]=l;break}s===c&&o.push(l)}i.tween=o}})(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){l7(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){l7(this,e).delay=t}})(t,e)):se(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){l9(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){l9(this,e).duration=t}})(t,e)):se(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){l9(this,e).ease=t}}(t,e)):se(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw Error();l9(this,t).ease=n}))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,a){var l={value:a},s={value:function(){0==--o&&i()}};n.each(function(){var n=l9(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(l),t._.interrupt.push(l),t._.end.push(s)),n.on=t}),0===o&&i()})},[Symbol.iterator]:s0[Symbol.iterator]};var s1={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};lj.prototype.interrupt=function(e){return this.each(function(){st(this,e)})},lj.prototype.transition=function(e){var t,n;e instanceof sJ?(t=e._id,e=e._name):(t=++sQ,(n=s1).time=lZ(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],s=l.length,c=0;c<s;++c)(a=l[c])&&l8(a,e,t,c,l,n||function(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return n}(a,t));return new sJ(r,this._parents,e,t)};let s2=e=>()=>e;function s3(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function s4(e,t,n){this.k=e,this.x=t,this.y=n}s4.prototype={constructor:s4,scale:function(e){return 1===e?this:new s4(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new s4(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var s5=new s4(1,0,0);function s6(e){e.stopImmediatePropagation()}function s8(e){e.preventDefault(),e.stopImmediatePropagation()}function s7(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function s9(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function ce(){return this.__zoom||s5}function ct(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function cn(){return navigator.maxTouchPoints||"ontouchstart"in this}function cr(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function co(){var e,t,n,r=s7,o=s9,i=cr,a=ct,l=cn,s=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],u=250,d=lD,f=aK("start","zoom","end"),h=0,p=10;function m(e){e.property("__zoom",ce).on("wheel.zoom",E,{passive:!1}).on("mousedown.zoom",_).on("dblclick.zoom",N).filter(l).on("touchstart.zoom",S).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",j).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(e,t){return(t=Math.max(s[0],Math.min(s[1],t)))===e.k?e:new s4(t,e.x,e.y)}function v(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new s4(e.k,r,o)}function x(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function y(e,t,n,r){e.on("start.zoom",function(){b(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){b(this,arguments).event(r).end()}).tween("zoom",function(){var e=arguments,i=b(this,e).event(r),a=o.apply(this,e),l=null==n?x(a):"function"==typeof n?n.apply(this,e):n,s=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),c=this.__zoom,u="function"==typeof t?t.apply(this,e):t,f=d(c.invert(l).concat(s/c.k),u.invert(l).concat(s/u.k));return function(e){if(1===e)e=u;else{var t=f(e),n=s/t[2];e=new s4(n,l[0]-t[0]*n,l[1]-t[1]*n)}i.zoom(null,e)}})}function b(e,t,n){return!n&&e.__zooming||new w(e,t)}function w(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=o.apply(e,t),this.taps=0}function E(e,...t){if(r.apply(this,arguments)){var n=b(this,t).event(e),o=this.__zoom,l=Math.max(s[0],Math.min(s[1],o.k*Math.pow(2,a.apply(this,arguments)))),u=lI(e);if(n.wheel)(n.mouse[0][0]!==u[0]||n.mouse[0][1]!==u[1])&&(n.mouse[1]=o.invert(n.mouse[0]=u)),clearTimeout(n.wheel);else{if(o.k===l)return;n.mouse=[u,o.invert(u)],st(this),n.start()}s8(e),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",i(v(g(o,l),n.mouse[0],n.mouse[1]),n.extent,c))}}function _(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,a=b(this,t,!0).event(e),l=lM(e.view).on("mousemove.zoom",function(e){if(s8(e),!a.moved){var t=e.clientX-u,n=e.clientY-d;a.moved=t*t+n*n>h}a.event(e).zoom("mouse",i(v(a.that.__zoom,a.mouse[0]=lI(e,o),a.mouse[1]),a.extent,c))},!0).on("mouseup.zoom",function(e){l.on("mousemove.zoom mouseup.zoom",null),lO(e.view,a.moved),s8(e),a.event(e).end()},!0),s=lI(e,o),u=e.clientX,d=e.clientY;lT(e.view),s6(e),a.mouse=[s,this.__zoom.invert(s)],st(this),a.start()}}function N(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,a=lI(e.changedTouches?e.changedTouches[0]:e,this),l=n.invert(a),s=n.k*(e.shiftKey?.5:2),d=i(v(g(n,s),a,l),o.apply(this,t),c);s8(e),u>0?lM(this).transition().duration(u).call(y,d,a,e):lM(this).call(m.transform,d,a,e)}}function S(n,...o){if(r.apply(this,arguments)){var i,a,l,s,c=n.touches,u=c.length,d=b(this,o,n.changedTouches.length===u).event(n);for(s6(n),a=0;a<u;++a)s=[s=lI(l=c[a],this),this.__zoom.invert(s),l.identifier],d.touch0?d.touch1||d.touch0[2]===s[2]||(d.touch1=s,d.taps=0):(d.touch0=s,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=s[0],e=setTimeout(function(){e=null},500)),st(this),d.start())}}function k(e,...t){if(this.__zooming){var n,r,o,a,l=b(this,t).event(e),s=e.changedTouches,u=s.length;for(s8(e),n=0;n<u;++n)o=lI(r=s[n],this),l.touch0&&l.touch0[2]===r.identifier?l.touch0[0]=o:l.touch1&&l.touch1[2]===r.identifier&&(l.touch1[0]=o);if(r=l.that.__zoom,l.touch1){var d=l.touch0[0],f=l.touch0[1],h=l.touch1[0],p=l.touch1[1],m=(m=h[0]-d[0])*m+(m=h[1]-d[1])*m,x=(x=p[0]-f[0])*x+(x=p[1]-f[1])*x;r=g(r,Math.sqrt(m/x)),o=[(d[0]+h[0])/2,(d[1]+h[1])/2],a=[(f[0]+p[0])/2,(f[1]+p[1])/2]}else{if(!l.touch0)return;o=l.touch0[0],a=l.touch0[1]}l.zoom("touch",i(v(r,o,a),l.extent,c))}}function j(e,...r){if(this.__zooming){var o,i,a=b(this,r).event(e),l=e.changedTouches,s=l.length;for(s6(e),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<s;++o)i=l[o],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=lI(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var c=lM(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return m.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",ce),e!==o?y(e,t,n,r):o.interrupt().each(function(){b(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},m.scaleBy=function(e,t,n,r){m.scaleTo(e,function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n},n,r)},m.scaleTo=function(e,t,n,r){m.transform(e,function(){var e=o.apply(this,arguments),r=this.__zoom,a=null==n?x(e):"function"==typeof n?n.apply(this,arguments):n,l=r.invert(a),s="function"==typeof t?t.apply(this,arguments):t;return i(v(g(r,s),a,l),e,c)},n,r)},m.translateBy=function(e,t,n,r){m.transform(e,function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),o.apply(this,arguments),c)},null,r)},m.translateTo=function(e,t,n,r,a){m.transform(e,function(){var e=o.apply(this,arguments),a=this.__zoom,l=null==r?x(e):"function"==typeof r?r.apply(this,arguments):r;return i(s5.translate(l[0],l[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,c)},r,a)},w.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=lM(this.that).datum();f.call(e,this.that,new s3(e,{sourceEvent:this.sourceEvent,target:m,type:e,transform:this.that.__zoom,dispatch:f}),t)}},m.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:s2(+e),m):a},m.filter=function(e){return arguments.length?(r="function"==typeof e?e:s2(!!e),m):r},m.touchable=function(e){return arguments.length?(l="function"==typeof e?e:s2(!!e),m):l},m.extent=function(e){return arguments.length?(o="function"==typeof e?e:s2([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),m):o},m.scaleExtent=function(e){return arguments.length?(s[0]=+e[0],s[1]=+e[1],m):[s[0],s[1]]},m.translateExtent=function(e){return arguments.length?(c[0][0]=+e[0][0],c[1][0]=+e[1][0],c[0][1]=+e[0][1],c[1][1]=+e[1][1],m):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},m.constrain=function(e){return arguments.length?(i=e,m):i},m.duration=function(e){return arguments.length?(u=+e,m):u},m.interpolate=function(e){return arguments.length?(d=e,m):d},m.on=function(){var e=f.on.apply(f,arguments);return e===f?m:e},m.clickDistance=function(e){return arguments.length?(h=(e*=1)*e,m):Math.sqrt(h)},m.tapDistance=function(e){return arguments.length?(p=+e,m):p},m}s4.prototype;let ci=e=>()=>e;function ca(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:l,dx:s,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:l,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function cl(e){return!e.ctrlKey&&!e.button}function cs(){return this.parentNode}function cc(e,t){return null==t?{x:e.x,y:e.y}:t}function cu(){return navigator.maxTouchPoints||"ontouchstart"in this}ca.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};let cd=(0,f.createContext)(null),cf=cd.Provider,ch={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${!e?"source":"target"} handle id: "${!e?t.sourceHandle:t.targetHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},cp=ch.error001();function cm(e,t){let n=(0,f.useContext)(cd);if(null===n)throw Error(cp);return aV(n,e,t)}let cg=()=>{let e=(0,f.useContext)(cd);if(null===e)throw Error(cp);return(0,f.useMemo)(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},cv=e=>e.userSelectionActive?"none":"all";function cx({position:e,children:t,className:n,style:r,...o}){let i=cm(cv),a=`${e}`.split("-");return f.createElement("div",{className:aD(["react-flow__panel",n,...a]),style:{...r,pointerEvents:i},...o},t)}function cy({proOptions:e,position:t="bottom-right"}){return e?.hideAttribution?null:f.createElement(cx,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},f.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var cb=(0,f.memo)(({x:e,y:t,label:n,labelStyle:r={},labelShowBg:o=!0,labelBgStyle:i={},labelBgPadding:a=[2,4],labelBgBorderRadius:l=2,children:s,className:c,...u})=>{let d=(0,f.useRef)(null),[h,p]=(0,f.useState)({x:0,y:0,width:0,height:0}),m=aD(["react-flow__edge-textwrapper",c]);return((0,f.useEffect)(()=>{if(d.current){let e=d.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}},[n]),void 0!==n&&n)?f.createElement("g",{transform:`translate(${e-h.width/2} ${t-h.height/2})`,className:m,visibility:h.width?"visible":"hidden",...u},o&&f.createElement("rect",{width:h.width+2*a[0],x:-a[0],y:-a[1],height:h.height+2*a[1],className:"react-flow__edge-textbg",style:i,rx:l,ry:l}),f.createElement("text",{className:"react-flow__edge-text",y:h.height/2,dy:"0.3em",ref:d,style:r},n),s):null});let cw=e=>({width:e.offsetWidth,height:e.offsetHeight}),cE=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),c_=(e={x:0,y:0},t)=>({x:cE(e.x,t[0][0],t[1][0]),y:cE(e.y,t[0][1],t[1][1])}),cN=(e,t,n)=>e<t?cE(Math.abs(e-t),1,50)/50:e>n?-cE(Math.abs(e-n),1,50)/50:0,cS=(e,t)=>[20*cN(e.x,35,t.width-35),20*cN(e.y,35,t.height-35)],ck=e=>e.getRootNode?.()||window?.document,cj=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),cM=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),cR=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),cC=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),cA=(e,t)=>cR(cj(cM(e),cM(t))),cP=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),cT=e=>cO(e.width)&&cO(e.height)&&cO(e.x)&&cO(e.y),cO=e=>!isNaN(e)&&isFinite(e),cz=Symbol.for("internals"),cD=["Enter"," ","Escape"],cI=(e,t)=>{},cL=e=>"nativeEvent"in e;function c$(e){let t=cL(e)?e.nativeEvent:e,n=t.composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n?.nodeName)||n?.hasAttribute("contenteditable")||!!n?.closest(".nokey")}let cH=e=>"clientX"in e,cF=(e,t)=>{let n=cH(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},cB=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,cV=({id:e,path:t,labelX:n,labelY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:c,style:u,markerEnd:d,markerStart:h,interactionWidth:p=20})=>f.createElement(f.Fragment,null,f.createElement("path",{id:e,style:u,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:d,markerStart:h}),p&&f.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:p,className:"react-flow__edge-interaction"}),o&&cO(n)&&cO(r)?f.createElement(cb,{x:n,y:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:c}):null);function cU(e,t,n){return void 0===n?n:r=>{let o=t().edges.find(t=>t.id===e);o&&n(r,{...o})}}function cW({sourceX:e,sourceY:t,targetX:n,targetY:r}){let o=Math.abs(n-e)/2,i=Math.abs(r-t)/2;return[n<e?n+o:n-o,r<t?r+i:r-i,o,i]}function cX({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:a,targetControlY:l}){let s=.125*e+.375*o+.375*a+.125*n,c=.125*t+.375*i+.375*l+.125*r,u=Math.abs(s-e),d=Math.abs(c-t);return[s,c,u,d]}function cq({pos:e,x1:t,y1:n,x2:r,y2:o}){return e===c.Left||e===c.Right?[.5*(t+r),n]:[t,.5*(n+o)]}function cK({sourceX:e,sourceY:t,sourcePosition:n=c.Bottom,targetX:r,targetY:o,targetPosition:i=c.Top}){let[a,l]=cq({pos:n,x1:e,y1:t,x2:r,y2:o}),[s,u]=cq({pos:i,x1:r,y1:o,x2:e,y2:t}),[d,f,h,p]=cX({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:a,sourceControlY:l,targetControlX:s,targetControlY:u});return[`M${e},${t} C${a},${l} ${s},${u} ${r},${o}`,d,f,h,p]}cV.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(o||(o={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(i||(i={})),function(e){e.Partial="partial",e.Full="full"}(a||(a={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(l||(l={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(s||(s={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(c||(c={}));let cY=(0,f.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=c.Bottom,targetPosition:i=c.Top,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:g,interactionWidth:v})=>{let[x,y,b]=cK({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:i});return f.createElement(cV,{path:x,labelX:y,labelY:b,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:g,interactionWidth:v})});cY.displayName="SimpleBezierEdge";let cG={[c.Left]:{x:-1,y:0},[c.Right]:{x:1,y:0},[c.Top]:{x:0,y:-1},[c.Bottom]:{x:0,y:1}},cZ=({source:e,sourcePosition:t=c.Bottom,target:n})=>t===c.Left||t===c.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},cQ=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function cJ({sourceX:e,sourceY:t,sourcePosition:n=c.Bottom,targetX:r,targetY:o,targetPosition:i=c.Top,borderRadius:a=5,centerX:l,centerY:s,offset:u=20}){let[d,f,h,p,m]=function({source:e,sourcePosition:t=c.Bottom,target:n,targetPosition:r=c.Top,center:o,offset:i}){let a,l,s=cG[t],u=cG[r],d={x:e.x+s.x*i,y:e.y+s.y*i},f={x:n.x+u.x*i,y:n.y+u.y*i},h=cZ({source:d,sourcePosition:t,target:f}),p=0!==h.x?"x":"y",m=h[p],g=[],v={x:0,y:0},x={x:0,y:0},[y,b,w,E]=cW({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(s[p]*u[p]==-1){a=o.x??y,l=o.y??b;let e=[{x:a,y:d.y},{x:a,y:f.y}],t=[{x:d.x,y:l},{x:f.x,y:l}];g=s[p]===m?"x"===p?e:t:"x"===p?t:e}else{let o=[{x:d.x,y:f.y}],c=[{x:f.x,y:d.y}];if(g="x"===p?s.x===m?c:o:s.y===m?o:c,t===r){let t=Math.abs(e[p]-n[p]);if(t<=i){let r=Math.min(i-1,i-t);s[p]===m?v[p]=(d[p]>e[p]?-1:1)*r:x[p]=(f[p]>n[p]?-1:1)*r}}if(t!==r){let e="x"===p?"y":"x",t=s[p]===u[e],n=d[e]>f[e],r=d[e]<f[e];(1===s[p]&&(!t&&n||t&&r)||1!==s[p]&&(!t&&r||t&&n))&&(g="x"===p?o:c)}let h={x:d.x+v.x,y:d.y+v.y},y={x:f.x+x.x,y:f.y+x.y};Math.max(Math.abs(h.x-g[0].x),Math.abs(y.x-g[0].x))>=Math.max(Math.abs(h.y-g[0].y),Math.abs(y.y-g[0].y))?(a=(h.x+y.x)/2,l=g[0].y):(a=g[0].x,l=(h.y+y.y)/2)}return[[e,{x:d.x+v.x,y:d.y+v.y},...g,{x:f.x+x.x,y:f.y+x.y},n],a,l,w,E]}({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:l,y:s},offset:u});return[d.reduce((e,t,n)=>{let r="";return e+(n>0&&n<d.length-1?function(e,t,n,r){let o=Math.min(cQ(e,t)/2,cQ(t,n)/2,r),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a){let t=e.x<n.x?-1:1,r=e.y<n.y?1:-1;return`L ${i+o*t},${a}Q ${i},${a} ${i},${a+o*r}`}let l=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${i},${a+o*s}Q ${i},${a} ${i+o*l},${a}`}(d[n-1],t,d[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`)},""),f,h,p,m]}let c0=(0,f.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:u,style:d,sourcePosition:h=c.Bottom,targetPosition:p=c.Top,markerEnd:m,markerStart:g,pathOptions:v,interactionWidth:x})=>{let[y,b,w]=cJ({sourceX:e,sourceY:t,sourcePosition:h,targetX:n,targetY:r,targetPosition:p,borderRadius:v?.borderRadius,offset:v?.offset});return f.createElement(cV,{path:y,labelX:b,labelY:w,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:u,style:d,markerEnd:m,markerStart:g,interactionWidth:x})});c0.displayName="SmoothStepEdge";let c1=(0,f.memo)(e=>f.createElement(c0,{...e,pathOptions:(0,f.useMemo)(()=>({borderRadius:0,offset:e.pathOptions?.offset}),[e.pathOptions?.offset])}));c1.displayName="StepEdge";let c2=(0,f.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:c,style:u,markerEnd:d,markerStart:h,interactionWidth:p})=>{let[m,g,v]=function({sourceX:e,sourceY:t,targetX:n,targetY:r}){let[o,i,a,l]=cW({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,a,l]}({sourceX:e,sourceY:t,targetX:n,targetY:r});return f.createElement(cV,{path:m,labelX:g,labelY:v,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:l,labelBgPadding:s,labelBgBorderRadius:c,style:u,markerEnd:d,markerStart:h,interactionWidth:p})});function c3(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function c4({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case c.Left:return[t-c3(t-r,i),n];case c.Right:return[t+c3(r-t,i),n];case c.Top:return[t,n-c3(n-o,i)];case c.Bottom:return[t,n+c3(o-n,i)]}}function c5({sourceX:e,sourceY:t,sourcePosition:n=c.Bottom,targetX:r,targetY:o,targetPosition:i=c.Top,curvature:a=.25}){let[l,s]=c4({pos:n,x1:e,y1:t,x2:r,y2:o,c:a}),[u,d]=c4({pos:i,x1:r,y1:o,x2:e,y2:t,c:a}),[f,h,p,m]=cX({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:l,sourceControlY:s,targetControlX:u,targetControlY:d});return[`M${e},${t} C${l},${s} ${u},${d} ${r},${o}`,f,h,p,m]}c2.displayName="StraightEdge";let c6=(0,f.memo)(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=c.Bottom,targetPosition:i=c.Top,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:g,pathOptions:v,interactionWidth:x})=>{let[y,b,w]=c5({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:i,curvature:v?.curvature});return f.createElement(cV,{path:y,labelX:b,labelY:w,label:a,labelStyle:l,labelShowBg:s,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:p,markerEnd:m,markerStart:g,interactionWidth:x})});c6.displayName="BezierEdge";let c8=(0,f.createContext)(null),c7=c8.Provider;c8.Consumer;let c9=()=>(0,f.useContext)(c8),ue=e=>"id"in e&&"source"in e&&"target"in e,ut=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`reactflow__edge-${e}${t||""}-${n}${r||""}`,un=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`},ur=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),uo=(e,t)=>{let n;return e.source&&e.target?ur(n=ue(e)?{...e}:{...e,id:ut(e)},t)?t:t.concat(n):(cI("006",ch.error006()),t)},ui=({x:e,y:t},[n,r,o],i,[a,l])=>{let s={x:(e-n)/o,y:(t-r)/o};return i?{x:a*Math.round(s.x/a),y:l*Math.round(s.y/l)}:s},ua=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r}),ul=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};let n=(e.width??0)*t[0],r=(e.height??0)*t[1],o={x:e.position.x-n,y:e.position.y-r};return{...o,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-r}:o}},us=(e,t=[0,0])=>0===e.length?{x:0,y:0,width:0,height:0}:cR(e.reduce((e,n)=>{let{x:r,y:o}=ul(n,t).positionAbsolute;return cj(e,cM({x:r,y:o,width:n.width||0,height:n.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),uc=(e,t,[n,r,o]=[0,0,1],i=!1,a=!1,l=[0,0])=>{let s={x:(t.x-n)/o,y:(t.y-r)/o,width:t.width/o,height:t.height/o},c=[];return e.forEach(e=>{let{width:t,height:n,selectable:r=!0,hidden:o=!1}=e;if(a&&!r||o)return!1;let{positionAbsolute:u}=ul(e,l),d=cP(s,{x:u.x,y:u.y,width:t||0,height:n||0}),f=void 0===t||void 0===n||null===t||null===n,h=(t||0)*(n||0);(f||i&&d>0||d>=h||e.dragging)&&c.push(e)}),c},uu=(e,t)=>{let n=e.map(e=>e.id);return t.filter(e=>n.includes(e.source)||n.includes(e.target))},ud=(e,t,n,r,o,i=.1)=>{let a=cE(Math.min(t/(e.width*(1+i)),n/(e.height*(1+i))),r,o),l=e.x+e.width/2;return{x:t/2-l*a,y:n/2-(e.y+e.height/2)*a,zoom:a}},uf=(e,t=0)=>e.transition().duration(t);function uh(e,t,n,r){return(t[n]||[]).reduce((t,o)=>(`${e.id}-${o.id}-${n}`!==r&&t.push({id:o.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+o.x+o.width/2,y:(e.positionAbsolute?.y??0)+o.y+o.height/2}),t),[])}let up={source:null,target:null,sourceHandle:null,targetHandle:null},um=()=>({handleDomNode:null,isValid:!1,connection:up,endHandle:null});function ug(e,t,n,r,i,a,l){let s="target"===i,c=l.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),u={...um(),handleDomNode:c};if(c){let e=uv(void 0,c),i=c.getAttribute("data-nodeid"),l=c.getAttribute("data-handleid"),d=c.classList.contains("connectable"),f=c.classList.contains("connectableend"),h={source:s?i:n,sourceHandle:s?l:r,target:s?n:i,targetHandle:s?r:l};u.connection=h,d&&f&&(t===o.Strict?s&&"source"===e||!s&&"target"===e:i!==n||l!==r)&&(u.endHandle={nodeId:i,handleId:l,type:e},u.isValid=a(h))}return u}function uv(e,t){return e?e:t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null}function ux(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function uy({event:e,handleId:t,nodeId:n,onConnect:r,isTarget:o,getState:i,setState:a,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:c}){let u,d,f=ck(e.target),{connectionMode:h,domNode:p,autoPanOnConnect:m,connectionRadius:g,onConnectStart:v,panBy:x,getNodes:y,cancelConnection:b}=i(),w=0,{x:E,y:_}=cF(e),N=uv(s,f?.elementFromPoint(E,_)),S=p?.getBoundingClientRect();if(!S||!N)return;let k=cF(e,S),j=!1,M=null,R=!1,C=null,A=function({nodes:e,nodeId:t,handleId:n,handleType:r}){return e.reduce((e,o)=>{if(o[cz]){let{handleBounds:i}=o[cz],a=[],l=[];i&&(a=uh(o,i,"source",`${t}-${n}-${r}`),l=uh(o,i,"target",`${t}-${n}-${r}`)),e.push(...a,...l)}return e},[])}({nodes:y(),nodeId:n,handleId:t,handleType:N}),P=()=>{if(!m)return;let[e,t]=cS(k,S);x({x:e,y:t}),w=requestAnimationFrame(P)};function T(e){var r,s;let c,{transform:p}=i();k=cF(e,S);let{handle:m,validHandleResult:v}=function(e,t,n,r,o,i){let{x:a,y:l}=cF(e),s=t.elementsFromPoint(a,l).find(e=>e.classList.contains("react-flow__handle"));if(s){let e=s.getAttribute("data-nodeid");if(e){let t=uv(void 0,s),r=s.getAttribute("data-handleid"),a=i({nodeId:e,id:r,type:t});if(a){let i=o.find(n=>n.nodeId===e&&n.type===t&&n.id===r);return{handle:{id:r,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let c=[],u=1/0;if(o.forEach(e=>{let t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=r){let n=i(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}}),!c.length)return{handle:null,validHandleResult:um()};if(1===c.length)return c[0];let d=c.some(({validHandleResult:e})=>e.isValid),f=c.some(({handle:e})=>"target"===e.type);return c.find(({handle:e,validHandleResult:t})=>f?"target"===e.type:!d||t.isValid)||c[0]}(e,f,ui(k,p,!1,[1,1]),g,A,e=>ug(e,h,n,t,o?"target":"source",l,f));if(u=m,j||(P(),j=!0),C=v.handleDomNode,M=v.connection,R=v.isValid,a({connectionPosition:u&&R?ua({x:u.x,y:u.y},p):k,connectionStatus:(r=!!u,c=null,(s=R)?c="valid":r&&!s&&(c="invalid"),c),connectionEndHandle:v.endHandle}),!u&&!R&&!C)return ux(d);M.source!==M.target&&C&&(ux(d),d=C,C.classList.add("connecting","react-flow__handle-connecting"),C.classList.toggle("valid",R),C.classList.toggle("react-flow__handle-valid",R))}function O(e){(u||C)&&M&&R&&r?.(M),i().onConnectEnd?.(e),s&&c?.(e),ux(d),b(),cancelAnimationFrame(w),j=!1,R=!1,M=null,C=null,f.removeEventListener("mousemove",T),f.removeEventListener("mouseup",O),f.removeEventListener("touchmove",T),f.removeEventListener("touchend",O)}a({connectionPosition:k,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:N,connectionStartHandle:{nodeId:n,handleId:t,type:N},connectionEndHandle:null}),v?.(e,{nodeId:n,handleId:t,handleType:N}),f.addEventListener("mousemove",T),f.addEventListener("mouseup",O),f.addEventListener("touchmove",T),f.addEventListener("touchend",O)}let ub=()=>!0,uw=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),uE=(e,t,n)=>r=>{let{connectionStartHandle:o,connectionEndHandle:i,connectionClickStartHandle:a}=r;return{connecting:o?.nodeId===e&&o?.handleId===t&&o?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}},u_=(0,f.forwardRef)(({type:e="source",position:t=c.Top,isValidConnection:n,isConnectable:r=!0,isConnectableStart:o=!0,isConnectableEnd:i=!0,id:a,onConnect:l,children:s,className:u,onMouseDown:d,onTouchStart:h,...p},m)=>{let g=a||null,v="target"===e,x=cg(),y=c9(),{connectOnClick:b,noPanClassName:w}=cm(uw,aX),{connecting:E,clickConnecting:_}=cm(uE(y,g,e),aX);y||x.getState().onError?.("010",ch.error010());let N=e=>{let{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:r}=x.getState(),o={...t,...e};if(r){let{edges:e,setEdges:t}=x.getState();t(uo(o,e))}n?.(o),l?.(o)},S=e=>{if(!y)return;let t=cH(e);o&&(t&&0===e.button||!t)&&uy({event:e,handleId:g,nodeId:y,onConnect:N,isTarget:v,getState:x.getState,setState:x.setState,isValidConnection:n||x.getState().isValidConnection||ub}),t?d?.(e):h?.(e)};return f.createElement("div",{"data-handleid":g,"data-nodeid":y,"data-handlepos":t,"data-id":`${y}-${g}-${e}`,className:aD(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",w,u,{source:!v,target:v,connectable:r,connectablestart:o,connectableend:i,connecting:_,connectionindicator:r&&(o&&!E||i&&E)}]),onMouseDown:S,onTouchStart:S,onClick:b?t=>{let{onClickConnectStart:r,onClickConnectEnd:i,connectionClickStartHandle:a,connectionMode:l,isValidConnection:s}=x.getState();if(!y||!a&&!o)return;if(!a){r?.(t,{nodeId:y,handleId:g,handleType:e}),x.setState({connectionClickStartHandle:{nodeId:y,type:e,handleId:g}});return}let c=ck(t.target),u=n||s||ub,{connection:d,isValid:f}=ug({nodeId:y,id:g,type:e},l,a.nodeId,a.handleId||null,a.type,u,c);f&&N(d),i?.(t),x.setState({connectionClickStartHandle:null})}:void 0,ref:m,...p},s)});u_.displayName="Handle";var uN=(0,f.memo)(u_);let uS=({data:e,isConnectable:t,targetPosition:n=c.Top,sourcePosition:r=c.Bottom})=>f.createElement(f.Fragment,null,f.createElement(uN,{type:"target",position:n,isConnectable:t}),e?.label,f.createElement(uN,{type:"source",position:r,isConnectable:t}));uS.displayName="DefaultNode";var uk=(0,f.memo)(uS);let uj=({data:e,isConnectable:t,sourcePosition:n=c.Bottom})=>f.createElement(f.Fragment,null,e?.label,f.createElement(uN,{type:"source",position:n,isConnectable:t}));uj.displayName="InputNode";var uM=(0,f.memo)(uj);let uR=({data:e,isConnectable:t,targetPosition:n=c.Top})=>f.createElement(f.Fragment,null,f.createElement(uN,{type:"target",position:n,isConnectable:t}),e?.label);uR.displayName="OutputNode";var uC=(0,f.memo)(uR);let uA=()=>null;uA.displayName="GroupNode";let uP=e=>({selectedNodes:e.getNodes().filter(e=>e.selected),selectedEdges:e.edges.filter(e=>e.selected).map(e=>({...e}))}),uT=e=>e.id;function uO(e,t){return aX(e.selectedNodes.map(uT),t.selectedNodes.map(uT))&&aX(e.selectedEdges.map(uT),t.selectedEdges.map(uT))}let uz=(0,f.memo)(({onSelectionChange:e})=>{let t=cg(),{selectedNodes:n,selectedEdges:r}=cm(uP,uO);return(0,f.useEffect)(()=>{let o={nodes:n,edges:r};e?.(o),t.getState().onSelectionChange.forEach(e=>e(o))},[n,r,e]),null});uz.displayName="SelectionListener";let uD=e=>!!e.onSelectionChange;function uI({onSelectionChange:e}){let t=cm(uD);return e||t?f.createElement(uz,{onSelectionChange:e}):null}let uL=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function u$(e,t){(0,f.useEffect)(()=>{void 0!==e&&t(e)},[e])}function uH(e,t,n){(0,f.useEffect)(()=>{void 0!==t&&n({[e]:t})},[t])}let uF=({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:o,onConnectStart:i,onConnectEnd:a,onClickConnectStart:l,onClickConnectEnd:s,nodesDraggable:c,nodesConnectable:u,nodesFocusable:d,edgesFocusable:h,edgesUpdatable:p,elevateNodesOnSelect:m,minZoom:g,maxZoom:v,nodeExtent:x,onNodesChange:y,onEdgesChange:b,elementsSelectable:w,connectionMode:E,snapGrid:_,snapToGrid:N,translateExtent:S,connectOnClick:k,defaultEdgeOptions:j,fitView:M,fitViewOptions:R,onNodesDelete:C,onEdgesDelete:A,onNodeDrag:P,onNodeDragStart:T,onNodeDragStop:O,onSelectionDrag:z,onSelectionDragStart:D,onSelectionDragStop:I,noPanClassName:L,nodeOrigin:$,rfId:H,autoPanOnConnect:F,autoPanOnNodeDrag:B,onError:V,connectionRadius:U,isValidConnection:W,nodeDragThreshold:X})=>{let{setNodes:q,setEdges:K,setDefaultNodesAndEdges:Y,setMinZoom:G,setMaxZoom:Z,setTranslateExtent:Q,setNodeExtent:J,reset:ee}=cm(uL,aX),et=cg();return(0,f.useEffect)(()=>(Y(n,r?.map(e=>({...e,...j}))),()=>{ee()}),[]),uH("defaultEdgeOptions",j,et.setState),uH("connectionMode",E,et.setState),uH("onConnect",o,et.setState),uH("onConnectStart",i,et.setState),uH("onConnectEnd",a,et.setState),uH("onClickConnectStart",l,et.setState),uH("onClickConnectEnd",s,et.setState),uH("nodesDraggable",c,et.setState),uH("nodesConnectable",u,et.setState),uH("nodesFocusable",d,et.setState),uH("edgesFocusable",h,et.setState),uH("edgesUpdatable",p,et.setState),uH("elementsSelectable",w,et.setState),uH("elevateNodesOnSelect",m,et.setState),uH("snapToGrid",N,et.setState),uH("snapGrid",_,et.setState),uH("onNodesChange",y,et.setState),uH("onEdgesChange",b,et.setState),uH("connectOnClick",k,et.setState),uH("fitViewOnInit",M,et.setState),uH("fitViewOnInitOptions",R,et.setState),uH("onNodesDelete",C,et.setState),uH("onEdgesDelete",A,et.setState),uH("onNodeDrag",P,et.setState),uH("onNodeDragStart",T,et.setState),uH("onNodeDragStop",O,et.setState),uH("onSelectionDrag",z,et.setState),uH("onSelectionDragStart",D,et.setState),uH("onSelectionDragStop",I,et.setState),uH("noPanClassName",L,et.setState),uH("nodeOrigin",$,et.setState),uH("rfId",H,et.setState),uH("autoPanOnConnect",F,et.setState),uH("autoPanOnNodeDrag",B,et.setState),uH("onError",V,et.setState),uH("connectionRadius",U,et.setState),uH("isValidConnection",W,et.setState),uH("nodeDragThreshold",X,et.setState),u$(e,q),u$(t,K),u$(g,G),u$(v,Z),u$(S,Q),u$(x,J),null},uB={display:"none"},uV={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},uU="react-flow__node-desc",uW="react-flow__edge-desc",uX=e=>e.ariaLiveMessage;function uq({rfId:e}){let t=cm(uX);return f.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:uV},t)}function uK({rfId:e,disableKeyboardA11y:t}){return f.createElement(f.Fragment,null,f.createElement("div",{id:`${uU}-${e}`,style:uB},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),f.createElement("div",{id:`${uW}-${e}`,style:uB},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&f.createElement(uq,{rfId:e}))}var uY=(e=null,t={actInsideInputWithModifier:!0})=>{let[n,r]=(0,f.useState)(!1),o=(0,f.useRef)(!1),i=(0,f.useRef)(new Set([])),[a,l]=(0,f.useMemo)(()=>{if(null!==e){let t=(Array.isArray(e)?e:[e]).filter(e=>"string"==typeof e).map(e=>e.split("+")),n=t.reduce((e,t)=>e.concat(...t),[]);return[t,n]}return[[],[]]},[e]);return(0,f.useEffect)(()=>{let n="undefined"!=typeof document?document:null,s=t?.target||n;if(null!==e){let e=e=>{if(o.current=e.ctrlKey||e.metaKey||e.shiftKey,(!o.current||o.current&&!t.actInsideInputWithModifier)&&c$(e))return!1;let n=uZ(e.code,l);i.current.add(e[n]),uG(a,i.current,!1)&&(e.preventDefault(),r(!0))},n=e=>{if((!o.current||o.current&&!t.actInsideInputWithModifier)&&c$(e))return!1;let n=uZ(e.code,l);uG(a,i.current,!0)?(r(!1),i.current.clear()):i.current.delete(e[n]),"Meta"===e.key&&i.current.clear(),o.current=!1},c=()=>{i.current.clear(),r(!1)};return s?.addEventListener("keydown",e),s?.addEventListener("keyup",n),window.addEventListener("blur",c),()=>{s?.removeEventListener("keydown",e),s?.removeEventListener("keyup",n),window.removeEventListener("blur",c)}}},[e,r]),n};function uG(e,t,n){return e.filter(e=>n||e.length===t.size).some(e=>e.every(e=>t.has(e)))}function uZ(e,t){return t.includes(e)?"code":"key"}function uQ(e,t,n){e.forEach(r=>{let o=r.parentNode||r.parentId;if(o&&!e.has(o))throw Error(`Parent node ${o} not found`);if(o||n?.[r.id]){let{x:o,y:i,z:a}=function e(t,n,r,o){let i=t.parentNode||t.parentId;if(!i)return r;let a=n.get(i),l=ul(a,o);return e(a,n,{x:(r.x??0)+l.x,y:(r.y??0)+l.y,z:(a[cz]?.z??0)>(r.z??0)?a[cz]?.z??0:r.z??0},o)}(r,e,{...r.position,z:r[cz]?.z??0},t);r.positionAbsolute={x:o,y:i},r[cz].z=a,n?.[r.id]&&(r[cz].isParent=!0)}})}function uJ(e,t,n,r){let o=new Map,i={},a=1e3*!!r;return e.forEach(e=>{let n=(cO(e.zIndex)?e.zIndex:0)+(e.selected?a:0),r=t.get(e.id),l={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},s=e.parentNode||e.parentId;s&&(i[s]=!0),Object.defineProperty(l,cz,{enumerable:!1,value:{handleBounds:r?.type&&r?.type!==e.type?void 0:r?.[cz]?.handleBounds,z:n}}),o.set(e.id,l)}),uQ(o,n,i),o}function u0(e,t={}){let{getNodes:n,width:r,height:o,minZoom:i,maxZoom:a,d3Zoom:l,d3Selection:s,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),f=t.initial&&!c&&u;if(l&&s&&(f||!t.initial)){let e=n().filter(e=>{let n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some(t=>t.id===e.id):n}),c=e.every(e=>e.width&&e.height);if(e.length>0&&c){let{x:n,y:c,zoom:u}=ud(us(e,d),r,o,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=s5.translate(n,c).scale(u);return"number"==typeof t.duration&&t.duration>0?l.transform(uf(s,t.duration),f):l.transform(s,f),!0}}return!1}function u1({changedNodes:e,changedEdges:t,get:n,set:r}){let{nodeInternals:o,edges:i,onNodesChange:a,onEdgesChange:l,hasDefaultNodes:s,hasDefaultEdges:c}=n();e?.length&&(s&&r({nodeInternals:(e.forEach(e=>{let t=o.get(e.id);t&&o.set(t.id,{...t,[cz]:t[cz],selected:e.selected})}),new Map(o))}),a?.(e)),t?.length&&(c&&r({edges:i.map(e=>{let n=t.find(t=>t.id===e.id);return n&&(e.selected=n.selected),e})}),l?.(t))}let u2=()=>{},u3={zoomIn:u2,zoomOut:u2,zoomTo:u2,getZoom:()=>1,setViewport:u2,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:u2,fitBounds:u2,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},u4=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),u5=()=>{let e=cg(),{d3Zoom:t,d3Selection:n}=cm(u4,aX);return(0,f.useMemo)(()=>n&&t?{zoomIn:e=>t.scaleBy(uf(n,e?.duration),1.2),zoomOut:e=>t.scaleBy(uf(n,e?.duration),1/1.2),zoomTo:(e,r)=>t.scaleTo(uf(n,r?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(r,o)=>{let[i,a,l]=e.getState().transform,s=s5.translate(r.x??i,r.y??a).scale(r.zoom??l);t.transform(uf(n,o?.duration),s)},getViewport:()=>{let[t,n,r]=e.getState().transform;return{x:t,y:n,zoom:r}},fitView:t=>u0(e.getState,t),setCenter:(r,o,i)=>{let{width:a,height:l,maxZoom:s}=e.getState(),c=void 0!==i?.zoom?i.zoom:s,u=a/2-r*c,d=l/2-o*c,f=s5.translate(u,d).scale(c);t.transform(uf(n,i?.duration),f)},fitBounds:(r,o)=>{let{width:i,height:a,minZoom:l,maxZoom:s}=e.getState(),{x:c,y:u,zoom:d}=ud(r,i,a,l,s,o?.padding??.1),f=s5.translate(c,u).scale(d);t.transform(uf(n,o?.duration),f)},project:t=>{let{transform:n,snapToGrid:r,snapGrid:o}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),ui(t,n,r,o)},screenToFlowPosition:t=>{let{transform:n,snapToGrid:r,snapGrid:o,domNode:i}=e.getState();if(!i)return t;let{x:a,y:l}=i.getBoundingClientRect();return ui({x:t.x-a,y:t.y-l},n,r,o)},flowToScreenPosition:t=>{let{transform:n,domNode:r}=e.getState();if(!r)return t;let{x:o,y:i}=r.getBoundingClientRect(),a=ua(t,n);return{x:a.x+o,y:a.y+i}},viewportInitialized:!0}:u3,[t,n])};function u6(){let e=u5(),t=cg(),n=(0,f.useCallback)(()=>t.getState().getNodes().map(e=>({...e})),[]),r=(0,f.useCallback)(e=>t.getState().nodeInternals.get(e),[]),o=(0,f.useCallback)(()=>{let{edges:e=[]}=t.getState();return e.map(e=>({...e}))},[]),i=(0,f.useCallback)(e=>{let{edges:n=[]}=t.getState();return n.find(t=>t.id===e)},[]),a=(0,f.useCallback)(e=>{let{getNodes:n,setNodes:r,hasDefaultNodes:o,onNodesChange:i}=t.getState(),a=n(),l="function"==typeof e?e(a):e;o?r(l):i&&i(0===l.length?a.map(e=>({type:"remove",id:e.id})):l.map(e=>({item:e,type:"reset"})))},[]),l=(0,f.useCallback)(e=>{let{edges:n=[],setEdges:r,hasDefaultEdges:o,onEdgesChange:i}=t.getState(),a="function"==typeof e?e(n):e;o?r(a):i&&i(0===a.length?n.map(e=>({type:"remove",id:e.id})):a.map(e=>({item:e,type:"reset"})))},[]),s=(0,f.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{getNodes:r,setNodes:o,hasDefaultNodes:i,onNodesChange:a}=t.getState();i?o([...r(),...n]):a&&a(n.map(e=>({item:e,type:"add"})))},[]),c=(0,f.useCallback)(e=>{let n=Array.isArray(e)?e:[e],{edges:r=[],setEdges:o,hasDefaultEdges:i,onEdgesChange:a}=t.getState();i?o([...r,...n]):a&&a(n.map(e=>({item:e,type:"add"})))},[]),u=(0,f.useCallback)(()=>{let{getNodes:e,edges:n=[],transform:r}=t.getState(),[o,i,a]=r;return{nodes:e().map(e=>({...e})),edges:n.map(e=>({...e})),viewport:{x:o,y:i,zoom:a}}},[]),d=(0,f.useCallback)(({nodes:e,edges:n})=>{let{nodeInternals:r,getNodes:o,edges:i,hasDefaultNodes:a,hasDefaultEdges:l,onNodesDelete:s,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=t.getState(),f=(e||[]).map(e=>e.id),h=(n||[]).map(e=>e.id),p=o().reduce((e,t)=>{let n=t.parentNode||t.parentId,r=!f.includes(t.id)&&n&&e.find(e=>e.id===n);return("boolean"!=typeof t.deletable||t.deletable)&&(f.includes(t.id)||r)&&e.push(t),e},[]),m=i.filter(e=>"boolean"!=typeof e.deletable||e.deletable),g=m.filter(e=>h.includes(e.id));if(p||g){let e=[...g,...uu(p,m)],n=e.reduce((e,t)=>(e.includes(t.id)||e.push(t.id),e),[]);(l||a)&&(l&&t.setState({edges:i.filter(e=>!n.includes(e.id))}),a&&(p.forEach(e=>{r.delete(e.id)}),t.setState({nodeInternals:new Map(r)}))),n.length>0&&(c?.(e),d&&d(n.map(e=>({id:e,type:"remove"})))),p.length>0&&(s?.(p),u&&u(p.map(e=>({id:e.id,type:"remove"}))))}},[]),h=(0,f.useCallback)(e=>{let n=cT(e),r=n?null:t.getState().nodeInternals.get(e.id);return n||r?[n?e:cC(r),r,n]:[null,null,n]},[]),p=(0,f.useCallback)((e,n=!0,r)=>{let[o,i,a]=h(e);return o?(r||t.getState().getNodes()).filter(e=>{if(!a&&(e.id===i.id||!e.positionAbsolute))return!1;let t=cP(cC(e),o);return n&&t>0||t>=o.width*o.height}):[]},[]),m=(0,f.useCallback)((e,t,n=!0)=>{let[r]=h(e);if(!r)return!1;let o=cP(r,t);return n&&o>0||o>=r.width*r.height},[]);return(0,f.useMemo)(()=>({...e,getNodes:n,getNode:r,getEdges:o,getEdge:i,setNodes:a,setEdges:l,addNodes:s,addEdges:c,toObject:u,deleteElements:d,getIntersectingNodes:p,isNodeIntersecting:m}),[e,n,r,o,i,a,l,s,c,u,d,p,m])}let u8={actInsideInputWithModifier:!1};var u7=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{let n=cg(),{deleteElements:r}=u6(),o=uY(e,u8),i=uY(t);(0,f.useEffect)(()=>{if(o){let{edges:e,getNodes:t}=n.getState();r({nodes:t().filter(e=>e.selected),edges:e.filter(e=>e.selected)}),n.setState({nodesSelectionActive:!1})}},[o]),(0,f.useEffect)(()=>{n.setState({multiSelectionActive:i})},[i])};let u9={position:"absolute",width:"100%",height:"100%",top:0,left:0},de=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,dt=e=>({x:e.x,y:e.y,zoom:e.k}),dn=(e,t)=>e.target.closest(`.${t}`),dr=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),di=e=>{let t=e.ctrlKey&&cB()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},da=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),dl=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:r,zoomOnScroll:o=!0,zoomOnPinch:a=!0,panOnScroll:l=!1,panOnScrollSpeed:s=.5,panOnScrollMode:c=i.Free,zoomOnDoubleClick:u=!0,elementsSelectable:d,panOnDrag:h=!0,defaultViewport:p,translateExtent:m,minZoom:g,maxZoom:v,zoomActivationKeyCode:x,preventScrolling:y=!0,children:b,noWheelClassName:w,noPanClassName:E})=>{let _=(0,f.useRef)(),N=cg(),S=(0,f.useRef)(!1),k=(0,f.useRef)(!1),j=(0,f.useRef)(null),M=(0,f.useRef)({x:0,y:0,zoom:0}),{d3Zoom:R,d3Selection:C,d3ZoomHandler:A,userSelectionActive:P}=cm(da,aX),T=uY(x),O=(0,f.useRef)(0),z=(0,f.useRef)(!1),D=(0,f.useRef)();return!function(e){let t=cg();(0,f.useEffect)(()=>{let n,r=()=>{if(!e.current)return;let n=cw(e.current);(0===n.height||0===n.width)&&t.getState().onError?.("004",ch.error004()),t.setState({width:n.width||500,height:n.height||500})};return r(),window.addEventListener("resize",r),e.current&&(n=new ResizeObserver(()=>r())).observe(e.current),()=>{window.removeEventListener("resize",r),n&&e.current&&n.unobserve(e.current)}},[])}(j),(0,f.useEffect)(()=>{if(j.current){let e=j.current.getBoundingClientRect(),t=co().scaleExtent([g,v]).translateExtent(m),n=lM(j.current).call(t),r=s5.translate(p.x,p.y).scale(cE(p.zoom,g,v)),o=[[0,0],[e.width,e.height]],i=t.constrain()(r,o,m);t.transform(n,i),t.wheelDelta(di),N.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[i.x,i.y,i.k],domNode:j.current.closest(".react-flow")})}},[]),(0,f.useEffect)(()=>{C&&R&&(!l||T||P?void 0!==A&&C.on("wheel.zoom",function(e,t){if(!y&&"wheel"===e.type&&!e.ctrlKey||dn(e,w))return null;e.preventDefault(),A.call(this,e,t)},{passive:!1}):C.on("wheel.zoom",r=>{if(dn(r,w))return!1;r.preventDefault(),r.stopImmediatePropagation();let o=C.property("__zoom").k||1;if(r.ctrlKey&&a){let e=lI(r),t=o*Math.pow(2,di(r));R.scaleTo(C,t,e,r);return}let l=1===r.deltaMode?20:1,u=c===i.Vertical?0:r.deltaX*l,d=c===i.Horizontal?0:r.deltaY*l;!cB()&&r.shiftKey&&c!==i.Vertical&&(u=r.deltaY*l,d=0),R.translateBy(C,-(u/o)*s,-(d/o)*s,{internal:!0});let f=dt(C.property("__zoom")),{onViewportChangeStart:h,onViewportChange:p,onViewportChangeEnd:m}=N.getState();clearTimeout(D.current),z.current||(z.current=!0,t?.(r,f),h?.(f)),z.current&&(e?.(r,f),p?.(f),D.current=setTimeout(()=>{n?.(r,f),m?.(f),z.current=!1},150))},{passive:!1}))},[P,l,c,C,R,A,T,a,y,w,t,e,n]),(0,f.useEffect)(()=>{R&&R.on("start",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;O.current=e.sourceEvent?.button;let{onViewportChangeStart:n}=N.getState(),r=dt(e.transform);S.current=!0,M.current=r,e.sourceEvent?.type==="mousedown"&&N.setState({paneDragging:!0}),n?.(r),t?.(e.sourceEvent,r)})},[R,t]),(0,f.useEffect)(()=>{R&&(P&&!S.current?R.on("zoom",null):P||R.on("zoom",t=>{let{onViewportChange:n}=N.getState();if(N.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),k.current=!!(r&&dr(h,O.current??0)),(e||n)&&!t.sourceEvent?.internal){let r=dt(t.transform);n?.(r),e?.(t.sourceEvent,r)}}))},[P,R,e,h,r]),(0,f.useEffect)(()=>{R&&R.on("end",e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;let{onViewportChangeEnd:t}=N.getState();if(S.current=!1,N.setState({paneDragging:!1}),r&&dr(h,O.current??0)&&!k.current&&r(e.sourceEvent),k.current=!1,(n||t)&&de(M.current,e.transform)){let r=dt(e.transform);M.current=r,clearTimeout(_.current),_.current=setTimeout(()=>{t?.(r),n?.(e.sourceEvent,r)},150*!!l)}})},[R,l,h,n,r]),(0,f.useEffect)(()=>{R&&R.filter(e=>{let t=T||o,n=a&&e.ctrlKey;if((!0===h||Array.isArray(h)&&h.includes(1))&&1===e.button&&"mousedown"===e.type&&(dn(e,"react-flow__node")||dn(e,"react-flow__edge")))return!0;if(!h&&!t&&!l&&!u&&!a||P||!u&&"dblclick"===e.type||dn(e,w)&&"wheel"===e.type||dn(e,E)&&("wheel"!==e.type||l&&"wheel"===e.type&&!T)||!a&&e.ctrlKey&&"wheel"===e.type||!t&&!l&&!n&&"wheel"===e.type||!h&&("mousedown"===e.type||"touchstart"===e.type)||Array.isArray(h)&&!h.includes(e.button)&&"mousedown"===e.type)return!1;let r=Array.isArray(h)&&h.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&r})},[P,R,o,a,l,u,h,d,T]),f.createElement("div",{className:"react-flow__renderer",ref:j,style:u9},b)},ds=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function dc(){let{userSelectionActive:e,userSelectionRect:t}=cm(ds,aX);return e&&t?f.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function du(e,t){let n=t.parentNode||t.parentId,r=e.find(e=>e.id===n);if(r){let e=t.position.x+t.width-r.width,n=t.position.y+t.height-r.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(r.style={...r.style},r.style.width=r.style.width??r.width,r.style.height=r.style.height??r.height,e>0&&(r.style.width+=e),n>0&&(r.style.height+=n),t.position.x<0){let e=Math.abs(t.position.x);r.position.x=r.position.x-e,r.style.width+=e,t.position.x=0}if(t.position.y<0){let e=Math.abs(t.position.y);r.position.y=r.position.y-e,r.style.height+=e,t.position.y=0}r.width=r.style.width,r.height=r.style.height}}}function dd(e,t){if(e.some(e=>"reset"===e.type))return e.filter(e=>"reset"===e.type).map(e=>e.item);let n=e.filter(e=>"add"===e.type).map(e=>e.item);return t.reduce((t,n)=>{let r=e.filter(e=>e.id===n.id);if(0===r.length)return t.push(n),t;let o={...n};for(let e of r)if(e)switch(e.type){case"select":o.selected=e.selected;break;case"position":void 0!==e.position&&(o.position=e.position),void 0!==e.positionAbsolute&&(o.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(o.dragging=e.dragging),o.expandParent&&du(t,o);break;case"dimensions":void 0!==e.dimensions&&(o.width=e.dimensions.width,o.height=e.dimensions.height),void 0!==e.updateStyle&&(o.style={...o.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(o.resizing=e.resizing),o.expandParent&&du(t,o);break;case"remove":return t}return t.push(o),t},n)}function df(e,t){return dd(e,t)}let dh=(e,t)=>({id:e,type:"select",selected:t});function dp(e,t){return e.reduce((e,n)=>{let r=t.includes(n.id);return!n.selected&&r?(n.selected=!0,e.push(dh(n.id,!0))):n.selected&&!r&&(n.selected=!1,e.push(dh(n.id,!1))),e},[])}let dm=(e,t)=>n=>{n.target===t.current&&e?.(n)},dg=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),dv=(0,f.memo)(({isSelecting:e,selectionMode:t=a.Full,panOnDrag:n,onSelectionStart:r,onSelectionEnd:o,onPaneClick:i,onPaneContextMenu:l,onPaneScroll:s,onPaneMouseEnter:c,onPaneMouseMove:u,onPaneMouseLeave:d,children:h})=>{let p=(0,f.useRef)(null),m=cg(),g=(0,f.useRef)(0),v=(0,f.useRef)(0),x=(0,f.useRef)(),{userSelectionActive:y,elementsSelectable:b,dragging:w}=cm(dg,aX),E=()=>{m.setState({userSelectionActive:!1,userSelectionRect:null}),g.current=0,v.current=0},_=e=>{i?.(e),m.getState().resetSelectedElements(),m.setState({nodesSelectionActive:!1})},N=b&&(e||y);return f.createElement("div",{className:aD(["react-flow__pane",{dragging:w,selection:e}]),onClick:N?void 0:dm(_,p),onContextMenu:dm(e=>{if(Array.isArray(n)&&n?.includes(2))return void e.preventDefault();l?.(e)},p),onWheel:dm(s?e=>s(e):void 0,p),onMouseEnter:N?void 0:c,onMouseDown:N?t=>{let{resetSelectedElements:n,domNode:o}=m.getState();if(x.current=o?.getBoundingClientRect(),!b||!e||0!==t.button||t.target!==p.current||!x.current)return;let{x:i,y:a}=cF(t,x.current);n(),m.setState({userSelectionRect:{width:0,height:0,startX:i,startY:a,x:i,y:a}}),r?.(t)}:void 0,onMouseMove:N?n=>{let{userSelectionRect:r,nodeInternals:o,edges:i,transform:l,onNodesChange:s,onEdgesChange:c,nodeOrigin:u,getNodes:d}=m.getState();if(!e||!x.current||!r)return;m.setState({userSelectionActive:!0,nodesSelectionActive:!1});let f=cF(n,x.current),h=r.startX??0,p=r.startY??0,y={...r,x:f.x<h?f.x:h,y:f.y<p?f.y:p,width:Math.abs(f.x-h),height:Math.abs(f.y-p)},b=d(),w=uc(o,y,l,t===a.Partial,!0,u),E=uu(w,i).map(e=>e.id),_=w.map(e=>e.id);if(g.current!==_.length){g.current=_.length;let e=dp(b,_);e.length&&s?.(e)}if(v.current!==E.length){v.current=E.length;let e=dp(i,E);e.length&&c?.(e)}m.setState({userSelectionRect:y})}:u,onMouseUp:N?e=>{if(0!==e.button)return;let{userSelectionRect:t}=m.getState();!y&&t&&e.target===p.current&&_?.(e),m.setState({nodesSelectionActive:g.current>0}),E(),o?.(e)}:void 0,onMouseLeave:N?e=>{y&&(m.setState({nodesSelectionActive:g.current>0}),o?.(e)),E()}:d,ref:p,style:u9},h,f.createElement(dc,null))});function dx(e,t,n){let r=e;do{if(r?.matches(t))return!0;if(r===n.current)break;r=r.parentElement}while(r);return!1}function dy(e,t,n,r,o=[0,0],i){var a;let l=(a=e.extent||r)&&"parent"!==a?[a[0],[a[1][0]-(e.width||0),a[1][1]-(e.height||0)]]:a,s=l,c=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&c&&"parent"!==e.extent){let{x:t,y:r}=ul(n.get(c),o).positionAbsolute;s=[[e.extent[0][0]+t,e.extent[0][1]+r],[e.extent[1][0]+t,e.extent[1][1]+r]]}}else if(c&&e.width&&e.height){let t=n.get(c),{x:r,y:i}=ul(t,o).positionAbsolute;s=t&&cO(r)&&cO(i)&&cO(t.width)&&cO(t.height)?[[r+e.width*o[0],i+e.height*o[1]],[r+t.width-e.width+e.width*o[0],i+t.height-e.height+e.height*o[1]]]:s}else i?.("005",ch.error005()),s=l;let u={x:0,y:0};c&&(u=ul(n.get(c),o).positionAbsolute);let d=s&&"parent"!==s?c_(t,s):t;return{position:{x:d.x-u.x,y:d.y-u.y},positionAbsolute:d}}function db({nodeId:e,dragItems:t,nodeInternals:n}){let r=t.map(e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute}));return[e?r.find(t=>t.id===e):r[0],r]}dv.displayName="Pane";let dw=(e,t,n,r)=>{let o=t.querySelectorAll(e);if(!o||!o.length)return null;let i=Array.from(o),a=t.getBoundingClientRect(),l={x:a.width*r[0],y:a.height*r[1]};return i.map(e=>{let t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-l.x)/n,y:(t.top-a.top-l.y)/n,...cw(e)}})};function dE(e,t,n){return void 0===n?n:r=>{let o=t().nodeInternals.get(e);o&&n(r,{...o})}}function d_({id:e,store:t,unselect:n=!1,nodeRef:r}){let{addSelectedNodes:o,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:l,onError:s}=t.getState(),c=l.get(e);if(!c)return void s?.("012",ch.error012(e));t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame(()=>r?.current?.blur())):o([e])}function dN(e){return(t,n,r)=>e?.(t,r)}function dS({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:r,nodeId:o,isSelectable:i,selectNodesOnDrag:a}){let l=cg(),[s,c]=(0,f.useState)(!1),u=(0,f.useRef)([]),d=(0,f.useRef)({x:null,y:null}),h=(0,f.useRef)(0),p=(0,f.useRef)(null),m=(0,f.useRef)({x:0,y:0}),g=(0,f.useRef)(null),v=(0,f.useRef)(!1),x=(0,f.useRef)(!1),y=(0,f.useRef)(!1),b=function(){let e=cg();return(0,f.useCallback)(({sourceEvent:t})=>{let{transform:n,snapGrid:r,snapToGrid:o}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,l={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:o?r[0]*Math.round(l.x/r[0]):l.x,ySnapped:o?r[1]*Math.round(l.y/r[1]):l.y,...l}},[])}();return(0,f.useEffect)(()=>{if(e?.current){let s=lM(e.current),f=({x:e,y:t})=>{let{nodeInternals:n,onNodeDrag:r,onSelectionDrag:i,updateNodePositions:a,nodeExtent:s,snapGrid:f,snapToGrid:h,nodeOrigin:p,onError:m}=l.getState();d.current={x:e,y:t};let v=!1,x={x:0,y:0,x2:0,y2:0};if(u.current.length>1&&s&&(x=cM(us(u.current,p))),u.current=u.current.map(r=>{let o={x:e-r.distance.x,y:t-r.distance.y};h&&(o.x=f[0]*Math.round(o.x/f[0]),o.y=f[1]*Math.round(o.y/f[1]));let i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];u.current.length>1&&s&&!r.extent&&(i[0][0]=r.positionAbsolute.x-x.x+s[0][0],i[1][0]=r.positionAbsolute.x+(r.width??0)-x.x2+s[1][0],i[0][1]=r.positionAbsolute.y-x.y+s[0][1],i[1][1]=r.positionAbsolute.y+(r.height??0)-x.y2+s[1][1]);let a=dy(r,o,n,i,p,m);return v=v||r.position.x!==a.position.x||r.position.y!==a.position.y,r.position=a.position,r.positionAbsolute=a.positionAbsolute,r}),!v)return;a(u.current,!0,!0),c(!0);let y=o?r:dN(i);if(y&&g.current){let[e,t]=db({nodeId:o,dragItems:u.current,nodeInternals:n});y(g.current,e,t)}},w=()=>{if(!p.current)return;let[e,t]=cS(m.current,p.current);if(0!==e||0!==t){let{transform:n,panBy:r}=l.getState();d.current.x=(d.current.x??0)-e/n[2],d.current.y=(d.current.y??0)-t/n[2],r({x:e,y:t})&&f(d.current)}h.current=requestAnimationFrame(w)},E=t=>{let{nodeInternals:n,multiSelectionActive:r,nodesDraggable:s,unselectNodesAndEdges:c,onNodeDragStart:f,onSelectionDragStart:h}=l.getState();x.current=!0;let p=o?f:dN(h);a&&i||r||!o||n.get(o)?.selected||c(),o&&i&&a&&d_({id:o,store:l,nodeRef:e});let m=b(t);if(d.current=m,u.current=Array.from(n.values()).filter(e=>(e.selected||e.id===o)&&(!e.parentNode||e.parentId||!function e(t,n){let r=t.parentNode||t.parentId;if(!r)return!1;let o=n.get(r);return!!o&&(!!o.selected||e(o,n))}(e,n))&&(e.draggable||s&&void 0===e.draggable)).map(e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:m.x-(e.positionAbsolute?.x??0),y:m.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})),p&&u.current){let[e,r]=db({nodeId:o,dragItems:u.current,nodeInternals:n});p(t.sourceEvent,e,r)}};if(t)s.on(".drag",null);else{let t=(function(){var e,t,n,r,o=cl,i=cs,a=cc,l=cu,s={},c=aK("start","drag","end"),u=0,d=0;function f(e){e.on("mousedown.drag",h).filter(l).on("touchstart.drag",g).on("touchmove.drag",v,lR).on("touchend.drag touchcancel.drag",x).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function h(a,l){if(!r&&o.call(this,a,l)){var s=y(this,i.call(this,a,l),a,l,"mouse");s&&(lM(a.view).on("mousemove.drag",p,lC).on("mouseup.drag",m,lC),lT(a.view),lA(a),n=!1,e=a.clientX,t=a.clientY,s("start",a))}}function p(r){if(lP(r),!n){var o=r.clientX-e,i=r.clientY-t;n=o*o+i*i>d}s.mouse("drag",r)}function m(e){lM(e.view).on("mousemove.drag mouseup.drag",null),lO(e.view,n),lP(e),s.mouse("end",e)}function g(e,t){if(o.call(this,e,t)){var n,r,a=e.changedTouches,l=i.call(this,e,t),s=a.length;for(n=0;n<s;++n)(r=y(this,l,e,t,a[n].identifier,a[n]))&&(lA(e),r("start",e,a[n]))}}function v(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=s[r[t].identifier])&&(lP(e),n("drag",e,r[t]))}function x(e){var t,n,o=e.changedTouches,i=o.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),t=0;t<i;++t)(n=s[o[t].identifier])&&(lA(e),n("end",e,o[t]))}function y(e,t,n,r,o,i){var l,d,h,p=c.copy(),m=lI(i||n,t);if(null!=(h=a.call(e,new ca("beforestart",{sourceEvent:n,target:f,identifier:o,active:u,x:m[0],y:m[1],dx:0,dy:0,dispatch:p}),r)))return l=h.x-m[0]||0,d=h.y-m[1]||0,function n(i,a,c){var g,v=m;switch(i){case"start":s[o]=n,g=u++;break;case"end":delete s[o],--u;case"drag":m=lI(c||a,t),g=u}p.call(i,e,new ca(i,{sourceEvent:a,subject:h,target:f,identifier:o,active:g,x:m[0]+l,y:m[1]+d,dx:m[0]-v[0],dy:m[1]-v[1],dispatch:p}),r)}}return f.filter=function(e){return arguments.length?(o="function"==typeof e?e:ci(!!e),f):o},f.container=function(e){return arguments.length?(i="function"==typeof e?e:ci(e),f):i},f.subject=function(e){return arguments.length?(a="function"==typeof e?e:ci(e),f):a},f.touchable=function(e){return arguments.length?(l="function"==typeof e?e:ci(!!e),f):l},f.on=function(){var e=c.on.apply(c,arguments);return e===c?f:e},f.clickDistance=function(e){return arguments.length?(d=(e*=1)*e,f):Math.sqrt(d)},f})().on("start",e=>{let{domNode:t,nodeDragThreshold:n}=l.getState();0===n&&E(e),y.current=!1,d.current=b(e),p.current=t?.getBoundingClientRect()||null,m.current=cF(e.sourceEvent,p.current)}).on("drag",e=>{let t=b(e),{autoPanOnNodeDrag:n,nodeDragThreshold:r}=l.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(y.current=!0),!y.current){if(!v.current&&x.current&&n&&(v.current=!0,w()),!x.current){let n=t.xSnapped-(d?.current?.x??0),o=t.ySnapped-(d?.current?.y??0);Math.sqrt(n*n+o*o)>r&&E(e)}(d.current.x!==t.xSnapped||d.current.y!==t.ySnapped)&&u.current&&x.current&&(g.current=e.sourceEvent,m.current=cF(e.sourceEvent,p.current),f(t))}}).on("end",e=>{if(x.current&&!y.current&&(c(!1),v.current=!1,x.current=!1,cancelAnimationFrame(h.current),u.current)){let{updateNodePositions:t,nodeInternals:n,onNodeDragStop:r,onSelectionDragStop:i}=l.getState(),a=o?r:dN(i);if(t(u.current,!1,!1),a){let[t,r]=db({nodeId:o,dragItems:u.current,nodeInternals:n});a(e.sourceEvent,t,r)}}}).filter(t=>{let o=t.target;return!t.button&&(!n||!dx(o,`.${n}`,e))&&(!r||dx(o,r,e))});return s.call(t),()=>{s.on(".drag",null)}}}},[e,t,n,r,i,l,o,a,b]),s}function dk(){let e=cg();return(0,f.useCallback)(t=>{let{nodeInternals:n,nodeExtent:r,updateNodePositions:o,getNodes:i,snapToGrid:a,snapGrid:l,onError:s,nodesDraggable:c}=e.getState(),u=i().filter(e=>e.selected&&(e.draggable||c&&void 0===e.draggable)),d=a?l[0]:5,f=a?l[1]:5,h=t.isShiftPressed?4:1,p=t.x*d*h,m=t.y*f*h;o(u.map(e=>{if(e.positionAbsolute){let t={x:e.positionAbsolute.x+p,y:e.positionAbsolute.y+m};a&&(t.x=l[0]*Math.round(t.x/l[0]),t.y=l[1]*Math.round(t.y/l[1]));let{positionAbsolute:o,position:i}=dy(e,t,n,r,void 0,s);e.position=i,e.positionAbsolute=o}return e}),!0,!1)},[])}let dj={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var dM=e=>{let t=({id:t,type:n,data:r,xPos:o,yPos:i,xPosOrigin:a,yPosOrigin:l,selected:s,onClick:c,onMouseEnter:u,onMouseMove:d,onMouseLeave:h,onContextMenu:p,onDoubleClick:m,style:g,className:v,isDraggable:x,isSelectable:y,isConnectable:b,isFocusable:w,selectNodesOnDrag:E,sourcePosition:_,targetPosition:N,hidden:S,resizeObserver:k,dragHandle:j,zIndex:M,isParent:R,noDragClassName:C,noPanClassName:A,initialized:P,disableKeyboardA11y:T,ariaLabel:O,rfId:z,hasHandleBounds:D})=>{let I=cg(),L=(0,f.useRef)(null),$=(0,f.useRef)(null),H=(0,f.useRef)(_),F=(0,f.useRef)(N),B=(0,f.useRef)(n),V=y||x||c||u||d||h,U=dk(),W=dE(t,I.getState,u),X=dE(t,I.getState,d),q=dE(t,I.getState,h),K=dE(t,I.getState,p),Y=dE(t,I.getState,m);(0,f.useEffect)(()=>()=>{$.current&&(k?.unobserve($.current),$.current=null)},[]),(0,f.useEffect)(()=>{if(L.current&&!S){let e=L.current;P&&D&&$.current===e||($.current&&k?.unobserve($.current),k?.observe(e),$.current=e)}},[S,P,D]),(0,f.useEffect)(()=>{let e=B.current!==n,r=H.current!==_,o=F.current!==N;L.current&&(e||r||o)&&(e&&(B.current=n),r&&(H.current=_),o&&(F.current=N),I.getState().updateNodeDimensions([{id:t,nodeElement:L.current,forceUpdate:!0}]))},[t,n,_,N]);let G=dS({nodeRef:L,disabled:S||!x,noDragClassName:C,handleSelector:j,nodeId:t,isSelectable:y,selectNodesOnDrag:E});return S?null:f.createElement("div",{className:aD(["react-flow__node",`react-flow__node-${n}`,{[A]:x},v,{selected:s,selectable:y,parent:R,dragging:G}]),ref:L,style:{zIndex:M,transform:`translate(${a}px,${l}px)`,pointerEvents:V?"all":"none",visibility:P?"visible":"hidden",...g},"data-id":t,"data-testid":`rf__node-${t}`,onMouseEnter:W,onMouseMove:X,onMouseLeave:q,onContextMenu:K,onClick:e=>{let{nodeDragThreshold:n}=I.getState();if(y&&(!E||!x||n>0)&&d_({id:t,store:I,nodeRef:L}),c){let n=I.getState().nodeInternals.get(t);n&&c(e,{...n})}},onDoubleClick:Y,onKeyDown:w?e=>{!c$(e)&&!T&&(cD.includes(e.key)&&y?d_({id:t,store:I,unselect:"Escape"===e.key,nodeRef:L}):x&&s&&Object.prototype.hasOwnProperty.call(dj,e.key)&&(I.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~o}, y: ${~~i}`}),U({x:dj[e.key].x,y:dj[e.key].y,isShiftPressed:e.shiftKey})))}:void 0,tabIndex:w?0:void 0,role:w?"button":void 0,"aria-describedby":T?void 0:`${uU}-${z}`,"aria-label":O},f.createElement(c7,{value:t},f.createElement(e,{id:t,data:r,type:n,xPos:o,yPos:i,selected:s,isConnectable:b,sourcePosition:_,targetPosition:N,dragging:G,dragHandle:j,zIndex:M})))};return t.displayName="NodeWrapper",(0,f.memo)(t)};let dR=e=>({...us(e.getNodes().filter(e=>e.selected),e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive});var dC=(0,f.memo)(function({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){let r=cg(),{width:o,height:i,x:a,y:l,transformString:s,userSelectionActive:c}=cm(dR,aX),u=dk(),d=(0,f.useRef)(null);if((0,f.useEffect)(()=>{n||d.current?.focus({preventScroll:!0})},[n]),dS({nodeRef:d}),c||!o||!i)return null;let h=e?t=>{e(t,r.getState().getNodes().filter(e=>e.selected))}:void 0;return f.createElement("div",{className:aD(["react-flow__nodesselection","react-flow__container",t]),style:{transform:s}},f.createElement("div",{ref:d,className:"react-flow__nodesselection-rect",onContextMenu:h,tabIndex:n?void 0:-1,onKeyDown:n?void 0:e=>{Object.prototype.hasOwnProperty.call(dj,e.key)&&u({x:dj[e.key].x,y:dj[e.key].y,isShiftPressed:e.shiftKey})},style:{width:o,height:i,top:l,left:a}}))});let dA=e=>e.nodesSelectionActive,dP=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:i,onPaneScroll:a,deleteKeyCode:l,onMove:s,onMoveStart:c,onMoveEnd:u,selectionKeyCode:d,selectionOnDrag:h,selectionMode:p,onSelectionStart:m,onSelectionEnd:g,multiSelectionKeyCode:v,panActivationKeyCode:x,zoomActivationKeyCode:y,elementsSelectable:b,zoomOnScroll:w,zoomOnPinch:E,panOnScroll:_,panOnScrollSpeed:N,panOnScrollMode:S,zoomOnDoubleClick:k,panOnDrag:j,defaultViewport:M,translateExtent:R,minZoom:C,maxZoom:A,preventScrolling:P,onSelectionContextMenu:T,noWheelClassName:O,noPanClassName:z,disableKeyboardA11y:D})=>{let I=cm(dA),L=uY(d),$=uY(x),H=$||j,F=$||_,B=L||h&&!0!==H;return u7({deleteKeyCode:l,multiSelectionKeyCode:v}),f.createElement(dl,{onMove:s,onMoveStart:c,onMoveEnd:u,onPaneContextMenu:i,elementsSelectable:b,zoomOnScroll:w,zoomOnPinch:E,panOnScroll:F,panOnScrollSpeed:N,panOnScrollMode:S,zoomOnDoubleClick:k,panOnDrag:!L&&H,defaultViewport:M,translateExtent:R,minZoom:C,maxZoom:A,zoomActivationKeyCode:y,preventScrolling:P,noWheelClassName:O,noPanClassName:z},f.createElement(dv,{onSelectionStart:m,onSelectionEnd:g,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:i,onPaneScroll:a,panOnDrag:H,isSelecting:!!B,selectionMode:p},e,I&&f.createElement(dC,{onSelectionContextMenu:T,noPanClassName:z,disableKeyboardA11y:D})))};dP.displayName="FlowRenderer";var dT=(0,f.memo)(dP);function dO(e){let t={input:dM(e.input||uM),default:dM(e.default||uk),output:dM(e.output||uC),group:dM(e.group||uA)},n=Object.keys(e).filter(e=>!["input","default","output","group"].includes(e)).reduce((t,n)=>(t[n]=dM(e[n]||uk),t),{});return{...t,...n}}let dz=({x:e,y:t,width:n,height:r,origin:o})=>!n||!r||o[0]<0||o[1]<0||o[0]>1||o[1]>1?{x:e,y:t}:{x:e-n*o[0],y:t-r*o[1]},dD=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),dI=e=>{let{nodesDraggable:t,nodesConnectable:n,nodesFocusable:r,elementsSelectable:o,updateNodeDimensions:i,onError:a}=cm(dD,aX),l=function(e){return cm((0,f.useCallback)(t=>e?uc(t.nodeInternals,{x:0,y:0,width:t.width,height:t.height},t.transform,!0):t.getNodes(),[e]))}(e.onlyRenderVisibleElements),s=(0,f.useRef)(),u=(0,f.useMemo)(()=>{if("undefined"==typeof ResizeObserver)return null;let e=new ResizeObserver(e=>{i(e.map(e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})))});return s.current=e,e},[]);return(0,f.useEffect)(()=>()=>{s?.current?.disconnect()},[]),f.createElement("div",{className:"react-flow__nodes",style:u9},l.map(i=>{let l=i.type||"default";e.nodeTypes[l]||(a?.("003",ch.error003(l)),l="default");let s=e.nodeTypes[l]||e.nodeTypes.default,d=!!(i.draggable||t&&void 0===i.draggable),h=!!(i.selectable||o&&void 0===i.selectable),p=!!(i.connectable||n&&void 0===i.connectable),m=!!(i.focusable||r&&void 0===i.focusable),g=e.nodeExtent?c_(i.positionAbsolute,e.nodeExtent):i.positionAbsolute,v=g?.x??0,x=g?.y??0,y=dz({x:v,y:x,width:i.width??0,height:i.height??0,origin:e.nodeOrigin});return f.createElement(s,{key:i.id,id:i.id,className:i.className,style:i.style,type:l,data:i.data,sourcePosition:i.sourcePosition||c.Bottom,targetPosition:i.targetPosition||c.Top,hidden:i.hidden,xPos:v,yPos:x,xPosOrigin:y.x,yPosOrigin:y.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!i.selected,isDraggable:d,isSelectable:h,isConnectable:p,isFocusable:m,resizeObserver:u,dragHandle:i.dragHandle,zIndex:i[cz]?.z??0,isParent:!!i[cz]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!i.width&&!!i.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:i.ariaLabel,hasHandleBounds:!!i[cz]?.handleBounds})}))};dI.displayName="NodeRenderer";var dL=(0,f.memo)(dI);let d$=(e,t,n)=>n===c.Left?e-t:n===c.Right?e+t:e,dH=(e,t,n)=>n===c.Top?e-t:n===c.Bottom?e+t:e,dF="react-flow__edgeupdater",dB=({position:e,centerX:t,centerY:n,radius:r=10,onMouseDown:o,onMouseEnter:i,onMouseOut:a,type:l})=>f.createElement("circle",{onMouseDown:o,onMouseEnter:i,onMouseOut:a,className:aD([dF,`${dF}-${l}`]),cx:d$(t,r,e),cy:dH(n,r,e),r:r,stroke:"transparent",fill:"transparent"}),dV=()=>!0;var dU=e=>{let t=({id:t,className:n,type:r,data:o,onClick:i,onEdgeDoubleClick:a,selected:l,animated:s,label:c,labelStyle:u,labelShowBg:d,labelBgStyle:h,labelBgPadding:p,labelBgBorderRadius:m,style:g,source:v,target:x,sourceX:y,sourceY:b,targetX:w,targetY:E,sourcePosition:_,targetPosition:N,elementsSelectable:S,hidden:k,sourceHandleId:j,targetHandleId:M,onContextMenu:R,onMouseEnter:C,onMouseMove:A,onMouseLeave:P,reconnectRadius:T,onReconnect:O,onReconnectStart:z,onReconnectEnd:D,markerEnd:I,markerStart:L,rfId:$,ariaLabel:H,isFocusable:F,isReconnectable:B,pathOptions:V,interactionWidth:U,disableKeyboardA11y:W})=>{let X=(0,f.useRef)(null),[q,K]=(0,f.useState)(!1),[Y,G]=(0,f.useState)(!1),Z=cg(),Q=(0,f.useMemo)(()=>`url('#${un(L,$)}')`,[L,$]),J=(0,f.useMemo)(()=>`url('#${un(I,$)}')`,[I,$]);if(k)return null;let ee=cU(t,Z.getState,a),et=cU(t,Z.getState,R),en=cU(t,Z.getState,C),er=cU(t,Z.getState,A),eo=cU(t,Z.getState,P),ei=(e,n)=>{if(0!==e.button)return;let{edges:r,isValidConnection:o}=Z.getState(),i=n?x:v,a=(n?M:j)||null,l=n?"target":"source",s=r.find(e=>e.id===t);G(!0),z?.(e,s,l),uy({event:e,handleId:a,nodeId:i,onConnect:e=>O?.(s,e),isTarget:n,getState:Z.getState,setState:Z.setState,isValidConnection:o||dV,edgeUpdaterType:l,onReconnectEnd:e=>{G(!1),D?.(e,s,l)}})},ea=()=>K(!0),el=()=>K(!1);return f.createElement("g",{className:aD(["react-flow__edge",`react-flow__edge-${r}`,n,{selected:l,animated:s,inactive:!S&&!i,updating:q}]),onClick:e=>{let{edges:n,addSelectedEdges:r,unselectNodesAndEdges:o,multiSelectionActive:a}=Z.getState(),l=n.find(e=>e.id===t);l&&(S&&(Z.setState({nodesSelectionActive:!1}),l.selected&&a?(o({nodes:[],edges:[l]}),X.current?.blur()):r([t])),i&&i(e,l))},onDoubleClick:ee,onContextMenu:et,onMouseEnter:en,onMouseMove:er,onMouseLeave:eo,onKeyDown:F?e=>{if(!W&&cD.includes(e.key)&&S){let{unselectNodesAndEdges:n,addSelectedEdges:r,edges:o}=Z.getState();"Escape"===e.key?(X.current?.blur(),n({edges:[o.find(e=>e.id===t)]})):r([t])}}:void 0,tabIndex:F?0:void 0,role:F?"button":"img","data-testid":`rf__edge-${t}`,"aria-label":null===H?void 0:H||`Edge from ${v} to ${x}`,"aria-describedby":F?`${uW}-${$}`:void 0,ref:X},!Y&&f.createElement(e,{id:t,source:v,target:x,selected:l,animated:s,label:c,labelStyle:u,labelShowBg:d,labelBgStyle:h,labelBgPadding:p,labelBgBorderRadius:m,data:o,style:g,sourceX:y,sourceY:b,targetX:w,targetY:E,sourcePosition:_,targetPosition:N,sourceHandleId:j,targetHandleId:M,markerStart:Q,markerEnd:J,pathOptions:V,interactionWidth:U}),B&&f.createElement(f.Fragment,null,("source"===B||!0===B)&&f.createElement(dB,{position:_,centerX:y,centerY:b,radius:T,onMouseDown:e=>ei(e,!0),onMouseEnter:ea,onMouseOut:el,type:"source"}),("target"===B||!0===B)&&f.createElement(dB,{position:N,centerX:w,centerY:E,radius:T,onMouseDown:e=>ei(e,!1),onMouseEnter:ea,onMouseOut:el,type:"target"})))};return t.displayName="EdgeWrapper",(0,f.memo)(t)};function dW(e){let t={default:dU(e.default||c6),straight:dU(e.bezier||c2),step:dU(e.step||c1),smoothstep:dU(e.step||c0),simplebezier:dU(e.simplebezier||cY)},n=Object.keys(e).filter(e=>!["default","bezier"].includes(e)).reduce((t,n)=>(t[n]=dU(e[n]||c6),t),{});return{...t,...n}}function dX(e,t,n=null){let r=(n?.x||0)+t.x,o=(n?.y||0)+t.y,i=n?.width||t.width,a=n?.height||t.height;switch(e){case c.Top:return{x:r+i/2,y:o};case c.Right:return{x:r+i,y:o+a/2};case c.Bottom:return{x:r+i/2,y:o+a};case c.Left:return{x:r,y:o+a/2}}}function dq(e,t){return e?1!==e.length&&t?t&&e.find(e=>e.id===t)||null:e[0]:null}let dK=(e,t,n,r,o,i)=>{let a=dX(n,e,t),l=dX(i,r,o);return{sourceX:a.x,sourceY:a.y,targetX:l.x,targetY:l.y}};function dY(e){let t=e?.[cz]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}let dG=[{level:0,isMaxLevel:!0,edges:[]}],dZ={[s.Arrow]:({color:e="none",strokeWidth:t=1})=>f.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[s.ArrowClosed]:({color:e="none",strokeWidth:t=1})=>f.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})},dQ=({id:e,type:t,color:n,width:r=12.5,height:o=12.5,markerUnits:i="strokeWidth",strokeWidth:a,orient:l="auto-start-reverse"})=>{let s=function(e){let t=cg();return(0,f.useMemo)(()=>Object.prototype.hasOwnProperty.call(dZ,e)?dZ[e]:(t.getState().onError?.("009",ch.error009(e)),null),[e])}(t);return s?f.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${o}`,viewBox:"-10 -10 20 20",markerUnits:i,orient:l,refX:"0",refY:"0"},f.createElement(s,{color:n,strokeWidth:a})):null},dJ=({defaultColor:e,rfId:t})=>n=>{let r=[];return n.edges.reduce((n,o)=>([o.markerStart,o.markerEnd].forEach(o=>{if(o&&"object"==typeof o){let i=un(o,t);r.includes(i)||(n.push({id:i,color:o.color||e,...o}),r.push(i))}}),n),[]).sort((e,t)=>e.id.localeCompare(t.id))},d0=({defaultColor:e,rfId:t})=>{let n=cm((0,f.useCallback)(dJ({defaultColor:e,rfId:t}),[e,t]),(e,t)=>!(e.length!==t.length||e.some((e,n)=>e.id!==t[n].id)));return f.createElement("defs",null,n.map(e=>f.createElement(dQ,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient})))};d0.displayName="MarkerDefinitions";var d1=(0,f.memo)(d0);let d2=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),d3=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:r,edgeTypes:i,noPanClassName:a,onEdgeContextMenu:l,onEdgeMouseEnter:s,onEdgeMouseMove:u,onEdgeMouseLeave:d,onEdgeClick:h,onEdgeDoubleClick:p,onReconnect:m,onReconnectStart:g,onReconnectEnd:v,reconnectRadius:x,children:y,disableKeyboardA11y:b})=>{let{edgesFocusable:w,edgesUpdatable:E,elementsSelectable:_,width:N,height:S,connectionMode:k,nodeInternals:j,onError:M}=cm(d2,aX),R=function(e,t,n){return function(e,t,n=!1){let r=-1,o=Object.entries(e.reduce((e,o)=>{let i=cO(o.zIndex),a=i?o.zIndex:0;if(n){let e=t.get(o.target),n=t.get(o.source),r=o.selected||e?.selected||n?.selected,l=Math.max(n?.[cz]?.z||0,e?.[cz]?.z||0,1e3);a=(i?o.zIndex:0)+(r?l:0)}return e[a]?e[a].push(o):e[a]=[o],r=a>r?a:r,e},{})).map(([e,t])=>{let n=+e;return{edges:t,level:n,isMaxLevel:n===r}});return 0===o.length?dG:o}(cm((0,f.useCallback)(n=>e?n.edges.filter(e=>{let r=t.get(e.source),o=t.get(e.target);return r?.width&&r?.height&&o?.width&&o?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:r,targetWidth:o,targetHeight:i,width:a,height:l,transform:s}){let c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+o),y2:Math.max(e.y+r,t.y+i)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);let u=cM({x:(0-s[0])/s[2],y:(0-s[1])/s[2],width:a/s[2],height:l/s[2]});return Math.ceil(Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x))*Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y)))>0}({sourcePos:r.positionAbsolute||{x:0,y:0},targetPos:o.positionAbsolute||{x:0,y:0},sourceWidth:r.width,sourceHeight:r.height,targetWidth:o.width,targetHeight:o.height,width:n.width,height:n.height,transform:n.transform})}):n.edges,[e,t])),t,n)}(t,j,n);return N?f.createElement(f.Fragment,null,R.map(({level:t,edges:n,isMaxLevel:y})=>f.createElement("svg",{key:t,style:{zIndex:t},width:N,height:S,className:"react-flow__edges react-flow__container"},y&&f.createElement(d1,{defaultColor:e,rfId:r}),f.createElement("g",null,n.map(e=>{let[t,n,y]=dY(j.get(e.source)),[N,S,R]=dY(j.get(e.target));if(!y||!R)return null;let C=e.type||"default";i[C]||(M?.("011",ch.error011(C)),C="default");let A=i[C]||i.default,P=k===o.Strict?S.target:(S.target??[]).concat(S.source??[]),T=dq(n.source,e.sourceHandle),O=dq(P,e.targetHandle),z=T?.position||c.Bottom,D=O?.position||c.Top,I=!!(e.focusable||w&&void 0===e.focusable),L=e.reconnectable||e.updatable;if(!T||!O)return M?.("008",ch.error008(T,e)),null;let{sourceX:$,sourceY:H,targetX:F,targetY:B}=dK(t,T,z,N,O,D);return f.createElement(A,{key:e.id,id:e.id,className:aD([e.className,a]),type:C,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:$,sourceY:H,targetX:F,targetY:B,sourcePosition:z,targetPosition:D,elementsSelectable:_,onContextMenu:l,onMouseEnter:s,onMouseMove:u,onMouseLeave:d,onClick:h,onEdgeDoubleClick:p,onReconnect:m,onReconnectStart:g,onReconnectEnd:v,reconnectRadius:x,rfId:r,ariaLabel:e.ariaLabel,isFocusable:I,isReconnectable:void 0!==m&&(L||E&&void 0===L),pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:b})})))),y):null};d3.displayName="EdgeRenderer";var d4=(0,f.memo)(d3);let d5=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function d6({children:e}){let t=cm(d5);return f.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}let d8={[c.Left]:c.Right,[c.Right]:c.Left,[c.Top]:c.Bottom,[c.Bottom]:c.Top},d7=({nodeId:e,handleType:t,style:n,type:r=l.Bezier,CustomComponent:i,connectionStatus:a})=>{let{fromNode:s,handleId:c,toX:u,toY:d,connectionMode:h}=cm((0,f.useCallback)(t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode}),[e]),aX),p=s?.[cz]?.handleBounds,m=p?.[t];if(h===o.Loose&&(m=m||p?.["source"===t?"target":"source"]),!s||!m)return null;let g=c?m.find(e=>e.id===c):m[0],v=g?g.x+g.width/2:(s.width??0)/2,x=g?g.y+g.height/2:s.height??0,y=(s.positionAbsolute?.x??0)+v,b=(s.positionAbsolute?.y??0)+x,w=g?.position,E=w?d8[w]:null;if(!w||!E)return null;if(i)return f.createElement(i,{connectionLineType:r,connectionLineStyle:n,fromNode:s,fromHandle:g,fromX:y,fromY:b,toX:u,toY:d,fromPosition:w,toPosition:E,connectionStatus:a});let _="",N={sourceX:y,sourceY:b,sourcePosition:w,targetX:u,targetY:d,targetPosition:E};return r===l.Bezier?[_]=c5(N):r===l.Step?[_]=cJ({...N,borderRadius:0}):r===l.SmoothStep?[_]=cJ(N):r===l.SimpleBezier?[_]=cK(N):_=`M${y},${b} ${u},${d}`,f.createElement("path",{d:_,fill:"none",className:"react-flow__connection-path",style:n})};d7.displayName="ConnectionLine";let d9=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function fe({containerStyle:e,style:t,type:n,component:r}){let{nodeId:o,handleType:i,nodesConnectable:a,width:l,height:s,connectionStatus:c}=cm(d9,aX);return o&&i&&l&&a?f.createElement("svg",{style:e,width:l,height:s,className:"react-flow__edges react-flow__connectionline react-flow__container"},f.createElement("g",{className:aD(["react-flow__connection",c])},f.createElement(d7,{nodeId:o,handleType:i,style:t,type:n,CustomComponent:r,connectionStatus:c}))):null}function ft(e,t){return(0,f.useRef)(null),cg(),(0,f.useMemo)(()=>t(e),[e])}let fn=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:r,onMoveEnd:o,onInit:i,onNodeClick:a,onEdgeClick:l,onNodeDoubleClick:s,onEdgeDoubleClick:c,onNodeMouseEnter:u,onNodeMouseMove:d,onNodeMouseLeave:h,onNodeContextMenu:p,onSelectionContextMenu:m,onSelectionStart:g,onSelectionEnd:v,connectionLineType:x,connectionLineStyle:y,connectionLineComponent:b,connectionLineContainerStyle:w,selectionKeyCode:E,selectionOnDrag:_,selectionMode:N,multiSelectionKeyCode:S,panActivationKeyCode:k,zoomActivationKeyCode:j,deleteKeyCode:M,onlyRenderVisibleElements:R,elementsSelectable:C,selectNodesOnDrag:A,defaultViewport:P,translateExtent:T,minZoom:O,maxZoom:z,preventScrolling:D,defaultMarkerColor:I,zoomOnScroll:L,zoomOnPinch:$,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:B,zoomOnDoubleClick:V,panOnDrag:U,onPaneClick:W,onPaneMouseEnter:X,onPaneMouseMove:q,onPaneMouseLeave:K,onPaneScroll:Y,onPaneContextMenu:G,onEdgeContextMenu:Z,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:er,reconnectRadius:eo,noDragClassName:ei,noWheelClassName:ea,noPanClassName:el,elevateEdgesOnSelect:es,disableKeyboardA11y:ec,nodeOrigin:eu,nodeExtent:ed,rfId:ef})=>{let eh=ft(e,dO),ep=ft(t,dW);return!function(e){let t=u6(),n=(0,f.useRef)(!1);(0,f.useEffect)(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}(i),f.createElement(dT,{onPaneClick:W,onPaneMouseEnter:X,onPaneMouseMove:q,onPaneMouseLeave:K,onPaneContextMenu:G,onPaneScroll:Y,deleteKeyCode:M,selectionKeyCode:E,selectionOnDrag:_,selectionMode:N,onSelectionStart:g,onSelectionEnd:v,multiSelectionKeyCode:S,panActivationKeyCode:k,zoomActivationKeyCode:j,elementsSelectable:C,onMove:n,onMoveStart:r,onMoveEnd:o,zoomOnScroll:L,zoomOnPinch:$,zoomOnDoubleClick:V,panOnScroll:H,panOnScrollSpeed:F,panOnScrollMode:B,panOnDrag:U,defaultViewport:P,translateExtent:T,minZoom:O,maxZoom:z,onSelectionContextMenu:m,preventScrolling:D,noDragClassName:ei,noWheelClassName:ea,noPanClassName:el,disableKeyboardA11y:ec},f.createElement(d6,null,f.createElement(d4,{edgeTypes:ep,onEdgeClick:l,onEdgeDoubleClick:c,onlyRenderVisibleElements:R,onEdgeContextMenu:Z,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:et,onReconnectStart:en,onReconnectEnd:er,reconnectRadius:eo,defaultMarkerColor:I,noPanClassName:el,elevateEdgesOnSelect:!!es,disableKeyboardA11y:ec,rfId:ef},f.createElement(fe,{style:y,type:x,component:b,containerStyle:w})),f.createElement("div",{className:"react-flow__edgelabel-renderer"}),f.createElement(dL,{nodeTypes:eh,onNodeClick:a,onNodeDoubleClick:s,onNodeMouseEnter:u,onNodeMouseMove:d,onNodeMouseLeave:h,onNodeContextMenu:p,selectNodesOnDrag:A,onlyRenderVisibleElements:R,noPanClassName:el,noDragClassName:ei,disableKeyboardA11y:ec,nodeOrigin:eu,nodeExtent:ed,rfId:ef})))};fn.displayName="GraphView";var fr=(0,f.memo)(fn);let fo=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],fi={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:fo,nodeExtent:fo,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:o.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:cI,isValidConnection:void 0},fa=()=>aW((e,t)=>({...fi,setNodes:n=>{let{nodeInternals:r,nodeOrigin:o,elevateNodesOnSelect:i}=t();e({nodeInternals:uJ(n,r,o,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{let{defaultEdgeOptions:r={}}=t();e({edges:n.map(e=>({...r,...e}))})},setDefaultNodesAndEdges:(n,r)=>{let o=void 0!==n,i=void 0!==r;e({nodeInternals:o?uJ(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map,edges:i?r:[],hasDefaultNodes:o,hasDefaultEdges:i})},updateNodeDimensions:n=>{let{onNodesChange:r,nodeInternals:o,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:l,domNode:s,nodeOrigin:c}=t(),u=s?.querySelector(".react-flow__viewport");if(!u)return;let d=window.getComputedStyle(u),{m22:f}=new window.DOMMatrixReadOnly(d.transform),h=n.reduce((e,t)=>{let n=o.get(t.id);if(n?.hidden)o.set(n.id,{...n,[cz]:{...n[cz],handleBounds:void 0}});else if(n){let r=cw(t.nodeElement);r.width&&r.height&&(n.width!==r.width||n.height!==r.height||t.forceUpdate)&&(o.set(n.id,{...n,[cz]:{...n[cz],handleBounds:{source:dw(".source",t.nodeElement,f,c),target:dw(".target",t.nodeElement,f,c)}},...r}),e.push({id:n.id,type:"dimensions",dimensions:r}))}return e},[]);uQ(o,c);let p=a||i&&!a&&u0(t,{initial:!0,...l});e({nodeInternals:new Map(o),fitViewOnInitDone:p}),h?.length>0&&r?.(h)},updateNodePositions:(e,n=!0,r=!1)=>{let{triggerNodeChanges:o}=t();o(e.map(e=>{let t={id:e.id,type:"position",dragging:r};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t}))},triggerNodeChanges:n=>{let{onNodesChange:r,nodeInternals:o,hasDefaultNodes:i,nodeOrigin:a,getNodes:l,elevateNodesOnSelect:s}=t();n?.length&&(i&&e({nodeInternals:uJ(df(n,l()),o,a,s)}),r?.(n))},addSelectedNodes:n=>{let r,{multiSelectionActive:o,edges:i,getNodes:a}=t(),l=null;o?r=n.map(e=>dh(e,!0)):(r=dp(a(),n),l=dp(i,[])),u1({changedNodes:r,changedEdges:l,get:t,set:e})},addSelectedEdges:n=>{let r,{multiSelectionActive:o,edges:i,getNodes:a}=t(),l=null;o?r=n.map(e=>dh(e,!0)):(r=dp(i,n),l=dp(a(),[])),u1({changedNodes:l,changedEdges:r,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:r}={})=>{let{edges:o,getNodes:i}=t();u1({changedNodes:(n||i()).map(e=>(e.selected=!1,dh(e.id,!1))),changedEdges:(r||o).map(e=>dh(e.id,!1)),get:t,set:e})},setMinZoom:n=>{let{d3Zoom:r,maxZoom:o}=t();r?.scaleExtent([n,o]),e({minZoom:n})},setMaxZoom:n=>{let{d3Zoom:r,minZoom:o}=t();r?.scaleExtent([o,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{let{edges:n,getNodes:r}=t();u1({changedNodes:r().filter(e=>e.selected).map(e=>dh(e.id,!1)),changedEdges:n.filter(e=>e.selected).map(e=>dh(e.id,!1)),get:t,set:e})},setNodeExtent:n=>{let{nodeInternals:r}=t();r.forEach(e=>{e.positionAbsolute=c_(e.position,n)}),e({nodeExtent:n,nodeInternals:new Map(r)})},panBy:e=>{let{transform:n,width:r,height:o,d3Zoom:i,d3Selection:a,translateExtent:l}=t();if(!i||!a||!e.x&&!e.y)return!1;let s=s5.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=i?.constrain()(s,[[0,0],[r,o]],l);return i.transform(a,c),n[0]!==c.x||n[1]!==c.y||n[2]!==c.k},cancelConnection:()=>e({connectionNodeId:fi.connectionNodeId,connectionHandleId:fi.connectionHandleId,connectionHandleType:fi.connectionHandleType,connectionStatus:fi.connectionStatus,connectionStartHandle:fi.connectionStartHandle,connectionEndHandle:fi.connectionEndHandle}),reset:()=>e({...fi})}),Object.is),fl=({children:e})=>{let t=(0,f.useRef)(null);return t.current||(t.current=fa()),f.createElement(cf,{value:t.current},e)};fl.displayName="ReactFlowProvider";let fs=({children:e})=>(0,f.useContext)(cd)?f.createElement(f.Fragment,null,e):f.createElement(fl,null,e);fs.displayName="ReactFlowWrapper";let fc={input:uM,default:uk,output:uC,group:uA},fu={default:c6,straight:c2,step:c1,smoothstep:c0,simplebezier:cY},fd=[0,0],ff=[15,15],fh={x:0,y:0,zoom:1},fp={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},fm=(0,f.forwardRef)(({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,className:s,nodeTypes:c=fc,edgeTypes:u=fu,onNodeClick:d,onEdgeClick:h,onInit:p,onMove:m,onMoveStart:g,onMoveEnd:v,onConnect:x,onConnectStart:y,onConnectEnd:b,onClickConnectStart:w,onClickConnectEnd:E,onNodeMouseEnter:_,onNodeMouseMove:N,onNodeMouseLeave:S,onNodeContextMenu:k,onNodeDoubleClick:j,onNodeDragStart:M,onNodeDrag:R,onNodeDragStop:C,onNodesDelete:A,onEdgesDelete:P,onSelectionChange:T,onSelectionDragStart:O,onSelectionDrag:z,onSelectionDragStop:D,onSelectionContextMenu:I,onSelectionStart:L,onSelectionEnd:$,connectionMode:H=o.Strict,connectionLineType:F=l.Bezier,connectionLineStyle:B,connectionLineComponent:V,connectionLineContainerStyle:U,deleteKeyCode:W="Backspace",selectionKeyCode:X="Shift",selectionOnDrag:q=!1,selectionMode:K=a.Full,panActivationKeyCode:Y="Space",multiSelectionKeyCode:G=cB()?"Meta":"Control",zoomActivationKeyCode:Z=cB()?"Meta":"Control",snapToGrid:Q=!1,snapGrid:J=ff,onlyRenderVisibleElements:ee=!1,selectNodesOnDrag:et=!0,nodesDraggable:en,nodesConnectable:er,nodesFocusable:eo,nodeOrigin:ei=fd,edgesFocusable:ea,edgesUpdatable:el,elementsSelectable:es,defaultViewport:ec=fh,minZoom:eu=.5,maxZoom:ed=2,translateExtent:ef=fo,preventScrolling:eh=!0,nodeExtent:ep,defaultMarkerColor:em="#b1b1b7",zoomOnScroll:eg=!0,zoomOnPinch:ev=!0,panOnScroll:ex=!1,panOnScrollSpeed:ey=.5,panOnScrollMode:eb=i.Free,zoomOnDoubleClick:ew=!0,panOnDrag:eE=!0,onPaneClick:e_,onPaneMouseEnter:eN,onPaneMouseMove:eS,onPaneMouseLeave:ek,onPaneScroll:ej,onPaneContextMenu:eM,children:eR,onEdgeContextMenu:eC,onEdgeDoubleClick:eA,onEdgeMouseEnter:eP,onEdgeMouseMove:eT,onEdgeMouseLeave:eO,onEdgeUpdate:ez,onEdgeUpdateStart:eD,onEdgeUpdateEnd:eI,onReconnect:eL,onReconnectStart:e$,onReconnectEnd:eH,reconnectRadius:eF=10,edgeUpdaterRadius:eB=10,onNodesChange:eV,onEdgesChange:eU,noDragClassName:eW="nodrag",noWheelClassName:eX="nowheel",noPanClassName:eq="nopan",fitView:eK=!1,fitViewOptions:eY,connectOnClick:eG=!0,attributionPosition:eZ,proOptions:eQ,defaultEdgeOptions:eJ,elevateNodesOnSelect:e0=!0,elevateEdgesOnSelect:e1=!1,disableKeyboardA11y:e2=!1,autoPanOnConnect:e3=!0,autoPanOnNodeDrag:e4=!0,connectionRadius:e5=20,isValidConnection:e6,onError:e8,style:e7,id:e9,nodeDragThreshold:te,...tt},tn)=>{let tr=e9||"1";return f.createElement("div",{...tt,style:{...e7,...fp},ref:tn,className:aD(["react-flow",s]),"data-testid":"rf__wrapper",id:e9},f.createElement(fs,null,f.createElement(fr,{onInit:p,onMove:m,onMoveStart:g,onMoveEnd:v,onNodeClick:d,onEdgeClick:h,onNodeMouseEnter:_,onNodeMouseMove:N,onNodeMouseLeave:S,onNodeContextMenu:k,onNodeDoubleClick:j,nodeTypes:c,edgeTypes:u,connectionLineType:F,connectionLineStyle:B,connectionLineComponent:V,connectionLineContainerStyle:U,selectionKeyCode:X,selectionOnDrag:q,selectionMode:K,deleteKeyCode:W,multiSelectionKeyCode:G,panActivationKeyCode:Y,zoomActivationKeyCode:Z,onlyRenderVisibleElements:ee,selectNodesOnDrag:et,defaultViewport:ec,translateExtent:ef,minZoom:eu,maxZoom:ed,preventScrolling:eh,zoomOnScroll:eg,zoomOnPinch:ev,zoomOnDoubleClick:ew,panOnScroll:ex,panOnScrollSpeed:ey,panOnScrollMode:eb,panOnDrag:eE,onPaneClick:e_,onPaneMouseEnter:eN,onPaneMouseMove:eS,onPaneMouseLeave:ek,onPaneScroll:ej,onPaneContextMenu:eM,onSelectionContextMenu:I,onSelectionStart:L,onSelectionEnd:$,onEdgeContextMenu:eC,onEdgeDoubleClick:eA,onEdgeMouseEnter:eP,onEdgeMouseMove:eT,onEdgeMouseLeave:eO,onReconnect:eL??ez,onReconnectStart:e$??eD,onReconnectEnd:eH??eI,reconnectRadius:eF??eB,defaultMarkerColor:em,noDragClassName:eW,noWheelClassName:eX,noPanClassName:eq,elevateEdgesOnSelect:e1,rfId:tr,disableKeyboardA11y:e2,nodeOrigin:ei,nodeExtent:ep}),f.createElement(uF,{nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:x,onConnectStart:y,onConnectEnd:b,onClickConnectStart:w,onClickConnectEnd:E,nodesDraggable:en,nodesConnectable:er,nodesFocusable:eo,edgesFocusable:ea,edgesUpdatable:el,elementsSelectable:es,elevateNodesOnSelect:e0,minZoom:eu,maxZoom:ed,nodeExtent:ep,onNodesChange:eV,onEdgesChange:eU,snapToGrid:Q,snapGrid:J,connectionMode:H,translateExtent:ef,connectOnClick:eG,defaultEdgeOptions:eJ,fitView:eK,fitViewOptions:eY,onNodesDelete:A,onEdgesDelete:P,onNodeDragStart:M,onNodeDrag:R,onNodeDragStop:C,onSelectionDrag:z,onSelectionDragStart:O,onSelectionDragStop:D,noPanClassName:eq,nodeOrigin:ei,rfId:tr,autoPanOnConnect:e3,autoPanOnNodeDrag:e4,onError:e8,connectionRadius:e5,isValidConnection:e6,nodeDragThreshold:te}),f.createElement(uI,{onSelectionChange:T}),eR,f.createElement(cy,{proOptions:eQ,position:eZ}),f.createElement(uK,{rfId:tr,disableKeyboardA11y:e2})))});fm.displayName="ReactFlow";function fg(e){return t=>{let[n,r]=(0,f.useState)(t),o=(0,f.useCallback)(t=>r(n=>e(t,n)),[]);return[n,r,o]}}let fv=fg(df),fx=fg(function(e,t){return dd(e,t)});function fy(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}function fb(){return f.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},f.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function fw(){return f.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},f.createElement("path",{d:"M0 0h32v4.2H0z"}))}function fE(){return f.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},f.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function f_(){return f.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},f.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function fN(){return f.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},f.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let fS=({children:e,className:t,...n})=>f.createElement("button",{type:"button",className:aD(["react-flow__controls-button",t]),...n},e);fS.displayName="ControlButton";let fk=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),fj=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:r=!0,fitViewOptions:o,onZoomIn:i,onZoomOut:a,onFitView:l,onInteractiveChange:s,className:c,children:u,position:d="bottom-left"})=>{let h=cg(),[p,m]=(0,f.useState)(!1),{isInteractive:g,minZoomReached:v,maxZoomReached:x}=cm(fk,fy),{zoomIn:y,zoomOut:b,fitView:w}=u6();return((0,f.useEffect)(()=>{m(!0)},[]),p)?f.createElement(cx,{className:aD(["react-flow__controls",c]),position:d,style:e,"data-testid":"rf__controls"},t&&f.createElement(f.Fragment,null,f.createElement(fS,{onClick:()=>{y(),i?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:x},f.createElement(fb,null)),f.createElement(fS,{onClick:()=>{b(),a?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:v},f.createElement(fw,null))),n&&f.createElement(fS,{className:"react-flow__controls-fitview",onClick:()=>{w(o),l?.()},title:"fit view","aria-label":"fit view"},f.createElement(fE,null)),r&&f.createElement(fS,{className:"react-flow__controls-interactive",onClick:()=>{h.setState({nodesDraggable:!g,nodesConnectable:!g,elementsSelectable:!g}),s?.(!g)},title:"toggle interactivity","aria-label":"toggle interactivity"},g?f.createElement(fN,null):f.createElement(f_,null)),u):null};fj.displayName="Controls";var fM=(0,f.memo)(fj);function fR(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}let fC=({id:e,x:t,y:n,width:r,height:o,style:i,color:a,strokeColor:l,strokeWidth:s,className:c,borderRadius:u,shapeRendering:d,onClick:h,selected:p})=>{let{background:m,backgroundColor:g}=i||{};return f.createElement("rect",{className:aD(["react-flow__minimap-node",{selected:p},c]),x:t,y:n,rx:u,ry:u,width:r,height:o,fill:a||m||g,stroke:l,strokeWidth:s,shapeRendering:d,onClick:h?t=>h(t,e):void 0})};fC.displayName="MiniMapNode";var fA=(0,f.memo)(fC);let fP=e=>e.nodeOrigin,fT=e=>e.getNodes().filter(e=>!e.hidden&&e.width&&e.height),fO=e=>e instanceof Function?e:()=>e;var fz=(0,f.memo)(function({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:r=5,nodeStrokeWidth:o=2,nodeComponent:i=fA,onClick:a}){let l=cm(fT,fR),s=cm(fP),c=fO(t),u=fO(e),d=fO(n),h="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return f.createElement(f.Fragment,null,l.map(e=>{let{x:t,y:n}=ul(e,s).positionAbsolute;return f.createElement(i,{key:e.id,x:t,y:n,width:e.width,height:e.height,style:e.style,selected:e.selected,className:d(e),color:c(e),borderRadius:r,strokeColor:u(e),strokeWidth:o,shapeRendering:h,onClick:a,id:e.id})}))});let fD=e=>{let t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?cA(us(t,e.nodeOrigin),n):n,rfId:e.rfId}};function fI({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:r="#e2e2e2",nodeClassName:o="",nodeBorderRadius:i=5,nodeStrokeWidth:a=2,nodeComponent:l,maskColor:s="rgb(240, 240, 240, 0.6)",maskStrokeColor:c="none",maskStrokeWidth:u=1,position:d="bottom-right",onClick:h,onNodeClick:p,pannable:m=!1,zoomable:g=!1,ariaLabel:v="React Flow mini map",inversePan:x=!1,zoomStep:y=10,offsetScale:b=5}){let w=cg(),E=(0,f.useRef)(null),{boundingRect:_,viewBB:N,rfId:S}=cm(fD,fR),k=e?.width??200,j=e?.height??150,M=Math.max(_.width/k,_.height/j),R=M*k,C=M*j,A=b*M,P=_.x-(R-_.width)/2-A,T=_.y-(C-_.height)/2-A,O=R+2*A,z=C+2*A,D=`react-flow__minimap-desc-${S}`,I=(0,f.useRef)(0);I.current=M,(0,f.useEffect)(()=>{if(E.current){let e=lM(E.current),t=co().on("zoom",m?e=>{let{transform:t,d3Selection:n,d3Zoom:r,translateExtent:o,width:i,height:a}=w.getState();if("mousemove"!==e.sourceEvent.type||!n||!r)return;let l=I.current*Math.max(1,t[2])*(x?-1:1),s={x:t[0]-e.sourceEvent.movementX*l,y:t[1]-e.sourceEvent.movementY*l},c=s5.translate(s.x,s.y).scale(t[2]),u=r.constrain()(c,[[0,0],[i,a]],o);r.transform(n,u)}:null).on("zoom.wheel",g?e=>{let{transform:t,d3Selection:n,d3Zoom:r}=w.getState();if("wheel"!==e.sourceEvent.type||!n||!r)return;let o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*y,i=t[2]*Math.pow(2,o);r.scaleTo(n,i)}:null);return e.call(t),()=>{e.on("zoom",null)}}},[m,g,x,y]);let L=h?e=>{let t=lI(e);h(e,{x:t[0],y:t[1]})}:void 0,$=p?(e,t)=>{p(e,w.getState().nodeInternals.get(t))}:void 0;return f.createElement(cx,{position:d,style:e,className:aD(["react-flow__minimap",t]),"data-testid":"rf__minimap"},f.createElement("svg",{width:k,height:j,viewBox:`${P} ${T} ${O} ${z}`,role:"img","aria-labelledby":D,ref:E,onClick:L},v&&f.createElement("title",{id:D},v),f.createElement(fz,{onClick:$,nodeColor:r,nodeStrokeColor:n,nodeBorderRadius:i,nodeClassName:o,nodeStrokeWidth:a,nodeComponent:l}),f.createElement("path",{className:"react-flow__minimap-mask",d:`M${P-A},${T-A}h${O+2*A}v${z+2*A}h${-O-2*A}z
        M${N.x},${N.y}h${N.width}v${N.height}h${-N.width}z`,fill:s,fillRule:"evenodd",stroke:c,strokeWidth:u,pointerEvents:"none"})))}fI.displayName="MiniMap";var fL=(0,f.memo)(fI);function f$(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}function fH({color:e,dimensions:t,lineWidth:n}){return f.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function fF({color:e,radius:t}){return f.createElement("circle",{cx:t,cy:t,r:t,fill:e})}!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(u||(u={}));let fB={[u.Dots]:"#91919a",[u.Lines]:"#eee",[u.Cross]:"#e2e2e2"},fV={[u.Dots]:1,[u.Lines]:1,[u.Cross]:6},fU=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function fW({id:e,variant:t=u.Dots,gap:n=20,size:r,lineWidth:o=1,offset:i=2,color:a,style:l,className:s}){let c=(0,f.useRef)(null),{transform:d,patternId:h}=cm(fU,f$),p=a||fB[t],m=r||fV[t],g=t===u.Dots,v=t===u.Cross,x=Array.isArray(n)?n:[n,n],y=[x[0]*d[2]||1,x[1]*d[2]||1],b=m*d[2],w=v?[b,b]:y,E=g?[b/i,b/i]:[w[0]/i,w[1]/i];return f.createElement("svg",{className:aD(["react-flow__background",s]),style:{...l,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:c,"data-testid":"rf__background"},f.createElement("pattern",{id:h+e,x:d[0]%y[0],y:d[1]%y[1],width:y[0],height:y[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${E[0]},-${E[1]})`},g?f.createElement(fF,{color:p,radius:b/i}):f.createElement(fH,{dimensions:w,color:p,lineWidth:o})),f.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${h+e})`}))}fW.displayName="Background";var fX=(0,f.memo)(fW);n(7163);let fq=ic("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),fK=ic("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),fY=ic("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),fG=ic("file-output",[["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M4 7V4a2 2 0 0 1 2-2 2 2 0 0 0-2 2",key:"1vk7w2"}],["path",{d:"M4.063 20.999a2 2 0 0 0 2 1L18 22a2 2 0 0 0 2-2V7l-5-5H6",key:"1jink5"}],["path",{d:"m5 11-3 3",key:"1dgrs4"}],["path",{d:"m5 17-3-3h10",key:"1mvvaf"}]]),fZ=[{id:"1",type:"input",position:{x:100,y:100},data:{label:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(im,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"数据输入"})]})},style:{background:"#e3f2fd",border:"2px solid #2196f3",borderRadius:"8px",padding:"10px"}},{id:"2",position:{x:300,y:100},data:{label:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(fq,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"数据验证"})]})},style:{background:"#fff3e0",border:"2px solid #ff9800",borderRadius:"8px",padding:"10px"}},{id:"3",position:{x:500,y:100},data:{label:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(fK,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"智能调度"})]})},style:{background:"#f3e5f5",border:"2px solid #9c27b0",borderRadius:"8px",padding:"10px"}},{id:"4",position:{x:700,y:100},data:{label:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(fY,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"结果优化"})]})},style:{background:"#e8f5e8",border:"2px solid #4caf50",borderRadius:"8px",padding:"10px"}},{id:"5",type:"output",position:{x:900,y:100},data:{label:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(fG,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"输出结果"})]})},style:{background:"#ffebee",border:"2px solid #f44336",borderRadius:"8px",padding:"10px"}}],fQ=[{id:"e1-2",source:"1",target:"2",animated:!0},{id:"e2-3",source:"2",target:"3",animated:!0},{id:"e3-4",source:"3",target:"4",animated:!0},{id:"e4-5",source:"4",target:"5",animated:!0}];function fJ(){let[e,t,n]=fv(fZ),[r,o,i]=fx(fQ),[a,l]=(0,f.useState)(null),s=(0,f.useCallback)(e=>o(t=>uo(e,t)),[o]),c=(0,f.useCallback)((e,t)=>{l(t)},[]);return(0,d.jsxs)("div",{className:"h-full relative",children:[(0,d.jsxs)("div",{className:"absolute top-4 left-4 z-10 flex space-x-2",children:[(0,d.jsxs)(eT,{size:"sm",onClick:()=>{let n={id:`${e.length+1}`,position:{x:400*Math.random()+100,y:300*Math.random()+200},data:{label:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(id,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"新节点"})]})},style:{background:"#f5f5f5",border:"2px solid #9e9e9e",borderRadius:"8px",padding:"10px"}};t(e=>e.concat(n))},children:[(0,d.jsx)(ax,{className:"h-4 w-4 mr-2"}),"添加节点"]}),(0,d.jsxs)(eT,{size:"sm",variant:"outline",children:[(0,d.jsx)(fq,{className:"h-4 w-4 mr-2"}),"数据节点"]}),(0,d.jsxs)(eT,{size:"sm",variant:"outline",children:[(0,d.jsx)(fK,{className:"h-4 w-4 mr-2"}),"处理节点"]})]}),a&&(0,d.jsxs)("div",{className:"absolute top-4 right-4 z-10 w-64 bg-white border rounded-lg shadow-lg p-4",children:[(0,d.jsx)("h3",{className:"font-medium mb-3",children:"节点属性"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"节点名称"}),(0,d.jsx)("input",{type:"text",className:"w-full mt-1 px-3 py-2 border rounded-md text-sm",defaultValue:a.data.label})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"节点类型"}),(0,d.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border rounded-md text-sm",children:[(0,d.jsx)("option",{children:"数据输入"}),(0,d.jsx)("option",{children:"数据处理"}),(0,d.jsx)("option",{children:"逻辑判断"}),(0,d.jsx)("option",{children:"结果输出"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium",children:"描述"}),(0,d.jsx)("textarea",{className:"w-full mt-1 px-3 py-2 border rounded-md text-sm",rows:3,placeholder:"节点功能描述..."})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(eT,{size:"sm",className:"flex-1",children:"保存"}),(0,d.jsx)(eT,{size:"sm",variant:"outline",onClick:()=>l(null),children:"关闭"})]})]})]}),(0,d.jsxs)(fm,{nodes:e,edges:r,onNodesChange:n,onEdgesChange:i,onConnect:s,onNodeClick:c,fitView:!0,className:"bg-gray-50",children:[(0,d.jsx)(fM,{}),(0,d.jsx)(fL,{nodeColor:e=>{switch(e.type){case"input":return"#2196f3";case"output":return"#f44336";default:return"#9e9e9e"}}}),(0,d.jsx)(fX,{variant:u.Dots,gap:12,size:1})]}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 text-sm text-gray-600",children:[(0,d.jsx)("div",{className:"font-medium mb-1",children:"操作说明："}),(0,d.jsx)("div",{children:"• 点击节点查看/编辑属性"}),(0,d.jsx)("div",{children:"• 拖拽节点连接点创建连接"}),(0,d.jsx)("div",{children:"• 使用右下角小地图导航"})]})]})}let f0=ic("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]),f1=ic("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]),f2=ic("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]),f3=ic("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),f4=ic("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);function f5(){let[e,t]=(0,f.useState)(1),n=[{id:1,title:"数据上传",icon:im},{id:2,title:"数据验证",icon:fY},{id:3,title:"智能调度",icon:f0},{id:4,title:"结果确认",icon:ig}],r=[{id:"ORD001",customer:"客户A",material:"钢材",weight:"2.5吨",date:"2024-01-15"},{id:"ORD002",customer:"客户B",material:"水泥",weight:"3.2吨",date:"2024-01-16"},{id:"ORD003",customer:"客户C",material:"砖块",weight:"1.8吨",date:"2024-01-17"}];return(0,d.jsxs)("div",{className:"h-full flex flex-col",children:[(0,d.jsx)("div",{className:"p-6 border-b",children:(0,d.jsx)("div",{className:"flex items-center justify-between",children:n.map((t,r)=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsxs)("div",{className:`flex items-center space-x-2 ${e>=t.id?"text-blue-600":"text-gray-400"}`,children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${e>=t.id?"bg-blue-600 text-white":"bg-gray-200"}`,children:(0,d.jsx)(t.icon,{className:"h-4 w-4"})}),(0,d.jsx)("span",{className:"font-medium text-sm",children:t.title})]}),r<n.length-1&&(0,d.jsx)("div",{className:`w-16 h-px mx-4 ${e>t.id?"bg-blue-600":"bg-gray-300"}`})]},t.id))})}),(0,d.jsxs)("div",{className:"flex-1 p-6",children:[1===e&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"数据上传"}),(0,d.jsx)("p",{className:"text-gray-600",children:"请上传订单明细、物料信息和车辆信息文件"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{name:"订单明细.xlsx",icon:f1,uploaded:!0},{name:"物料信息.xlsx",icon:f2,uploaded:!0},{name:"车辆信息.xlsx",icon:f0,uploaded:!1}].map((e,t)=>(0,d.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center ${e.uploaded?"border-green-300 bg-green-50":"border-gray-300"}`,children:[(0,d.jsx)(e.icon,{className:`h-12 w-12 mx-auto mb-3 ${e.uploaded?"text-green-600":"text-gray-400"}`}),(0,d.jsx)("div",{className:"font-medium",children:e.name}),e.uploaded?(0,d.jsx)("div",{className:"text-green-600 text-sm mt-2",children:"✓ 已上传"}):(0,d.jsxs)(eT,{className:"mt-3",size:"sm",children:[(0,d.jsx)(im,{className:"h-4 w-4 mr-2"}),"上传文件"]})]},t))}),(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsx)(eT,{onClick:()=>t(2),children:"下一步：数据验证"})})]}),2===e&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"数据验证"}),(0,d.jsx)("p",{className:"text-gray-600",children:"系统正在验证上传的数据格式和完整性"}),(0,d.jsxs)(iH,{defaultValue:"orders",className:"w-full",children:[(0,d.jsxs)(iF,{children:[(0,d.jsx)(iB,{value:"orders",children:"订单数据"}),(0,d.jsx)(iB,{value:"vehicles",children:"车辆数据"})]}),(0,d.jsxs)(iV,{value:"orders",className:"space-y-4",children:[(0,d.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-green-800",children:[(0,d.jsx)(fY,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"订单数据验证通过"})]}),(0,d.jsxs)("div",{className:"text-sm text-green-700 mt-1",children:["共 ",r.length," 条订单记录，格式正确"]})]}),(0,d.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"订单号"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"客户"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"物料"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"重量"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"交货日期"})]})}),(0,d.jsx)("tbody",{children:r.map(e=>(0,d.jsxs)("tr",{className:"border-t",children:[(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.id}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.customer}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.material}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.weight}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.date})]},e.id))})]})})]}),(0,d.jsxs)(iV,{value:"vehicles",className:"space-y-4",children:[(0,d.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-yellow-800",children:[(0,d.jsx)(f3,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"车辆数据需要注意"})]}),(0,d.jsx)("div",{className:"text-sm text-yellow-700 mt-1",children:"1 辆车辆状态为维修中，不参与调度"})]}),(0,d.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"车辆编号"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"类型"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"载重"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"状态"})]})}),(0,d.jsx)("tbody",{children:[{id:"VEH001",type:"大货车",capacity:"5吨",status:"可用"},{id:"VEH002",type:"中货车",capacity:"3吨",status:"可用"},{id:"VEH003",type:"小货车",capacity:"2吨",status:"维修中"}].map(e=>(0,d.jsxs)("tr",{className:"border-t",children:[(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.id}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.type}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.capacity}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,d.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${"可用"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.status})})]},e.id))})]})})]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)(eT,{variant:"outline",onClick:()=>t(1),children:"上一步"}),(0,d.jsx)(eT,{onClick:()=>t(3),children:"下一步：开始调度"})]})]}),3===e&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"智能调度"}),(0,d.jsx)("p",{className:"text-gray-600",children:"AI 正在为您生成最优的运输调度方案"}),(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"font-medium",children:"正在计算最优调度方案..."})]}),(0,d.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsx)("div",{children:"✓ 分析订单需求和时间约束"}),(0,d.jsx)("div",{children:"✓ 匹配车辆容量和物料属性"}),(0,d.jsx)("div",{children:"✓ 优化运输路线和成本"}),(0,d.jsx)("div",{className:"animate-pulse",children:"⏳ 生成调度结果..."})]})]}),(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)(eT,{onClick:()=>t(4),className:"px-8",children:"查看调度结果"})})]}),4===e&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"调度结果"}),(0,d.jsx)("p",{className:"text-gray-600",children:"以下是系统生成的最优运输调度方案"}),(0,d.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-green-800",children:[(0,d.jsx)(fY,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"font-medium",children:"调度方案生成成功"})]}),(0,d.jsx)("div",{className:"text-sm text-green-700 mt-1",children:"预计节省运输成本 15%，提高车辆利用率 20%"})]}),(0,d.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"车辆"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"分配订单"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"运输路线"}),(0,d.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"配送日期"})]})}),(0,d.jsx)("tbody",{children:[{vehicle:"VEH001",orders:["ORD001","ORD003"],route:"客户A → 客户C",date:"2024-01-15"},{vehicle:"VEH002",orders:["ORD002"],route:"客户B",date:"2024-01-16"}].map((e,t)=>(0,d.jsxs)("tr",{className:"border-t",children:[(0,d.jsx)("td",{className:"px-4 py-2 text-sm font-medium",children:e.vehicle}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:e.orders.join(", ")}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(f4,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:e.route})]})}),(0,d.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(ab,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:e.date})]})})]},t))})]})}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)(eT,{variant:"outline",onClick:()=>t(3),children:"重新调度"}),(0,d.jsxs)("div",{className:"space-x-2",children:[(0,d.jsxs)(eT,{variant:"outline",children:[(0,d.jsx)(ig,{className:"h-4 w-4 mr-2"}),"导出方案"]}),(0,d.jsx)(eT,{children:"确认并执行"})]})]})]})]})]})}let f6=ic("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),f8=ic("file-code",[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]]);function f7(){let[e,t]=(0,f.useState)(!0),n=`# 订单运输调度系统
# 智能调度算法实现

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple

class TransportScheduler:
    """运输调度器"""
    
    def __init__(self):
        self.orders = []
        self.vehicles = []
        self.schedule_result = []
    
    def load_data(self, orders_file: str, vehicles_file: str):
        """加载订单和车辆数据"""
        try:
            self.orders = pd.read_excel(orders_file)
            self.vehicles = pd.read_excel(vehicles_file)
            return True
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    def validate_data(self) -> Dict[str, bool]:
        """验证数据完整性"""
        validation_result = {
            'orders_valid': False,
            'vehicles_valid': False
        }
        
        # 验证订单数据
        required_order_columns = ['订单号', '客户', '物料', '重量', '交货日期']
        if all(col in self.orders.columns for col in required_order_columns):
            validation_result['orders_valid'] = True
        
        # 验证车辆数据
        required_vehicle_columns = ['车辆编号', '类型', '载重', '状态']
        if all(col in self.vehicles.columns for col in required_vehicle_columns):
            validation_result['vehicles_valid'] = True
        
        return validation_result
    
    def calculate_optimal_schedule(self) -> List[Dict]:
        """计算最优调度方案"""
        available_vehicles = self.vehicles[
            self.vehicles['状态'] == '可用'
        ].copy()
        
        schedule = []
        
        for _, vehicle in available_vehicles.iterrows():
            vehicle_capacity = float(vehicle['载重'].replace('吨', ''))
            assigned_orders = []
            current_load = 0
            
            for _, order in self.orders.iterrows():
                order_weight = float(order['重量'].replace('吨', ''))
                
                if current_load + order_weight <= vehicle_capacity:
                    assigned_orders.append(order['订单号'])
                    current_load += order_weight
            
            if assigned_orders:
                schedule.append({
                    'vehicle': vehicle['车辆编号'],
                    'orders': assigned_orders,
                    'total_weight': current_load,
                    'utilization': (current_load / vehicle_capacity) * 100
                })
        
        return schedule
    
    def export_schedule(self, filename: str):
        """导出调度结果"""
        if self.schedule_result:
            df = pd.DataFrame(self.schedule_result)
            df.to_excel(filename, index=False)
            return True
        return False

# 使用示例
if __name__ == "__main__":
    scheduler = TransportScheduler()
    
    # 加载数据
    if scheduler.load_data("orders.xlsx", "vehicles.xlsx"):
        print("数据加载成功")
        
        # 验证数据
        validation = scheduler.validate_data()
        if all(validation.values()):
            print("数据验证通过")
            
            # 计算调度方案
            schedule = scheduler.calculate_optimal_schedule()
            scheduler.schedule_result = schedule
            
            print(f"生成调度方案，共 {len(schedule)} 个分配")
            
            # 导出结果
            scheduler.export_schedule("schedule_result.xlsx")
            print("调度结果已导出")
        else:
            print("数据验证失败")
    else:
        print("数据加载失败")`,r=`# 配置文件
# config.yaml

# 数据库配置
database:
  host: localhost
  port: 5432
  name: transport_db
  user: admin
  password: password

# 调度算法参数
scheduler:
  # 车辆利用率目标 (%)
  target_utilization: 85
  
  # 最大配送距离 (km)
  max_delivery_distance: 200
  
  # 时间窗口容忍度 (小时)
  time_window_tolerance: 2
  
  # 优化目标权重
  weights:
    cost: 0.4
    time: 0.3
    utilization: 0.3

# API 配置
api:
  host: 0.0.0.0
  port: 8000
  debug: true
  
# 文件上传配置
upload:
  max_file_size: 10MB
  allowed_extensions:
    - xlsx
    - xls
    - csv
  
# 日志配置
logging:
  level: INFO
  file: logs/scheduler.log
  max_size: 100MB
  backup_count: 5`,o=`-- 数据库表结构
-- 订单表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    material_type VARCHAR(100) NOT NULL,
    weight DECIMAL(10,2) NOT NULL,
    delivery_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 车辆表
CREATE TABLE vehicles (
    id SERIAL PRIMARY KEY,
    vehicle_no VARCHAR(50) UNIQUE NOT NULL,
    vehicle_type VARCHAR(50) NOT NULL,
    capacity DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 调度结果表
CREATE TABLE schedules (
    id SERIAL PRIMARY KEY,
    vehicle_id INTEGER REFERENCES vehicles(id),
    order_ids INTEGER[] NOT NULL,
    route TEXT,
    scheduled_date DATE NOT NULL,
    total_weight DECIMAL(10,2),
    utilization_rate DECIMAL(5,2),
    status VARCHAR(20) DEFAULT 'planned',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_orders_delivery_date ON orders(delivery_date);
CREATE INDEX idx_vehicles_status ON vehicles(status);
CREATE INDEX idx_schedules_scheduled_date ON schedules(scheduled_date);

-- 插入示例数据
INSERT INTO orders (order_no, customer_name, material_type, weight, delivery_date) VALUES
('ORD001', '客户A', '钢材', 2.5, '2024-01-15'),
('ORD002', '客户B', '水泥', 3.2, '2024-01-16'),
('ORD003', '客户C', '砖块', 1.8, '2024-01-17');

INSERT INTO vehicles (vehicle_no, vehicle_type, capacity, status) VALUES
('VEH001', '大货车', 5.0, 'available'),
('VEH002', '中货车', 3.0, 'available'),
('VEH003', '小货车', 2.0, 'maintenance');`,i=e=>{navigator.clipboard.writeText(e)},a=({code:n,language:r})=>(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsxs)("div",{className:"absolute top-3 right-3 flex space-x-2",children:[(0,d.jsx)(eT,{size:"sm",variant:"outline",onClick:()=>t(!e),children:e?(0,d.jsx)(f6,{className:"h-4 w-4"}):(0,d.jsx)(aM,{className:"h-4 w-4"})}),(0,d.jsx)(eT,{size:"sm",variant:"outline",onClick:()=>i(n),children:(0,d.jsx)(ih,{className:"h-4 w-4"})})]}),(0,d.jsx)(ah,{className:"h-[600px]",children:(0,d.jsxs)("pre",{className:"p-4 text-sm font-mono bg-gray-900 text-gray-100 rounded-lg overflow-x-auto",children:[e&&(0,d.jsx)("div",{className:"float-left pr-4 text-gray-500 select-none",children:n.split("\n").map((e,t)=>(0,d.jsx)("div",{className:"text-right",children:t+1},t))}),(0,d.jsx)("code",{className:`language-${r}`,children:n})]})})]});return(0,d.jsxs)("div",{className:"h-full flex flex-col",children:[(0,d.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"font-medium",children:"生成的应用代码"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(eT,{size:"sm",variant:"outline",children:[(0,d.jsx)(ig,{className:"h-4 w-4 mr-2"}),"下载代码"]}),(0,d.jsxs)(eT,{size:"sm",children:[(0,d.jsx)(f8,{className:"h-4 w-4 mr-2"}),"部署应用"]})]})]}),(0,d.jsx)("div",{className:"flex-1 p-4",children:(0,d.jsxs)(iH,{defaultValue:"python",className:"h-full flex flex-col",children:[(0,d.jsxs)(iF,{className:"grid w-full grid-cols-3",children:[(0,d.jsxs)(iB,{value:"python",className:"flex items-center space-x-2",children:[(0,d.jsx)(f8,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Python 算法"})]}),(0,d.jsxs)(iB,{value:"config",className:"flex items-center space-x-2",children:[(0,d.jsx)(id,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"配置文件"})]}),(0,d.jsxs)(iB,{value:"sql",className:"flex items-center space-x-2",children:[(0,d.jsx)(fq,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"数据库"})]})]}),(0,d.jsx)(iV,{value:"python",className:"flex-1 mt-4",children:(0,d.jsx)(a,{code:n,language:"python"})}),(0,d.jsx)(iV,{value:"config",className:"flex-1 mt-4",children:(0,d.jsx)(a,{code:r,language:"yaml"})}),(0,d.jsx)(iV,{value:"sql",className:"flex-1 mt-4",children:(0,d.jsx)(a,{code:o,language:"sql"})})]})})]})}function f9(){let{canvas:e,setCanvasMode:t}=i_();return(0,d.jsxs)("div",{className:"flex-1 flex flex-col bg-gray-50",children:[(0,d.jsxs)("div",{className:"h-14 bg-white border-b flex items-center justify-between px-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("h2",{className:"font-medium",children:"应用构建器"}),(0,d.jsx)("div",{className:"h-4 w-px bg-gray-300"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"实时预览 • 自动保存"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)(eT,{variant:"outline",size:"sm",children:[(0,d.jsx)(id,{className:"h-4 w-4 mr-2"}),"配置"]}),(0,d.jsxs)(eT,{variant:"outline",size:"sm",children:[(0,d.jsx)(aT,{className:"h-4 w-4 mr-2"}),"全屏"]}),(0,d.jsxs)(eT,{size:"sm",className:"bg-green-600 hover:bg-green-700",children:[(0,d.jsx)(iv,{className:"h-4 w-4 mr-2"}),"运行测试"]})]})]}),(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)(iH,{value:e.mode,onValueChange:e=>t(e),className:"h-full flex flex-col",children:[(0,d.jsxs)(iF,{className:"grid w-full grid-cols-3 mx-6 mt-4 mb-0",children:[(0,d.jsxs)(iB,{value:"flow",className:"flex items-center space-x-2",children:[(0,d.jsx)(aO,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"流程设计"})]}),(0,d.jsxs)(iB,{value:"preview",className:"flex items-center space-x-2",children:[(0,d.jsx)(aM,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"应用预览"})]}),(0,d.jsxs)(iB,{value:"code",className:"flex items-center space-x-2",children:[(0,d.jsx)(az,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"代码查看"})]})]}),(0,d.jsx)(iV,{value:"flow",className:"flex-1 m-6 mt-4",children:(0,d.jsx)("div",{className:"h-full bg-white rounded-lg border shadow-sm",children:(0,d.jsx)(fJ,{})})}),(0,d.jsx)(iV,{value:"preview",className:"flex-1 m-6 mt-4",children:(0,d.jsx)("div",{className:"h-full bg-white rounded-lg border shadow-sm",children:(0,d.jsx)(f5,{})})}),(0,d.jsx)(iV,{value:"code",className:"flex-1 m-6 mt-4",children:(0,d.jsx)("div",{className:"h-full bg-white rounded-lg border shadow-sm",children:(0,d.jsx)(f7,{})})})]})})]})}function he(){return(0,d.jsxs)("div",{className:"h-screen flex flex-col bg-gray-50",children:[(0,d.jsx)(iN,{}),(0,d.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,d.jsx)(aP,{}),(0,d.jsx)(f9,{})]})]})}},6341:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getPreviouslyRevalidatedTags:function(){return v},getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return h}});let r=n(9551),o=n(1959),i=n(2437),a=n(4396),l=n(8034),s=n(5526),c=n(2887),u=n(4722),d=n(6143),f=n(7912);function h(e,t,n){let o=(0,r.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let r=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),i=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(r||i||t.includes(e)||n&&Object.keys(n.groups).includes(e))&&delete o.query[e]}e.url=(0,r.format)(o)}function p(e,t,n){if(!n)return e;for(let r of Object.keys(n.groups)){let o,{optional:i,repeat:a}=n.groups[r],l=`[${a?"...":""}${r}]`;i&&(l=`[${l}]`);let s=t[r];o=Array.isArray(s)?s.map(e=>e&&encodeURIComponent(e)).join("/"):s?encodeURIComponent(s):"",e=e.replaceAll(l,o)}return e}function m(e,t,n,r){let o={};for(let i of Object.keys(t.groups)){let a=e[i];"string"==typeof a?a=(0,u.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(u.normalizeRscURL));let l=n[i],s=t.groups[i].optional;if((Array.isArray(l)?l.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(l))||void 0===a&&!(s&&r))return{params:{},hasValidParams:!1};s&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${i}]]`))&&(a=void 0,delete e[i]),a&&"string"==typeof a&&t.groups[i].repeat&&(a=a.split("/")),a&&(o[i]=a)}return{params:o,hasValidParams:!0}}function g({page:e,i18n:t,basePath:n,rewrites:r,pageIsDynamic:u,trailingSlash:d,caseSensitive:g}){let v,x,y;return u&&(v=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),y=(x=(0,l.getRouteMatcher)(v))(e)),{handleRewrites:function(a,l){let f={},h=l.pathname,p=r=>{let c=(0,i.getPathMatch)(r.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!l.pathname)return!1;let p=c(l.pathname);if((r.has||r.missing)&&p){let e=(0,s.matchHas)(a,l.query,r.has,r.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:i,destQuery:a}=(0,s.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:p,query:l.query});if(i.protocol)return!0;if(Object.assign(f,a,p),Object.assign(l.query,i.query),delete i.query,Object.assign(l,i),!(h=l.pathname))return!1;if(n&&(h=h.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,o.normalizeLocalePath)(h,t.locales);h=e.pathname,l.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(h===e)return!0;if(u&&x){let e=x(h);if(e)return l.query={...l.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])p(e);if(h!==e){let t=!1;for(let e of r.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(h||"");return t===(0,c.removeTrailingSlash)(e)||(null==x?void 0:x(t))})()){for(let e of r.fallback||[])if(t=p(e))break}}return f},defaultRouteRegex:v,dynamicRouteMatcher:x,defaultRouteMatches:y,getParamsFromRouteMatches:function(e){if(!v)return null;let{groups:t,routeKeys:n}=v,r=(0,l.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let n=(0,f.normalizeNextQueryParam)(e);n&&(r[n]=t,delete r[e])}let o={};for(let e of Object.keys(n)){let i=n[e];if(!i)continue;let a=t[i],l=r[e];if(!a.optional&&!l)return null;o[a.pos]=l}return o}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>v&&y?m(e,v,y,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>h(e,t,v),interpolateDynamicPath:(e,t)=>p(e,t,v)}}function v(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,n){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},i=t.split(r),a=(n||{}).decode||e,l=0;l<i.length;l++){var s=i[l],c=s.indexOf("=");if(!(c<0)){var u=s.substr(0,c).trim(),d=s.substr(++c,s.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==o[u]&&(o[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return o},t.serialize=function(e,t,r){var i=r||{},a=i.encode||n;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var l=a(t);if(l&&!o.test(l))throw TypeError("argument val is invalid");var s=e+"="+l;if(null!=i.maxAge){var c=i.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(c)}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");s+="; Domain="+i.domain}if(i.path){if(!o.test(i.path))throw TypeError("option path is invalid");s+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");s+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(s+="; HttpOnly"),i.secure&&(s+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return s};var e=decodeURIComponent,n=encodeURIComponent,r=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6759:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return i}});let r=n(2785),o=n(3736);function i(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},7163:()=>{},7379:(e,t,n)=>{"use strict";e.exports=n(3332)},8034:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let r=n(4827);function o(e){let{re:t,groups:n}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(n)){let n=o[t.pos];void 0!==n&&(t.repeat?a[e]=n.split("/").map(e=>i(e)):a[e]=i(n))}return a}}},8212:(e,t,n)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=n(6415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},8304:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return l},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return s},isMetadataPage:function(){return d},isMetadataRoute:function(){return f},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return u}});let r=n(2958),o=n(4722),i=n(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},l=["js","jsx","ts","tsx"],s=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,n){let o=(n?"":"?")+"$",i=`\\d?${n?"":"(-\\w{6})?"}`,l=[RegExp(`^[\\\\/]robots${s(t.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${s(t.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${s(["xml"],t)}${o}`),RegExp(`[\\\\/]${a.icon.filename}${i}${s(a.icon.extensions,t)}${o}`),RegExp(`[\\\\/]${a.apple.filename}${i}${s(a.apple.extensions,t)}${o}`),RegExp(`[\\\\/]${a.openGraph.filename}${i}${s(a.openGraph.extensions,t)}${o}`),RegExp(`[\\\\/]${a.twitter.filename}${i}${s(a.twitter.extensions,t)}${o}`)],c=(0,r.normalizePathSep)(e);return l.some(e=>e.test(c))}function u(e){let t=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,i.isAppRouteRoute)(e)&&c(e,[],!1)}function f(e){let t=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,i.isAppRouteRoute)(e)&&c(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9344:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6346,23)),Promise.resolve().then(n.t.bind(n,7924,23)),Promise.resolve().then(n.t.bind(n,5656,23)),Promise.resolve().then(n.t.bind(n,99,23)),Promise.resolve().then(n.t.bind(n,8243,23)),Promise.resolve().then(n.t.bind(n,8827,23)),Promise.resolve().then(n.t.bind(n,2763,23)),Promise.resolve().then(n.t.bind(n,7173,23))},9551:e=>{"use strict";e.exports=require("url")},9679:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c});var r=n(5239),o=n(8088),i=n(8170),a=n.n(i),l=n(893),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);n.d(t,s);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,1204)),"/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,4431)),"/Users/<USER>/Projects/awoprojects/ideaflow/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx"],d={require:n,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9733:(e,t,n)=>{"use strict";e.exports=n(907)}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[447,169],()=>n(9679));module.exports=r})();