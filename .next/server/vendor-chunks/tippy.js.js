"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tippy.js";
exports.ids = ["vendor-chunks/tippy.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/tippy.js/dist/tippy.esm.js":
/*!*************************************************!*\
  !*** ./node_modules/tippy.js/dist/tippy.esm.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateFill: () => (/* binding */ animateFill),\n/* harmony export */   createSingleton: () => (/* binding */ createSingleton),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   delegate: () => (/* binding */ delegate),\n/* harmony export */   followCursor: () => (/* binding */ followCursor),\n/* harmony export */   hideAll: () => (/* binding */ hideAll),\n/* harmony export */   inlinePositioning: () => (/* binding */ inlinePositioning),\n/* harmony export */   roundArrow: () => (/* binding */ ROUND_ARROW),\n/* harmony export */   sticky: () => (/* binding */ sticky)\n/* harmony export */ });\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/@popperjs/core/lib/popper.js\");\n/* harmony import */ var _popperjs_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @popperjs/core */ \"(ssr)/./node_modules/@popperjs/core/lib/modifiers/applyStyles.js\");\n/**!\n* tippy.js v6.3.7\n* (c) 2017-2021 atomiks\n* MIT License\n*/\n\n\nvar ROUND_ARROW = '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\nvar BOX_CLASS = \"tippy-box\";\nvar CONTENT_CLASS = \"tippy-content\";\nvar BACKDROP_CLASS = \"tippy-backdrop\";\nvar ARROW_CLASS = \"tippy-arrow\";\nvar SVG_ARROW_CLASS = \"tippy-svg-arrow\";\nvar TOUCH_OPTIONS = {\n  passive: true,\n  capture: true\n};\nvar TIPPY_DEFAULT_APPEND_TO = function TIPPY_DEFAULT_APPEND_TO() {\n  return document.body;\n};\n\nfunction hasOwnProperty(obj, key) {\n  return {}.hasOwnProperty.call(obj, key);\n}\nfunction getValueAtIndexOrReturn(value, index, defaultValue) {\n  if (Array.isArray(value)) {\n    var v = value[index];\n    return v == null ? Array.isArray(defaultValue) ? defaultValue[index] : defaultValue : v;\n  }\n\n  return value;\n}\nfunction isType(value, type) {\n  var str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(type + \"]\") > -1;\n}\nfunction invokeWithArgsOrReturn(value, args) {\n  return typeof value === 'function' ? value.apply(void 0, args) : value;\n}\nfunction debounce(fn, ms) {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  var timeout;\n  return function (arg) {\n    clearTimeout(timeout);\n    timeout = setTimeout(function () {\n      fn(arg);\n    }, ms);\n  };\n}\nfunction removeProperties(obj, keys) {\n  var clone = Object.assign({}, obj);\n  keys.forEach(function (key) {\n    delete clone[key];\n  });\n  return clone;\n}\nfunction splitBySpaces(value) {\n  return value.split(/\\s+/).filter(Boolean);\n}\nfunction normalizeToArray(value) {\n  return [].concat(value);\n}\nfunction pushIfUnique(arr, value) {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\nfunction unique(arr) {\n  return arr.filter(function (item, index) {\n    return arr.indexOf(item) === index;\n  });\n}\nfunction getBasePlacement(placement) {\n  return placement.split('-')[0];\n}\nfunction arrayFrom(value) {\n  return [].slice.call(value);\n}\nfunction removeUndefinedProps(obj) {\n  return Object.keys(obj).reduce(function (acc, key) {\n    if (obj[key] !== undefined) {\n      acc[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction div() {\n  return document.createElement('div');\n}\nfunction isElement(value) {\n  return ['Element', 'Fragment'].some(function (type) {\n    return isType(value, type);\n  });\n}\nfunction isNodeList(value) {\n  return isType(value, 'NodeList');\n}\nfunction isMouseEvent(value) {\n  return isType(value, 'MouseEvent');\n}\nfunction isReferenceElement(value) {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\nfunction getArrayOfElements(value) {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\nfunction setTransitionDuration(els, value) {\n  els.forEach(function (el) {\n    if (el) {\n      el.style.transitionDuration = value + \"ms\";\n    }\n  });\n}\nfunction setVisibilityState(els, state) {\n  els.forEach(function (el) {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\nfunction getOwnerDocument(elementOrElements) {\n  var _element$ownerDocumen;\n\n  var _normalizeToArray = normalizeToArray(elementOrElements),\n      element = _normalizeToArray[0]; // Elements created via a <template> have an ownerDocument with no reference to the body\n\n\n  return element != null && (_element$ownerDocumen = element.ownerDocument) != null && _element$ownerDocumen.body ? element.ownerDocument : document;\n}\nfunction isCursorOutsideInteractiveBorder(popperTreeData, event) {\n  var clientX = event.clientX,\n      clientY = event.clientY;\n  return popperTreeData.every(function (_ref) {\n    var popperRect = _ref.popperRect,\n        popperState = _ref.popperState,\n        props = _ref.props;\n    var interactiveBorder = props.interactiveBorder;\n    var basePlacement = getBasePlacement(popperState.placement);\n    var offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    var topDistance = basePlacement === 'bottom' ? offsetData.top.y : 0;\n    var bottomDistance = basePlacement === 'top' ? offsetData.bottom.y : 0;\n    var leftDistance = basePlacement === 'right' ? offsetData.left.x : 0;\n    var rightDistance = basePlacement === 'left' ? offsetData.right.x : 0;\n    var exceedsTop = popperRect.top - clientY + topDistance > interactiveBorder;\n    var exceedsBottom = clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    var exceedsLeft = popperRect.left - clientX + leftDistance > interactiveBorder;\n    var exceedsRight = clientX - popperRect.right - rightDistance > interactiveBorder;\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\nfunction updateTransitionEndListener(box, action, listener) {\n  var method = action + \"EventListener\"; // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n\n  ['transitionend', 'webkitTransitionEnd'].forEach(function (event) {\n    box[method](event, listener);\n  });\n}\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\n\nfunction actualContains(parent, child) {\n  var target = child;\n\n  while (target) {\n    var _target$getRootNode;\n\n    if (parent.contains(target)) {\n      return true;\n    }\n\n    target = target.getRootNode == null ? void 0 : (_target$getRootNode = target.getRootNode()) == null ? void 0 : _target$getRootNode.host;\n  }\n\n  return false;\n}\n\nvar currentInput = {\n  isTouch: false\n};\nvar lastMouseMoveTime = 0;\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\n\nfunction onDocumentTouchStart() {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\n\nfunction onDocumentMouseMove() {\n  var now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\n\nfunction onWindowBlur() {\n  var activeElement = document.activeElement;\n\n  if (isReferenceElement(activeElement)) {\n    var instance = activeElement._tippy;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\nfunction bindGlobalEventListeners() {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nvar isIE11 = isBrowser ? // @ts-ignore\n!!window.msCrypto : false;\n\nfunction createMemoryLeakWarning(method) {\n  var txt = method === 'destroy' ? 'n already-' : ' ';\n  return [method + \"() was called on a\" + txt + \"destroyed instance. This is a no-op but\", 'indicates a potential memory leak.'].join(' ');\n}\nfunction clean(value) {\n  var spacesAndTabs = /[ \\t]{2,}/g;\n  var lineStartWithSpaces = /^[ \\t]*/gm;\n  return value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\n}\n\nfunction getDevMessage(message) {\n  return clean(\"\\n  %ctippy.js\\n\\n  %c\" + clean(message) + \"\\n\\n  %c\\uD83D\\uDC77\\u200D This is a development-only message. It will be removed in production.\\n  \");\n}\n\nfunction getFormattedMessage(message) {\n  return [getDevMessage(message), // title\n  'color: #00C584; font-size: 1.3em; font-weight: bold;', // message\n  'line-height: 1.5', // footer\n  'color: #a6a095;'];\n} // Assume warnings and errors never have the same message\n\nvar visitedMessages;\n\nif (true) {\n  resetVisitedMessages();\n}\n\nfunction resetVisitedMessages() {\n  visitedMessages = new Set();\n}\nfunction warnWhen(condition, message) {\n  if (condition && !visitedMessages.has(message)) {\n    var _console;\n\n    visitedMessages.add(message);\n\n    (_console = console).warn.apply(_console, getFormattedMessage(message));\n  }\n}\nfunction errorWhen(condition, message) {\n  if (condition && !visitedMessages.has(message)) {\n    var _console2;\n\n    visitedMessages.add(message);\n\n    (_console2 = console).error.apply(_console2, getFormattedMessage(message));\n  }\n}\nfunction validateTargets(targets) {\n  var didPassFalsyValue = !targets;\n  var didPassPlainObject = Object.prototype.toString.call(targets) === '[object Object]' && !targets.addEventListener;\n  errorWhen(didPassFalsyValue, ['tippy() was passed', '`' + String(targets) + '`', 'as its targets (first) argument. Valid types are: String, Element,', 'Element[], or NodeList.'].join(' '));\n  errorWhen(didPassPlainObject, ['tippy() was passed a plain object which is not supported as an argument', 'for virtual positioning. Use props.getReferenceClientRect instead.'].join(' '));\n}\n\nvar pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false\n};\nvar renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999\n};\nvar defaultProps = Object.assign({\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto'\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate: function onAfterUpdate() {},\n  onBeforeUpdate: function onBeforeUpdate() {},\n  onCreate: function onCreate() {},\n  onDestroy: function onDestroy() {},\n  onHidden: function onHidden() {},\n  onHide: function onHide() {},\n  onMount: function onMount() {},\n  onShow: function onShow() {},\n  onShown: function onShown() {},\n  onTrigger: function onTrigger() {},\n  onUntrigger: function onUntrigger() {},\n  onClickOutside: function onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null\n}, pluginProps, renderProps);\nvar defaultKeys = Object.keys(defaultProps);\nvar setDefaultProps = function setDefaultProps(partialProps) {\n  /* istanbul ignore else */\n  if (true) {\n    validateProps(partialProps, []);\n  }\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (key) {\n    defaultProps[key] = partialProps[key];\n  });\n};\nfunction getExtendedPassedProps(passedProps) {\n  var plugins = passedProps.plugins || [];\n  var pluginProps = plugins.reduce(function (acc, plugin) {\n    var name = plugin.name,\n        defaultValue = plugin.defaultValue;\n\n    if (name) {\n      var _name;\n\n      acc[name] = passedProps[name] !== undefined ? passedProps[name] : (_name = defaultProps[name]) != null ? _name : defaultValue;\n    }\n\n    return acc;\n  }, {});\n  return Object.assign({}, passedProps, pluginProps);\n}\nfunction getDataAttributeProps(reference, plugins) {\n  var propKeys = plugins ? Object.keys(getExtendedPassedProps(Object.assign({}, defaultProps, {\n    plugins: plugins\n  }))) : defaultKeys;\n  var props = propKeys.reduce(function (acc, key) {\n    var valueAsString = (reference.getAttribute(\"data-tippy-\" + key) || '').trim();\n\n    if (!valueAsString) {\n      return acc;\n    }\n\n    if (key === 'content') {\n      acc[key] = valueAsString;\n    } else {\n      try {\n        acc[key] = JSON.parse(valueAsString);\n      } catch (e) {\n        acc[key] = valueAsString;\n      }\n    }\n\n    return acc;\n  }, {});\n  return props;\n}\nfunction evaluateProps(reference, props) {\n  var out = Object.assign({}, props, {\n    content: invokeWithArgsOrReturn(props.content, [reference])\n  }, props.ignoreAttributes ? {} : getDataAttributeProps(reference, props.plugins));\n  out.aria = Object.assign({}, defaultProps.aria, out.aria);\n  out.aria = {\n    expanded: out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content: out.aria.content === 'auto' ? props.interactive ? null : 'describedby' : out.aria.content\n  };\n  return out;\n}\nfunction validateProps(partialProps, plugins) {\n  if (partialProps === void 0) {\n    partialProps = {};\n  }\n\n  if (plugins === void 0) {\n    plugins = [];\n  }\n\n  var keys = Object.keys(partialProps);\n  keys.forEach(function (prop) {\n    var nonPluginProps = removeProperties(defaultProps, Object.keys(pluginProps));\n    var didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop); // Check if the prop exists in `plugins`\n\n    if (didPassUnknownProp) {\n      didPassUnknownProp = plugins.filter(function (plugin) {\n        return plugin.name === prop;\n      }).length === 0;\n    }\n\n    warnWhen(didPassUnknownProp, [\"`\" + prop + \"`\", \"is not a valid prop. You may have spelled it incorrectly, or if it's\", 'a plugin, forgot to pass it in an array as props.plugins.', '\\n\\n', 'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n', 'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/'].join(' '));\n  });\n}\n\nvar innerHTML = function innerHTML() {\n  return 'innerHTML';\n};\n\nfunction dangerouslySetInnerHTML(element, html) {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value) {\n  var arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value);\n    }\n  }\n\n  return arrow;\n}\n\nfunction setContent(content, props) {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\nfunction getChildren(popper) {\n  var box = popper.firstElementChild;\n  var boxChildren = arrayFrom(box.children);\n  return {\n    box: box,\n    content: boxChildren.find(function (node) {\n      return node.classList.contains(CONTENT_CLASS);\n    }),\n    arrow: boxChildren.find(function (node) {\n      return node.classList.contains(ARROW_CLASS) || node.classList.contains(SVG_ARROW_CLASS);\n    }),\n    backdrop: boxChildren.find(function (node) {\n      return node.classList.contains(BACKDROP_CLASS);\n    })\n  };\n}\nfunction render(instance) {\n  var popper = div();\n  var box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n  var content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n  setContent(content, instance.props);\n  popper.appendChild(box);\n  box.appendChild(content);\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps, nextProps) {\n    var _getChildren = getChildren(popper),\n        box = _getChildren.box,\n        content = _getChildren.content,\n        arrow = _getChildren.arrow;\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth = typeof nextProps.maxWidth === 'number' ? nextProps.maxWidth + \"px\" : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (prevProps.content !== nextProps.content || prevProps.allowHTML !== nextProps.allowHTML) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow);\n    }\n  }\n\n  return {\n    popper: popper,\n    onUpdate: onUpdate\n  };\n} // Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\n\nrender.$$tippy = true;\n\nvar idCounter = 1;\nvar mouseMoveListeners = []; // Used by `hideAll()`\n\nvar mountedInstances = [];\nfunction createTippy(reference, passedProps) {\n  var props = evaluateProps(reference, Object.assign({}, defaultProps, getExtendedPassedProps(removeUndefinedProps(passedProps)))); // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n\n  var showTimeout;\n  var hideTimeout;\n  var scheduleHideAnimationFrame;\n  var isVisibleFromClick = false;\n  var didHideDueToDocumentMouseDown = false;\n  var didTouchMove = false;\n  var ignoreOnFirstUpdate = false;\n  var lastTriggerEvent;\n  var currentTransitionEndListener;\n  var onFirstUpdate;\n  var listeners = [];\n  var debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  var currentTarget; // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n\n  var id = idCounter++;\n  var popperInstance = null;\n  var plugins = unique(props.plugins);\n  var state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false\n  };\n  var instance = {\n    // properties\n    id: id,\n    reference: reference,\n    popper: div(),\n    popperInstance: popperInstance,\n    props: props,\n    state: state,\n    plugins: plugins,\n    // methods\n    clearDelayTimeouts: clearDelayTimeouts,\n    setProps: setProps,\n    setContent: setContent,\n    show: show,\n    hide: hide,\n    hideWithInteractivity: hideWithInteractivity,\n    enable: enable,\n    disable: disable,\n    unmount: unmount,\n    destroy: destroy\n  }; // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n\n  /* istanbul ignore if */\n\n  if (!props.render) {\n    if (true) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  } // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n\n\n  var _props$render = props.render(instance),\n      popper = _props$render.popper,\n      onUpdate = _props$render.onUpdate;\n\n  popper.setAttribute('data-tippy-root', '');\n  popper.id = \"tippy-\" + instance.id;\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n  var pluginsHooks = plugins.map(function (plugin) {\n    return plugin.fn(instance);\n  });\n  var hasAriaExpanded = reference.hasAttribute('aria-expanded');\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  } // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n\n\n  popper.addEventListener('mouseenter', function () {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n  popper.addEventListener('mouseleave', function () {\n    if (instance.props.interactive && instance.props.trigger.indexOf('mouseenter') >= 0) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n  return instance; // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n\n  function getNormalizedTouchSettings() {\n    var touch = instance.props.touch;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior() {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn() {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    return !!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy);\n  }\n\n  function getCurrentTarget() {\n    return currentTarget || reference;\n  }\n\n  function getDocument() {\n    var parent = getCurrentTarget().parentNode;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren() {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow) {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (instance.state.isMounted && !instance.state.isVisible || currentInput.isTouch || lastTriggerEvent && lastTriggerEvent.type === 'focus') {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(instance.props.delay, isShow ? 0 : 1, defaultProps.delay);\n  }\n\n  function handleStyles(fromHide) {\n    if (fromHide === void 0) {\n      fromHide = false;\n    }\n\n    popper.style.pointerEvents = instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = \"\" + instance.props.zIndex;\n  }\n\n  function invokeHook(hook, args, shouldInvokePropsHook) {\n    if (shouldInvokePropsHook === void 0) {\n      shouldInvokePropsHook = true;\n    }\n\n    pluginsHooks.forEach(function (pluginHooks) {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook].apply(pluginHooks, args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      var _instance$props;\n\n      (_instance$props = instance.props)[hook].apply(_instance$props, args);\n    }\n  }\n\n  function handleAriaContentAttribute() {\n    var aria = instance.props.aria;\n\n    if (!aria.content) {\n      return;\n    }\n\n    var attr = \"aria-\" + aria.content;\n    var id = popper.id;\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      var currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? currentValue + \" \" + id : id);\n      } else {\n        var nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute() {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      if (instance.props.interactive) {\n        node.setAttribute('aria-expanded', instance.state.isVisible && node === getCurrentTarget() ? 'true' : 'false');\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners() {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(function (listener) {\n      return listener !== debouncedOnMouseMove;\n    });\n  }\n\n  function onDocumentPress(event) {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    var actualTarget = event.composedPath && event.composedPath()[0] || event.target; // Clicked on interactive popper\n\n    if (instance.props.interactive && actualContains(popper, actualTarget)) {\n      return;\n    } // Clicked on the event listeners target\n\n\n    if (normalizeToArray(instance.props.triggerTarget || reference).some(function (el) {\n      return actualContains(el, actualTarget);\n    })) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (instance.state.isVisible && instance.props.trigger.indexOf('click') >= 0) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide(); // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(function () {\n        didHideDueToDocumentMouseDown = false;\n      }); // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove() {\n    didTouchMove = true;\n  }\n\n  function onTouchStart() {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress() {\n    var doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress() {\n    var doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration, callback) {\n    onTransitionEnd(duration, function () {\n      if (!instance.state.isVisible && popper.parentNode && popper.parentNode.contains(popper)) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration, callback) {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration, callback) {\n    var box = getDefaultTemplateChildren().box;\n\n    function listener(event) {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    } // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n\n\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n    currentTransitionEndListener = listener;\n  }\n\n  function on(eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    var nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach(function (node) {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({\n        node: node,\n        eventType: eventType,\n        handler: handler,\n        options: options\n      });\n    });\n  }\n\n  function addListeners() {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {\n        passive: true\n      });\n      on('touchend', onMouseLeave, {\n        passive: true\n      });\n    }\n\n    splitBySpaces(instance.props.trigger).forEach(function (eventType) {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave);\n          break;\n\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut);\n          break;\n\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut);\n          break;\n      }\n    });\n  }\n\n  function removeListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event) {\n    var _lastTriggerEvent;\n\n    var shouldScheduleClickHide = false;\n\n    if (!instance.state.isEnabled || isEventListenerStopped(event) || didHideDueToDocumentMouseDown) {\n      return;\n    }\n\n    var wasFocused = ((_lastTriggerEvent = lastTriggerEvent) == null ? void 0 : _lastTriggerEvent.type) === 'focus';\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget;\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach(function (listener) {\n        return listener(event);\n      });\n    } // Toggle show/hide when clicking click-triggered tooltips\n\n\n    if (event.type === 'click' && (instance.props.trigger.indexOf('mouseenter') < 0 || isVisibleFromClick) && instance.props.hideOnClick !== false && instance.state.isVisible) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event) {\n    var target = event.target;\n    var isCursorOverReferenceOrPopper = getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    var popperTreeData = getNestedPopperTree().concat(popper).map(function (popper) {\n      var _instance$popperInsta;\n\n      var instance = popper._tippy;\n      var state = (_instance$popperInsta = instance.popperInstance) == null ? void 0 : _instance$popperInsta.state;\n\n      if (state) {\n        return {\n          popperRect: popper.getBoundingClientRect(),\n          popperState: state,\n          props: props\n        };\n      }\n\n      return null;\n    }).filter(Boolean);\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event) {\n    var shouldBail = isEventListenerStopped(event) || instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick;\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event) {\n    if (instance.props.trigger.indexOf('focusin') < 0 && event.target !== getCurrentTarget()) {\n      return;\n    } // If focus was moved to within the popper\n\n\n    if (instance.props.interactive && event.relatedTarget && popper.contains(event.relatedTarget)) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event) {\n    return currentInput.isTouch ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0 : false;\n  }\n\n  function createPopperInstance() {\n    destroyPopperInstance();\n    var _instance$props2 = instance.props,\n        popperOptions = _instance$props2.popperOptions,\n        placement = _instance$props2.placement,\n        offset = _instance$props2.offset,\n        getReferenceClientRect = _instance$props2.getReferenceClientRect,\n        moveTransition = _instance$props2.moveTransition;\n    var arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n    var computedReference = getReferenceClientRect ? {\n      getBoundingClientRect: getReferenceClientRect,\n      contextElement: getReferenceClientRect.contextElement || getCurrentTarget()\n    } : reference;\n    var tippyModifier = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (getIsDefaultRenderFn()) {\n          var _getDefaultTemplateCh = getDefaultTemplateChildren(),\n              box = _getDefaultTemplateCh.box;\n\n          ['placement', 'reference-hidden', 'escaped'].forEach(function (attr) {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[\"data-popper-\" + attr]) {\n                box.setAttribute(\"data-\" + attr, '');\n              } else {\n                box.removeAttribute(\"data-\" + attr);\n              }\n            }\n          });\n          state.attributes.popper = {};\n        }\n      }\n    };\n    var modifiers = [{\n      name: 'offset',\n      options: {\n        offset: offset\n      }\n    }, {\n      name: 'preventOverflow',\n      options: {\n        padding: {\n          top: 2,\n          bottom: 2,\n          left: 5,\n          right: 5\n        }\n      }\n    }, {\n      name: 'flip',\n      options: {\n        padding: 5\n      }\n    }, {\n      name: 'computeStyles',\n      options: {\n        adaptive: !moveTransition\n      }\n    }, tippyModifier];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3\n        }\n      });\n    }\n\n    modifiers.push.apply(modifiers, (popperOptions == null ? void 0 : popperOptions.modifiers) || []);\n    instance.popperInstance = (0,_popperjs_core__WEBPACK_IMPORTED_MODULE_0__.createPopper)(computedReference, popper, Object.assign({}, popperOptions, {\n      placement: placement,\n      onFirstUpdate: onFirstUpdate,\n      modifiers: modifiers\n    }));\n  }\n\n  function destroyPopperInstance() {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount() {\n    var appendTo = instance.props.appendTo;\n    var parentNode; // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n\n    var node = getCurrentTarget();\n\n    if (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO || appendTo === 'parent') {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    } // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n\n\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n    createPopperInstance();\n    /* istanbul ignore else */\n\n    if (true) {\n      // Accessibility check\n      warnWhen(instance.props.interactive && appendTo === defaultProps.appendTo && node.nextElementSibling !== popper, ['Interactive tippy element may not be accessible via keyboard', 'navigation because it is not directly after the reference element', 'in the DOM source order.', '\\n\\n', 'Using a wrapper <div> or <span> tag around the reference element', 'solves this by creating a new parentNode context.', '\\n\\n', 'Specifying `appendTo: document.body` silences this warning, but it', 'assumes you are using a focus management solution to handle', 'keyboard navigation.', '\\n\\n', 'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity'].join(' '));\n    }\n  }\n\n  function getNestedPopperTree() {\n    return arrayFrom(popper.querySelectorAll('[data-tippy-root]'));\n  }\n\n  function scheduleShow(event) {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n    var delay = getDelay(true);\n\n    var _getNormalizedTouchSe = getNormalizedTouchSettings(),\n        touchValue = _getNormalizedTouchSe[0],\n        touchDelay = _getNormalizedTouchSe[1];\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(function () {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event) {\n    instance.clearDelayTimeouts();\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n      return;\n    } // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n\n\n    if (instance.props.trigger.indexOf('mouseenter') >= 0 && instance.props.trigger.indexOf('click') >= 0 && ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 && isVisibleFromClick) {\n      return;\n    }\n\n    var delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(function () {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(function () {\n        instance.hide();\n      });\n    }\n  } // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n\n\n  function enable() {\n    instance.state.isEnabled = true;\n  }\n\n  function disable() {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts() {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps) {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n    removeListeners();\n    var prevProps = instance.props;\n    var nextProps = evaluateProps(reference, Object.assign({}, prevProps, removeUndefinedProps(partialProps), {\n      ignoreAttributes: true\n    }));\n    instance.props = nextProps;\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(onMouseMove, nextProps.interactiveDebounce);\n    } // Ensure stale aria-expanded attributes are removed\n\n\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach(function (node) {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance(); // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n\n      getNestedPopperTree().forEach(function (nestedPopper) {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy.popperInstance.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content) {\n    instance.setProps({\n      content: content\n    });\n  }\n\n  function show() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    } // Early bail-out\n\n\n    var isAlreadyVisible = instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var isTouchAndTouchDisabled = currentInput.isTouch && !instance.props.touch;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 0, defaultProps.duration);\n\n    if (isAlreadyVisible || isDestroyed || isDisabled || isTouchAndTouchDisabled) {\n      return;\n    } // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n\n\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    } // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh2 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh2.box,\n          content = _getDefaultTemplateCh2.content;\n\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = function onFirstUpdate() {\n      var _instance$popperInsta2;\n\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true; // reflow\n\n      void popper.offsetHeight;\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        var _getDefaultTemplateCh3 = getDefaultTemplateChildren(),\n            _box = _getDefaultTemplateCh3.box,\n            _content = _getDefaultTemplateCh3.content;\n\n        setTransitionDuration([_box, _content], duration);\n        setVisibilityState([_box, _content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n      pushIfUnique(mountedInstances, instance); // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n\n      (_instance$popperInsta2 = instance.popperInstance) == null ? void 0 : _instance$popperInsta2.forceUpdate();\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, function () {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    } // Early bail-out\n\n\n    var isAlreadyHidden = !instance.state.isVisible;\n    var isDestroyed = instance.state.isDestroyed;\n    var isDisabled = !instance.state.isEnabled;\n    var duration = getValueAtIndexOrReturn(instance.props.duration, 1, defaultProps.duration);\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      var _getDefaultTemplateCh4 = getDefaultTemplateChildren(),\n          box = _getDefaultTemplateCh4.box,\n          content = _getDefaultTemplateCh4.content;\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event) {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hideWithInteractivity'));\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance(); // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n\n    getNestedPopperTree().forEach(function (nestedPopper) {\n      nestedPopper._tippy.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter(function (i) {\n      return i !== instance;\n    });\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy() {\n    /* istanbul ignore else */\n    if (true) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n    removeListeners();\n    delete reference._tippy;\n    instance.state.isDestroyed = true;\n    invokeHook('onDestroy', [instance]);\n  }\n}\n\nfunction tippy(targets, optionalProps) {\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  var plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n  /* istanbul ignore else */\n\n  if (true) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n  var passedProps = Object.assign({}, optionalProps, {\n    plugins: plugins\n  });\n  var elements = getArrayOfElements(targets);\n  /* istanbul ignore else */\n\n  if (true) {\n    var isSingleContentElement = isElement(passedProps.content);\n    var isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(isSingleContentElement && isMoreThanOneReferenceElement, ['tippy() was passed an Element as the `content` prop, but more than', 'one tippy instance was created by this invocation. This means the', 'content element will only be appended to the last tippy instance.', '\\n\\n', 'Instead, pass the .innerHTML of the element, or use a function that', 'returns a cloned version of the element instead.', '\\n\\n', '1) content: element.innerHTML\\n', '2) content: () => element.cloneNode(true)'].join(' '));\n  }\n\n  var instances = elements.reduce(function (acc, reference) {\n    var instance = reference && createTippy(reference, passedProps);\n\n    if (instance) {\n      acc.push(instance);\n    }\n\n    return acc;\n  }, []);\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\nvar hideAll = function hideAll(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      excludedReferenceOrInstance = _ref.exclude,\n      duration = _ref.duration;\n\n  mountedInstances.forEach(function (instance) {\n    var isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance) ? instance.reference === excludedReferenceOrInstance : instance.popper === excludedReferenceOrInstance.popper;\n    }\n\n    if (!isExcluded) {\n      var originalDuration = instance.props.duration;\n      instance.setProps({\n        duration: duration\n      });\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({\n          duration: originalDuration\n        });\n      }\n    }\n  });\n};\n\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\n\nvar applyStylesModifier = Object.assign({}, _popperjs_core__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n  effect: function effect(_ref) {\n    var state = _ref.state;\n    var initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0'\n      },\n      arrow: {\n        position: 'absolute'\n      },\n      reference: {}\n    };\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    } // intentionally return no cleanup function\n    // return () => { ... }\n\n  }\n});\n\nvar createSingleton = function createSingleton(tippyInstances, optionalProps) {\n  var _optionalProps$popper;\n\n  if (optionalProps === void 0) {\n    optionalProps = {};\n  }\n\n  /* istanbul ignore else */\n  if (true) {\n    errorWhen(!Array.isArray(tippyInstances), ['The first argument passed to createSingleton() must be an array of', 'tippy instances. The passed value was', String(tippyInstances)].join(' '));\n  }\n\n  var individualInstances = tippyInstances;\n  var references = [];\n  var triggerTargets = [];\n  var currentTarget;\n  var overrides = optionalProps.overrides;\n  var interceptSetPropsCleanups = [];\n  var shownOnCreate = false;\n\n  function setTriggerTargets() {\n    triggerTargets = individualInstances.map(function (instance) {\n      return normalizeToArray(instance.props.triggerTarget || instance.reference);\n    }).reduce(function (acc, item) {\n      return acc.concat(item);\n    }, []);\n  }\n\n  function setReferences() {\n    references = individualInstances.map(function (instance) {\n      return instance.reference;\n    });\n  }\n\n  function enableInstances(isEnabled) {\n    individualInstances.forEach(function (instance) {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton) {\n    return individualInstances.map(function (instance) {\n      var originalSetProps = instance.setProps;\n\n      instance.setProps = function (props) {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return function () {\n        instance.setProps = originalSetProps;\n      };\n    });\n  } // have to pass singleton, as it maybe undefined on first call\n\n\n  function prepareInstance(singleton, target) {\n    var index = triggerTargets.indexOf(target); // bail-out\n\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n    var overrideProps = (overrides || []).concat('content').reduce(function (acc, prop) {\n      acc[prop] = individualInstances[index].props[prop];\n      return acc;\n    }, {});\n    singleton.setProps(Object.assign({}, overrideProps, {\n      getReferenceClientRect: typeof overrideProps.getReferenceClientRect === 'function' ? overrideProps.getReferenceClientRect : function () {\n        var _references$index;\n\n        return (_references$index = references[index]) == null ? void 0 : _references$index.getBoundingClientRect();\n      }\n    }));\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n  var plugin = {\n    fn: function fn() {\n      return {\n        onDestroy: function onDestroy() {\n          enableInstances(true);\n        },\n        onHidden: function onHidden() {\n          currentTarget = null;\n        },\n        onClickOutside: function onClickOutside(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow: function onShow(instance) {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger: function onTrigger(instance, event) {\n          prepareInstance(instance, event.currentTarget);\n        }\n      };\n    }\n  };\n  var singleton = tippy(div(), Object.assign({}, removeProperties(optionalProps, ['overrides']), {\n    plugins: [plugin].concat(optionalProps.plugins || []),\n    triggerTarget: triggerTargets,\n    popperOptions: Object.assign({}, optionalProps.popperOptions, {\n      modifiers: [].concat(((_optionalProps$popper = optionalProps.popperOptions) == null ? void 0 : _optionalProps$popper.modifiers) || [], [applyStylesModifier])\n    })\n  }));\n  var originalShow = singleton.show;\n\n  singleton.show = function (target) {\n    originalShow(); // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    } // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n\n\n    if (currentTarget && target == null) {\n      return;\n    } // target is index of instance\n\n\n    if (typeof target === 'number') {\n      return references[target] && prepareInstance(singleton, references[target]);\n    } // target is a child tippy instance\n\n\n    if (individualInstances.indexOf(target) >= 0) {\n      var ref = target.reference;\n      return prepareInstance(singleton, ref);\n    } // target is a ReferenceElement\n\n\n    if (references.indexOf(target) >= 0) {\n      return prepareInstance(singleton, target);\n    }\n  };\n\n  singleton.showNext = function () {\n    var first = references[0];\n\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n\n    var index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = function () {\n    var last = references[references.length - 1];\n\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n\n    var index = references.indexOf(currentTarget);\n    var target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  var originalSetProps = singleton.setProps;\n\n  singleton.setProps = function (props) {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = function (nextInstances) {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach(function (fn) {\n      return fn();\n    });\n    individualInstances = nextInstances;\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n    singleton.setProps({\n      triggerTarget: triggerTargets\n    });\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n  return singleton;\n};\n\nvar BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click'\n};\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\n\nfunction delegate(targets, props) {\n  /* istanbul ignore else */\n  if (true) {\n    errorWhen(!(props && props.target), ['You must specity a `target` prop indicating a CSS selector string matching', 'the target elements that should receive a tippy.'].join(' '));\n  }\n\n  var listeners = [];\n  var childTippyInstances = [];\n  var disabled = false;\n  var target = props.target;\n  var nativeProps = removeProperties(props, ['target']);\n  var parentProps = Object.assign({}, nativeProps, {\n    trigger: 'manual',\n    touch: false\n  });\n  var childProps = Object.assign({\n    touch: defaultProps.touch\n  }, nativeProps, {\n    showOnCreate: true\n  });\n  var returnValue = tippy(targets, parentProps);\n  var normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event) {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    var targetNode = event.target.closest(target);\n\n    if (!targetNode) {\n      return;\n    } // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n\n\n    var trigger = targetNode.getAttribute('data-tippy-trigger') || props.trigger || defaultProps.trigger; // @ts-ignore\n\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (event.type !== 'touchstart' && trigger.indexOf(BUBBLING_EVENTS_MAP[event.type]) < 0) {\n      return;\n    }\n\n    var instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(node, eventType, handler, options) {\n    if (options === void 0) {\n      options = false;\n    }\n\n    node.addEventListener(eventType, handler, options);\n    listeners.push({\n      node: node,\n      eventType: eventType,\n      handler: handler,\n      options: options\n    });\n  }\n\n  function addEventListeners(instance) {\n    var reference = instance.reference;\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners() {\n    listeners.forEach(function (_ref) {\n      var node = _ref.node,\n          eventType = _ref.eventType,\n          handler = _ref.handler,\n          options = _ref.options;\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance) {\n    var originalDestroy = instance.destroy;\n    var originalEnable = instance.enable;\n    var originalDisable = instance.disable;\n\n    instance.destroy = function (shouldDestroyChildInstances) {\n      if (shouldDestroyChildInstances === void 0) {\n        shouldDestroyChildInstances = true;\n      }\n\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach(function (instance) {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = function () {\n      originalEnable();\n      childTippyInstances.forEach(function (instance) {\n        return instance.enable();\n      });\n      disabled = false;\n    };\n\n    instance.disable = function () {\n      originalDisable();\n      childTippyInstances.forEach(function (instance) {\n        return instance.disable();\n      });\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n  return returnValue;\n}\n\nvar animateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var _instance$props$rende;\n\n    // @ts-ignore\n    if (!((_instance$props$rende = instance.props.render) != null && _instance$props$rende.$$tippy)) {\n      if (true) {\n        errorWhen(instance.props.animateFill, 'The `animateFill` plugin requires the default render function.');\n      }\n\n      return {};\n    }\n\n    var _getChildren = getChildren(instance.popper),\n        box = _getChildren.box,\n        content = _getChildren.content;\n\n    var backdrop = instance.props.animateFill ? createBackdropElement() : null;\n    return {\n      onCreate: function onCreate() {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n          instance.setProps({\n            arrow: false,\n            animation: 'shift-away'\n          });\n        }\n      },\n      onMount: function onMount() {\n        if (backdrop) {\n          var transitionDuration = box.style.transitionDuration;\n          var duration = Number(transitionDuration.replace('ms', '')); // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n\n          content.style.transitionDelay = Math.round(duration / 10) + \"ms\";\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow: function onShow() {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide: function onHide() {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      }\n    };\n  }\n};\n\nfunction createBackdropElement() {\n  var backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n\nvar mouseCoords = {\n  clientX: 0,\n  clientY: 0\n};\nvar activeInstances = [];\n\nfunction storeMouseCoords(_ref) {\n  var clientX = _ref.clientX,\n      clientY = _ref.clientY;\n  mouseCoords = {\n    clientX: clientX,\n    clientY: clientY\n  };\n}\n\nfunction addMouseCoordsListener(doc) {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc) {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nvar followCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n    var doc = getOwnerDocument(instance.props.triggerTarget || reference);\n    var isInternalUpdate = false;\n    var wasFocusEvent = false;\n    var isUnmounted = true;\n    var prevProps = instance.props;\n\n    function getIsInitialBehavior() {\n      return instance.props.followCursor === 'initial' && instance.state.isVisible;\n    }\n\n    function addListener() {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener() {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect() {\n      isInternalUpdate = true;\n      instance.setProps({\n        getReferenceClientRect: null\n      });\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event) {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      var isCursorOverReference = event.target ? reference.contains(event.target) : true;\n      var followCursor = instance.props.followCursor;\n      var clientX = event.clientX,\n          clientY = event.clientY;\n      var rect = reference.getBoundingClientRect();\n      var relativeX = clientX - rect.left;\n      var relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect: function getReferenceClientRect() {\n            var rect = reference.getBoundingClientRect();\n            var x = clientX;\n            var y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            var top = followCursor === 'horizontal' ? rect.top : y;\n            var right = followCursor === 'vertical' ? rect.right : x;\n            var bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            var left = followCursor === 'vertical' ? rect.left : x;\n            return {\n              width: right - left,\n              height: bottom - top,\n              top: top,\n              right: right,\n              bottom: bottom,\n              left: left\n            };\n          }\n        });\n      }\n    }\n\n    function create() {\n      if (instance.props.followCursor) {\n        activeInstances.push({\n          instance: instance,\n          doc: doc\n        });\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy() {\n      activeInstances = activeInstances.filter(function (data) {\n        return data.instance !== instance;\n      });\n\n      if (activeInstances.filter(function (data) {\n        return data.doc === doc;\n      }).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate: function onBeforeUpdate() {\n        prevProps = instance.props;\n      },\n      onAfterUpdate: function onAfterUpdate(_, _ref2) {\n        var followCursor = _ref2.followCursor;\n\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (followCursor !== undefined && prevProps.followCursor !== followCursor) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (instance.state.isMounted && !wasFocusEvent && !getIsInitialBehavior()) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount: function onMount() {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          mouseCoords = {\n            clientX: event.clientX,\n            clientY: event.clientY\n          };\n        }\n\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden: function onHidden() {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      }\n    };\n  }\n};\n\nfunction getProps(props, modifier) {\n  var _props$popperOptions;\n\n  return {\n    popperOptions: Object.assign({}, props.popperOptions, {\n      modifiers: [].concat((((_props$popperOptions = props.popperOptions) == null ? void 0 : _props$popperOptions.modifiers) || []).filter(function (_ref) {\n        var name = _ref.name;\n        return name !== modifier.name;\n      }), [modifier])\n    })\n  };\n}\n\nvar inlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference;\n\n    function isEnabled() {\n      return !!instance.props.inlinePositioning;\n    }\n\n    var placement;\n    var cursorRectIndex = -1;\n    var isInternalUpdate = false;\n    var triedPlacements = [];\n    var modifier = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn: function fn(_ref2) {\n        var state = _ref2.state;\n\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (placement !== state.placement && triedPlacements.indexOf(state.placement) === -1) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: function getReferenceClientRect() {\n                return _getReferenceClientRect(state.placement);\n              }\n            });\n          }\n\n          placement = state.placement;\n        }\n      }\n    };\n\n    function _getReferenceClientRect(placement) {\n      return getInlineBoundingClientRect(getBasePlacement(placement), reference.getBoundingClientRect(), arrayFrom(reference.getClientRects()), cursorRectIndex);\n    }\n\n    function setInternalProps(partialProps) {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier() {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger: function onTrigger(_, event) {\n        if (isMouseEvent(event)) {\n          var rects = arrayFrom(instance.reference.getClientRects());\n          var cursorRect = rects.find(function (rect) {\n            return rect.left - 2 <= event.clientX && rect.right + 2 >= event.clientX && rect.top - 2 <= event.clientY && rect.bottom + 2 >= event.clientY;\n          });\n          var index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden: function onHidden() {\n        cursorRectIndex = -1;\n      }\n    };\n  }\n};\nfunction getInlineBoundingClientRect(currentBasePlacement, boundingRect, clientRects, cursorRectIndex) {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  } // There are two rects and they are disjoined\n\n\n  if (clientRects.length === 2 && cursorRectIndex >= 0 && clientRects[0].left > clientRects[1].right) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom':\n      {\n        var firstRect = clientRects[0];\n        var lastRect = clientRects[clientRects.length - 1];\n        var isTop = currentBasePlacement === 'top';\n        var top = firstRect.top;\n        var bottom = lastRect.bottom;\n        var left = isTop ? firstRect.left : lastRect.left;\n        var right = isTop ? firstRect.right : lastRect.right;\n        var width = right - left;\n        var height = bottom - top;\n        return {\n          top: top,\n          bottom: bottom,\n          left: left,\n          right: right,\n          width: width,\n          height: height\n        };\n      }\n\n    case 'left':\n    case 'right':\n      {\n        var minLeft = Math.min.apply(Math, clientRects.map(function (rects) {\n          return rects.left;\n        }));\n        var maxRight = Math.max.apply(Math, clientRects.map(function (rects) {\n          return rects.right;\n        }));\n        var measureRects = clientRects.filter(function (rect) {\n          return currentBasePlacement === 'left' ? rect.left === minLeft : rect.right === maxRight;\n        });\n        var _top = measureRects[0].top;\n        var _bottom = measureRects[measureRects.length - 1].bottom;\n        var _left = minLeft;\n        var _right = maxRight;\n\n        var _width = _right - _left;\n\n        var _height = _bottom - _top;\n\n        return {\n          top: _top,\n          bottom: _bottom,\n          left: _left,\n          right: _right,\n          width: _width,\n          height: _height\n        };\n      }\n\n    default:\n      {\n        return boundingRect;\n      }\n  }\n}\n\nvar sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn: function fn(instance) {\n    var reference = instance.reference,\n        popper = instance.popper;\n\n    function getReference() {\n      return instance.popperInstance ? instance.popperInstance.state.elements.reference : reference;\n    }\n\n    function shouldCheck(value) {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    var prevRefRect = null;\n    var prevPopRect = null;\n\n    function updatePosition() {\n      var currentRefRect = shouldCheck('reference') ? getReference().getBoundingClientRect() : null;\n      var currentPopRect = shouldCheck('popper') ? popper.getBoundingClientRect() : null;\n\n      if (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect) || currentPopRect && areRectsDifferent(prevPopRect, currentPopRect)) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount: function onMount() {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      }\n    };\n  }\n};\n\nfunction areRectsDifferent(rectA, rectB) {\n  if (rectA && rectB) {\n    return rectA.top !== rectB.top || rectA.right !== rectB.right || rectA.bottom !== rectB.bottom || rectA.left !== rectB.left;\n  }\n\n  return true;\n}\n\ntippy.setDefaultProps({\n  render: render\n});\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tippy);\n\n//# sourceMappingURL=tippy.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tippy.js/dist/tippy.esm.js\n");

/***/ })

};
;