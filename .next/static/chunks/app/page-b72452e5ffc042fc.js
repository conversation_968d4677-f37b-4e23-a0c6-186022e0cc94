(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2865:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eR});var t=a(5155),l=a(2115),n=a(9708),i=a(2085),r=a(2596),d=a(9688);function c(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,d.QP)((0,r.$)(s))}let x=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:a,size:l,asChild:i=!1,...r}=e,d=i?n.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:c(x({variant:a,size:l,className:s})),...r})}var m=a(4011);function h(e){let{className:s,...a}=e;return(0,t.jsx)(m.bL,{"data-slot":"avatar",className:c("relative flex size-8 shrink-0 overflow-hidden rounded-full",s),...a})}function u(e){let{className:s,...a}=e;return(0,t.jsx)(m.H4,{"data-slot":"avatar-fallback",className:c("bg-muted flex size-full items-center justify-center rounded-full",s),...a})}var p=a(6606);function f(e){let{...s}=e;return(0,t.jsx)(p.bL,{"data-slot":"dropdown-menu",...s})}function v(e){let{...s}=e;return(0,t.jsx)(p.l9,{"data-slot":"dropdown-menu-trigger",...s})}function j(e){let{className:s,sideOffset:a=4,...l}=e;return(0,t.jsx)(p.ZL,{children:(0,t.jsx)(p.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:c("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",s),...l})})}function g(e){let{className:s,inset:a,variant:l="default",...n}=e;return(0,t.jsx)(p.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":l,className:c("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n})}function N(e){let{className:s,...a}=e;return(0,t.jsx)(p.wv,{"data-slot":"dropdown-menu-separator",className:c("bg-border -mx-1 my-1 h-px",s),...a})}let b=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function w(e){let{className:s,variant:a,asChild:l=!1,...i}=e,r=l?n.DX:"span";return(0,t.jsx)(r,{"data-slot":"badge",className:c(b({variant:a}),s),...i})}var y=a(5623),_=a(381),A=a(4357),E=a(2525),T=a(9869),k=a(1788),R=a(5690),C=a(2178);let z=(0,a(5453).v)(e=>({currentApp:{id:"demo-app",name:"订单运输调度系统",icon:"\uD83D\uDE9B",status:"development",version:"v0.1.0"},leftPanel:{activeTab:"chat",isDocumentEditing:!1},canvas:{mode:"flow",flowData:null},chat:{messages:[{id:"1",type:"assistant",content:"你好！我是 ideaFlow AI 助手。我可以帮助你将自然语言描述的工作流程转化为可执行的业务应用。\n\n当前项目：订单运输调度系统\n\n你可以：\n- 描述你的业务流程需求\n- 上传相关文档或数据文件\n- 让我帮你构建和优化工作流程",timestamp:new Date}],isLoading:!1},files:[{id:"main-doc",name:"项目需求文档.md",type:"document",content:"# 订单运输调度系统需求文档\n\n## 项目概述\n本系统旨在根据用户上传的订单明细、物料信息、车辆信息，将销售订单中的物料合理分配给车辆进行配送，实现智能化的运输调度。\n\n## 核心功能\n\n### 1. 数据输入\n- 订单明细：包含客户信息、物料需求、交货日期等\n- 物料信息：物料规格、重量、体积等属性\n- 车辆信息：车辆载重、容积、可用时间等\n\n### 2. 智能调度\n- 根据物料属性和车辆容量进行最优匹配\n- 考虑交货日期进行合理的时间安排\n- 优化运输路线，降低成本\n\n### 3. 结果输出\n- 生成详细的配送计划\n- 显示每辆车的装载清单\n- 提供时间安排和路线建议\n\n## 业务流程\n1. 用户上传基础数据文件\n2. 系统解析和验证数据\n3. 执行智能调度算法\n4. 生成配送方案\n5. 用户确认并导出结果\n\n## 技术要求\n- 支持 Excel 文件导入\n- 提供可视化的流程设计界面\n- 实时预览调度结果\n- 支持方案调整和优化\n",lastModified:new Date}],updateAppInfo:s=>e(e=>({currentApp:{...e.currentApp,...s}})),setLeftPanelTab:s=>e(e=>({leftPanel:{...e.leftPanel,activeTab:s}})),setDocumentEditing:s=>e(e=>({leftPanel:{...e.leftPanel,isDocumentEditing:s}})),setCanvasMode:s=>e(e=>({canvas:{...e.canvas,mode:s}})),updateFlowData:s=>e(e=>({canvas:{...e.canvas,flowData:s}})),addMessage:s=>e(e=>({chat:{...e.chat,messages:[...e.chat.messages,{...s,id:Date.now().toString(),timestamp:new Date}]}})),setLoading:s=>e(e=>({chat:{...e.chat,isLoading:s}})),addFile:s=>e(e=>({files:[...e.files,{...s,id:Date.now().toString(),lastModified:new Date}]})),updateFile:(s,a)=>e(e=>({files:e.files.map(e=>e.id===s?{...e,content:a,lastModified:new Date}:e)})),deleteFile:s=>e(e=>({files:e.files.filter(e=>e.id!==s)}))}));function L(){let{currentApp:e}=z();return(0,t.jsxs)("header",{className:"h-16 border-b bg-white flex items-center justify-between px-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)("div",{className:"text-2xl font-bold",children:["idea",(0,t.jsx)("span",{className:"text-blue-600",children:"Flow"})]})}),(0,t.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h,{className:"h-8 w-8",children:(0,t.jsx)(u,{className:"text-lg",children:e.icon})}),(0,t.jsx)("div",{className:"flex flex-col",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-medium text-sm",children:e.name}),(0,t.jsx)(w,{variant:"secondary",className:"text-xs",children:e.version})]})}),(0,t.jsxs)(f,{children:[(0,t.jsx)(v,{asChild:!0,children:(0,t.jsx)(o,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,t.jsx)(y.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(j,{align:"start",children:[(0,t.jsxs)(g,{children:[(0,t.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"应用配置"]}),(0,t.jsxs)(g,{children:[(0,t.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"复制应用"]}),(0,t.jsx)(N,{}),(0,t.jsxs)(g,{className:"text-red-600",children:[(0,t.jsx)(E.A,{className:"mr-2 h-4 w-4"}),"删除应用"]})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ".concat((e=>{switch(e){case"development":return"bg-yellow-500";case"testing":return"bg-blue-500";case"production":return"bg-green-500";default:return"bg-gray-500"}})(e.status))}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:(e=>{switch(e){case"development":return"开发中";case"testing":return"测试中";case"production":return"已发布";default:return"未知"}})(e.status)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(o,{variant:"outline",size:"sm",children:[(0,t.jsx)(T.A,{className:"mr-2 h-4 w-4"}),"导入"]}),(0,t.jsxs)(o,{variant:"outline",size:"sm",children:[(0,t.jsx)(k.A,{className:"mr-2 h-4 w-4"}),"导出"]}),"development"===e.status?(0,t.jsxs)(o,{size:"sm",children:[(0,t.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"测试运行"]}):(0,t.jsxs)(o,{size:"sm",variant:"outline",children:[(0,t.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"停止"]}),(0,t.jsx)(o,{size:"sm",className:"bg-blue-600 hover:bg-blue-700",children:"发布应用"})]})]})]})}var D=a(704);function M(e){let{className:s,...a}=e;return(0,t.jsx)(D.bL,{"data-slot":"tabs",className:c("flex flex-col gap-2",s),...a})}function I(e){let{className:s,...a}=e;return(0,t.jsx)(D.B8,{"data-slot":"tabs-list",className:c("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...a})}function S(e){let{className:s,...a}=e;return(0,t.jsx)(D.l9,{"data-slot":"tabs-trigger",className:c("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...a})}function U(e){let{className:s,...a}=e;return(0,t.jsx)(D.UC,{"data-slot":"tabs-content",className:c("flex-1 outline-none",s),...a})}var O=a(9051);function V(e){let{className:s,children:a,...l}=e;return(0,t.jsxs)(O.bL,{"data-slot":"scroll-area",className:c("relative",s),...l,children:[(0,t.jsx)(O.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:a}),(0,t.jsx)(F,{}),(0,t.jsx)(O.OK,{})]})}function F(e){let{className:s,orientation:a="vertical",...l}=e;return(0,t.jsx)(O.VM,{"data-slot":"scroll-area-scrollbar",orientation:a,className:c("flex touch-none p-px transition-colors select-none","vertical"===a&&"h-full w-2.5 border-l border-l-transparent","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent",s),...l,children:(0,t.jsx)(O.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function P(e){let{className:s,...a}=e;return(0,t.jsx)("textarea",{"data-slot":"textarea",className:c("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}var H=a(1366),B=a(7434),q=a(4616),K=a(9890),X=a(9074),Y=a(492),G=a(4355),Q=a(2486),Z=a(333),$=a(5580);function J(e){let{message:s}=e,a="user"===s.type;return(0,t.jsxs)("div",{className:"flex space-x-3 ".concat(a?"flex-row-reverse space-x-reverse":""),children:[(0,t.jsx)(h,{className:"h-8 w-8 flex-shrink-0",children:(0,t.jsx)(u,{className:a?"bg-blue-600 text-white":"bg-gray-200",children:a?"你":"AI"})}),(0,t.jsxs)("div",{className:"flex-1 space-y-2 ".concat(a?"items-end":"items-start"),children:[(0,t.jsxs)("div",{className:"max-w-[280px] p-3 rounded-lg ".concat(a?"bg-blue-600 text-white ml-auto":"bg-gray-100 text-gray-900"),children:[(0,t.jsx)("div",{className:"text-sm whitespace-pre-wrap",children:s.content}),s.attachments&&s.attachments.length>0&&(0,t.jsx)("div",{className:"mt-2 space-y-1",children:s.attachments.map((e,s)=>(0,t.jsxs)("div",{className:"text-xs p-2 rounded ".concat(a?"bg-blue-500":"bg-gray-200"),children:["\uD83D\uDCCE ",e.name]},s))})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500 ".concat(a?"flex-row-reverse space-x-reverse":""),children:[(0,t.jsx)("span",{children:s.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),!a&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(o,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-gray-200",onClick:()=>{navigator.clipboard.writeText(s.content)},children:(0,t.jsx)(A.A,{className:"h-3 w-3"})}),(0,t.jsx)(o,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-gray-200",children:(0,t.jsx)(Z.A,{className:"h-3 w-3"})}),(0,t.jsx)(o,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-gray-200",children:(0,t.jsx)($.A,{className:"h-3 w-3"})})]})]})]})]})}var W=a(7550),ee=a(2657),es=a(4940),ea=a(4229);function et(e){let{fileId:s,onBack:a}=e,{files:n,updateFile:i}=z(),[r,d]=(0,l.useState)(!1),[c,x]=(0,l.useState)(""),[m,h]=(0,l.useState)(!1),u=n.find(e=>e.id===s);(0,l.useEffect)(()=>{u&&x(u.content)},[u]);let p=e=>{x(e),h(e!==(null==u?void 0:u.content))};return u?(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o,{variant:"ghost",size:"sm",onClick:a,className:"p-2",children:(0,t.jsx)(W.A,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-sm",children:u.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["最后修改: ",u.lastModified.toLocaleString()]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o,{variant:"outline",size:"sm",onClick:()=>d(!r),children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ee.A,{className:"h-4 w-4 mr-2"}),"预览"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(es.A,{className:"h-4 w-4 mr-2"}),"编辑"]})}),m&&(0,t.jsxs)(o,{size:"sm",onClick:()=>{u&&m&&(i(u.id,c),h(!1))},className:"bg-blue-600 hover:bg-blue-700",children:[(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"保存"]})]})]}),(0,t.jsx)("div",{className:"flex-1 p-4",children:r?(0,t.jsx)(P,{value:c,onChange:e=>p(e.target.value),className:"w-full h-full resize-none font-mono text-sm",placeholder:"开始编辑文档..."}):(0,t.jsx)("div",{className:"h-full overflow-auto",children:(0,t.jsx)("div",{className:"prose prose-sm max-w-none",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap text-sm leading-relaxed",children:c})})})}),(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-t bg-gray-50 text-xs text-gray-500",children:[(0,t.jsxs)("div",{children:["document"===u.type?"\uD83D\uDCC4":"data"===u.type?"\uD83D\uDCCA":"⚙️"," ",u.type]}),(0,t.jsxs)("div",{children:[c.length," 字符 | ",c.split("\n").length," 行"]})]})]}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsx)("div",{className:"text-gray-500",children:"文件未找到"})})}function el(){let{leftPanel:e,chat:s,files:a,setLeftPanelTab:n,setDocumentEditing:i,addMessage:r,setLoading:d}=z(),[c,x]=(0,l.useState)(""),[m,h]=(0,l.useState)(null),u=async()=>{c.trim()&&(r({type:"user",content:c}),x(""),d(!0),setTimeout(()=>{r({type:"assistant",content:"我理解了你的需求。让我帮你分析一下这个工作流程...\n\n基于你的描述，我建议创建以下几个处理节点：\n1. 数据输入验证节点\n2. 智能匹配算法节点\n3. 优化调度节点\n4. 结果输出节点\n\n你希望我先从哪个部分开始构建？"}),d(!1)},2e3))},p=e=>{h(e),i(!0)};return(0,t.jsx)("div",{className:"w-96 border-r bg-white flex flex-col h-full",children:e.isDocumentEditing&&m?(0,t.jsx)(et,{fileId:m,onBack:()=>{h(null),i(!1)}}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(M,{value:e.activeTab,onValueChange:e=>n(e),className:"flex flex-col h-full",children:[(0,t.jsxs)(I,{className:"grid w-full grid-cols-2 m-4 mb-0",children:[(0,t.jsxs)(S,{value:"chat",className:"flex items-center space-x-2",children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"对话"})]}),(0,t.jsxs)(S,{value:"files",className:"flex items-center space-x-2",children:[(0,t.jsx)(B.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"文件"})]})]}),(0,t.jsx)(U,{value:"chat",className:"flex-1 flex flex-col m-4 mt-0",children:(0,t.jsx)(V,{className:"flex-1 pr-4",children:(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[s.messages.map(e=>(0,t.jsx)(J,{message:e},e.id)),s.isLoading&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"AI 正在思考..."})]})]})})}),(0,t.jsxs)(U,{value:"files",className:"flex-1 flex flex-col m-4 mt-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"font-medium",children:"项目文件"}),(0,t.jsxs)(o,{size:"sm",variant:"outline",children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"添加"]})]}),(0,t.jsx)(V,{className:"flex-1",children:(0,t.jsx)("div",{className:"space-y-2",children:a.map(e=>(0,t.jsx)("div",{className:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>p(e.id),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(K.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"font-medium text-sm truncate",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-xs text-gray-500 mt-1",children:[(0,t.jsx)(X.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.lastModified.toLocaleDateString()})]})]})]})},e.id))})})]})]}),"chat"===e.activeTab&&(0,t.jsxs)("div",{className:"border-t p-4 space-y-3",children:[(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(o,{variant:"outline",size:"sm",children:(0,t.jsx)(Y.A,{className:"h-4 w-4"})}),(0,t.jsx)(o,{variant:"outline",size:"sm",children:(0,t.jsx)(G.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(P,{placeholder:"描述你的工作流程需求...",value:c,onChange:e=>x(e.target.value),className:"flex-1 min-h-[80px] resize-none",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())}}),(0,t.jsx)(o,{onClick:u,disabled:!c.trim()||s.isLoading,className:"self-end",children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})})]})]})]})})}var en=a(1981),ei=a(81),er=a(9621),ed=a(4211),ec=a(9295),ex=a(3454),eo=a(5541);a(1687);var em=a(4213),eh=a(3314),eu=a(646),ep=a(664);let ef=[{id:"1",type:"input",position:{x:100,y:100},data:{label:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"数据输入"})]})},style:{background:"#e3f2fd",border:"2px solid #2196f3",borderRadius:"8px",padding:"10px"}},{id:"2",position:{x:300,y:100},data:{label:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(em.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"数据验证"})]})},style:{background:"#fff3e0",border:"2px solid #ff9800",borderRadius:"8px",padding:"10px"}},{id:"3",position:{x:500,y:100},data:{label:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eh.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"智能调度"})]})},style:{background:"#f3e5f5",border:"2px solid #9c27b0",borderRadius:"8px",padding:"10px"}},{id:"4",position:{x:700,y:100},data:{label:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(eu.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"结果优化"})]})},style:{background:"#e8f5e8",border:"2px solid #4caf50",borderRadius:"8px",padding:"10px"}},{id:"5",type:"output",position:{x:900,y:100},data:{label:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ep.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"输出结果"})]})},style:{background:"#ffebee",border:"2px solid #f44336",borderRadius:"8px",padding:"10px"}}],ev=[{id:"e1-2",source:"1",target:"2",animated:!0},{id:"e2-3",source:"2",target:"3",animated:!0},{id:"e3-4",source:"3",target:"4",animated:!0},{id:"e4-5",source:"4",target:"5",animated:!0}];function ej(){let[e,s,a]=(0,ed.ck)(ef),[n,i,r]=(0,ed.fM)(ev),[d,c]=(0,l.useState)(null),x=(0,l.useCallback)(e=>i(s=>(0,ed.rN)(e,s)),[i]),m=(0,l.useCallback)((e,s)=>{c(s)},[]);return(0,t.jsxs)("div",{className:"h-full relative",children:[(0,t.jsxs)("div",{className:"absolute top-4 left-4 z-10 flex space-x-2",children:[(0,t.jsxs)(o,{size:"sm",onClick:()=>{let a={id:"".concat(e.length+1),position:{x:400*Math.random()+100,y:300*Math.random()+200},data:{label:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(_.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"新节点"})]})},style:{background:"#f5f5f5",border:"2px solid #9e9e9e",borderRadius:"8px",padding:"10px"}};s(e=>e.concat(a))},children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"添加节点"]}),(0,t.jsxs)(o,{size:"sm",variant:"outline",children:[(0,t.jsx)(em.A,{className:"h-4 w-4 mr-2"}),"数据节点"]}),(0,t.jsxs)(o,{size:"sm",variant:"outline",children:[(0,t.jsx)(eh.A,{className:"h-4 w-4 mr-2"}),"处理节点"]})]}),d&&(0,t.jsxs)("div",{className:"absolute top-4 right-4 z-10 w-64 bg-white border rounded-lg shadow-lg p-4",children:[(0,t.jsx)("h3",{className:"font-medium mb-3",children:"节点属性"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"节点名称"}),(0,t.jsx)("input",{type:"text",className:"w-full mt-1 px-3 py-2 border rounded-md text-sm",defaultValue:d.data.label})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"节点类型"}),(0,t.jsxs)("select",{className:"w-full mt-1 px-3 py-2 border rounded-md text-sm",children:[(0,t.jsx)("option",{children:"数据输入"}),(0,t.jsx)("option",{children:"数据处理"}),(0,t.jsx)("option",{children:"逻辑判断"}),(0,t.jsx)("option",{children:"结果输出"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium",children:"描述"}),(0,t.jsx)("textarea",{className:"w-full mt-1 px-3 py-2 border rounded-md text-sm",rows:3,placeholder:"节点功能描述..."})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(o,{size:"sm",className:"flex-1",children:"保存"}),(0,t.jsx)(o,{size:"sm",variant:"outline",onClick:()=>c(null),children:"关闭"})]})]})]}),(0,t.jsxs)(ed.Gc,{nodes:e,edges:n,onNodesChange:a,onEdgesChange:r,onConnect:x,onNodeClick:m,fitView:!0,className:"bg-gray-50",children:[(0,t.jsx)(ec.H,{}),(0,t.jsx)(ex.o,{nodeColor:e=>{switch(e.type){case"input":return"#2196f3";case"output":return"#f44336";default:return"#9e9e9e"}}}),(0,t.jsx)(eo.V,{variant:eo._.Dots,gap:12,size:1})]}),(0,t.jsxs)("div",{className:"absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 text-sm text-gray-600",children:[(0,t.jsx)("div",{className:"font-medium mb-1",children:"操作说明："}),(0,t.jsx)("div",{children:"• 点击节点查看/编辑属性"}),(0,t.jsx)("div",{children:"• 拖拽节点连接点创建连接"}),(0,t.jsx)("div",{children:"• 使用右下角小地图导航"})]})]})}var eg=a(9799),eN=a(4261),eb=a(7108),ew=a(5339),ey=a(4516);function e_(){let[e,s]=(0,l.useState)(1),a=[{id:1,title:"数据上传",icon:T.A},{id:2,title:"数据验证",icon:eu.A},{id:3,title:"智能调度",icon:eg.A},{id:4,title:"结果确认",icon:k.A}],n=[{id:"ORD001",customer:"客户A",material:"钢材",weight:"2.5吨",date:"2024-01-15"},{id:"ORD002",customer:"客户B",material:"水泥",weight:"3.2吨",date:"2024-01-16"},{id:"ORD003",customer:"客户C",material:"砖块",weight:"1.8吨",date:"2024-01-17"}];return(0,t.jsxs)("div",{className:"h-full flex flex-col",children:[(0,t.jsx)("div",{className:"p-6 border-b",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:a.map((s,l)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 ".concat(e>=s.id?"text-blue-600":"text-gray-400"),children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(e>=s.id?"bg-blue-600 text-white":"bg-gray-200"),children:(0,t.jsx)(s.icon,{className:"h-4 w-4"})}),(0,t.jsx)("span",{className:"font-medium text-sm",children:s.title})]}),l<a.length-1&&(0,t.jsx)("div",{className:"w-16 h-px mx-4 ".concat(e>s.id?"bg-blue-600":"bg-gray-300")})]},s.id))})}),(0,t.jsxs)("div",{className:"flex-1 p-6",children:[1===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"数据上传"}),(0,t.jsx)("p",{className:"text-gray-600",children:"请上传订单明细、物料信息和车辆信息文件"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{name:"订单明细.xlsx",icon:eN.A,uploaded:!0},{name:"物料信息.xlsx",icon:eb.A,uploaded:!0},{name:"车辆信息.xlsx",icon:eg.A,uploaded:!1}].map((e,s)=>(0,t.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-6 text-center ".concat(e.uploaded?"border-green-300 bg-green-50":"border-gray-300"),children:[(0,t.jsx)(e.icon,{className:"h-12 w-12 mx-auto mb-3 ".concat(e.uploaded?"text-green-600":"text-gray-400")}),(0,t.jsx)("div",{className:"font-medium",children:e.name}),e.uploaded?(0,t.jsx)("div",{className:"text-green-600 text-sm mt-2",children:"✓ 已上传"}):(0,t.jsxs)(o,{className:"mt-3",size:"sm",children:[(0,t.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"上传文件"]})]},s))}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)(o,{onClick:()=>s(2),children:"下一步：数据验证"})})]}),2===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"数据验证"}),(0,t.jsx)("p",{className:"text-gray-600",children:"系统正在验证上传的数据格式和完整性"}),(0,t.jsxs)(M,{defaultValue:"orders",className:"w-full",children:[(0,t.jsxs)(I,{children:[(0,t.jsx)(S,{value:"orders",children:"订单数据"}),(0,t.jsx)(S,{value:"vehicles",children:"车辆数据"})]}),(0,t.jsxs)(U,{value:"orders",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-green-800",children:[(0,t.jsx)(eu.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"订单数据验证通过"})]}),(0,t.jsxs)("div",{className:"text-sm text-green-700 mt-1",children:["共 ",n.length," 条订单记录，格式正确"]})]}),(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"订单号"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"客户"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"物料"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"重量"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"交货日期"})]})}),(0,t.jsx)("tbody",{children:n.map(e=>(0,t.jsxs)("tr",{className:"border-t",children:[(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.id}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.customer}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.material}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.weight}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.date})]},e.id))})]})})]}),(0,t.jsxs)(U,{value:"vehicles",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-yellow-800",children:[(0,t.jsx)(ew.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"车辆数据需要注意"})]}),(0,t.jsx)("div",{className:"text-sm text-yellow-700 mt-1",children:"1 辆车辆状态为维修中，不参与调度"})]}),(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"车辆编号"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"类型"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"载重"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"状态"})]})}),(0,t.jsx)("tbody",{children:[{id:"VEH001",type:"大货车",capacity:"5吨",status:"可用"},{id:"VEH002",type:"中货车",capacity:"3吨",status:"可用"},{id:"VEH003",type:"小货车",capacity:"2吨",status:"维修中"}].map(e=>(0,t.jsxs)("tr",{className:"border-t",children:[(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.id}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.type}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.capacity}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat("可用"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status})})]},e.id))})]})})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(o,{variant:"outline",onClick:()=>s(1),children:"上一步"}),(0,t.jsx)(o,{onClick:()=>s(3),children:"下一步：开始调度"})]})]}),3===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"智能调度"}),(0,t.jsx)("p",{className:"text-gray-600",children:"AI 正在为您生成最优的运输调度方案"}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,t.jsx)("span",{className:"font-medium",children:"正在计算最优调度方案..."})]}),(0,t.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("div",{children:"✓ 分析订单需求和时间约束"}),(0,t.jsx)("div",{children:"✓ 匹配车辆容量和物料属性"}),(0,t.jsx)("div",{children:"✓ 优化运输路线和成本"}),(0,t.jsx)("div",{className:"animate-pulse",children:"⏳ 生成调度结果..."})]})]}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(o,{onClick:()=>s(4),className:"px-8",children:"查看调度结果"})})]}),4===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"调度结果"}),(0,t.jsx)("p",{className:"text-gray-600",children:"以下是系统生成的最优运输调度方案"}),(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-green-800",children:[(0,t.jsx)(eu.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"调度方案生成成功"})]}),(0,t.jsx)("div",{className:"text-sm text-green-700 mt-1",children:"预计节省运输成本 15%，提高车辆利用率 20%"})]}),(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"车辆"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"分配订单"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"运输路线"}),(0,t.jsx)("th",{className:"px-4 py-2 text-left text-sm font-medium",children:"配送日期"})]})}),(0,t.jsx)("tbody",{children:[{vehicle:"VEH001",orders:["ORD001","ORD003"],route:"客户A → 客户C",date:"2024-01-15"},{vehicle:"VEH002",orders:["ORD002"],route:"客户B",date:"2024-01-16"}].map((e,s)=>(0,t.jsxs)("tr",{className:"border-t",children:[(0,t.jsx)("td",{className:"px-4 py-2 text-sm font-medium",children:e.vehicle}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:e.orders.join(", ")}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(ey.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.route})]})}),(0,t.jsx)("td",{className:"px-4 py-2 text-sm",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(X.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.date})]})})]},s))})]})}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(o,{variant:"outline",onClick:()=>s(3),children:"重新调度"}),(0,t.jsxs)("div",{className:"space-x-2",children:[(0,t.jsxs)(o,{variant:"outline",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"导出方案"]}),(0,t.jsx)(o,{children:"确认并执行"})]})]})]})]})]})}var eA=a(8749),eE=a(2332);function eT(){let[e,s]=(0,l.useState)(!0),a=e=>{navigator.clipboard.writeText(e)},n=l=>{let{code:n,language:i}=l;return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"absolute top-3 right-3 flex space-x-2",children:[(0,t.jsx)(o,{size:"sm",variant:"outline",onClick:()=>s(!e),children:e?(0,t.jsx)(eA.A,{className:"h-4 w-4"}):(0,t.jsx)(ee.A,{className:"h-4 w-4"})}),(0,t.jsx)(o,{size:"sm",variant:"outline",onClick:()=>a(n),children:(0,t.jsx)(A.A,{className:"h-4 w-4"})})]}),(0,t.jsx)(V,{className:"h-[600px]",children:(0,t.jsxs)("pre",{className:"p-4 text-sm font-mono bg-gray-900 text-gray-100 rounded-lg overflow-x-auto",children:[e&&(0,t.jsx)("div",{className:"float-left pr-4 text-gray-500 select-none",children:n.split("\n").map((e,s)=>(0,t.jsx)("div",{className:"text-right",children:s+1},s))}),(0,t.jsx)("code",{className:"language-".concat(i),children:n})]})})]})};return(0,t.jsxs)("div",{className:"h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"font-medium",children:"生成的应用代码"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(o,{size:"sm",variant:"outline",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"下载代码"]}),(0,t.jsxs)(o,{size:"sm",children:[(0,t.jsx)(eE.A,{className:"h-4 w-4 mr-2"}),"部署应用"]})]})]}),(0,t.jsx)("div",{className:"flex-1 p-4",children:(0,t.jsxs)(M,{defaultValue:"python",className:"h-full flex flex-col",children:[(0,t.jsxs)(I,{className:"grid w-full grid-cols-3",children:[(0,t.jsxs)(S,{value:"python",className:"flex items-center space-x-2",children:[(0,t.jsx)(eE.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Python 算法"})]}),(0,t.jsxs)(S,{value:"config",className:"flex items-center space-x-2",children:[(0,t.jsx)(_.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"配置文件"})]}),(0,t.jsxs)(S,{value:"sql",className:"flex items-center space-x-2",children:[(0,t.jsx)(em.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"数据库"})]})]}),(0,t.jsx)(U,{value:"python",className:"flex-1 mt-4",children:(0,t.jsx)(n,{code:"# 订单运输调度系统\n# 智能调度算法实现\n\nimport pandas as pd\nimport numpy as np\nfrom datetime import datetime, timedelta\nfrom typing import List, Dict, Tuple\n\nclass TransportScheduler:\n    \"\"\"运输调度器\"\"\"\n    \n    def __init__(self):\n        self.orders = []\n        self.vehicles = []\n        self.schedule_result = []\n    \n    def load_data(self, orders_file: str, vehicles_file: str):\n        \"\"\"加载订单和车辆数据\"\"\"\n        try:\n            self.orders = pd.read_excel(orders_file)\n            self.vehicles = pd.read_excel(vehicles_file)\n            return True\n        except Exception as e:\n            print(f\"数据加载失败: {e}\")\n            return False\n    \n    def validate_data(self) -> Dict[str, bool]:\n        \"\"\"验证数据完整性\"\"\"\n        validation_result = {\n            'orders_valid': False,\n            'vehicles_valid': False\n        }\n        \n        # 验证订单数据\n        required_order_columns = ['订单号', '客户', '物料', '重量', '交货日期']\n        if all(col in self.orders.columns for col in required_order_columns):\n            validation_result['orders_valid'] = True\n        \n        # 验证车辆数据\n        required_vehicle_columns = ['车辆编号', '类型', '载重', '状态']\n        if all(col in self.vehicles.columns for col in required_vehicle_columns):\n            validation_result['vehicles_valid'] = True\n        \n        return validation_result\n    \n    def calculate_optimal_schedule(self) -> List[Dict]:\n        \"\"\"计算最优调度方案\"\"\"\n        available_vehicles = self.vehicles[\n            self.vehicles['状态'] == '可用'\n        ].copy()\n        \n        schedule = []\n        \n        for _, vehicle in available_vehicles.iterrows():\n            vehicle_capacity = float(vehicle['载重'].replace('吨', ''))\n            assigned_orders = []\n            current_load = 0\n            \n            for _, order in self.orders.iterrows():\n                order_weight = float(order['重量'].replace('吨', ''))\n                \n                if current_load + order_weight <= vehicle_capacity:\n                    assigned_orders.append(order['订单号'])\n                    current_load += order_weight\n            \n            if assigned_orders:\n                schedule.append({\n                    'vehicle': vehicle['车辆编号'],\n                    'orders': assigned_orders,\n                    'total_weight': current_load,\n                    'utilization': (current_load / vehicle_capacity) * 100\n                })\n        \n        return schedule\n    \n    def export_schedule(self, filename: str):\n        \"\"\"导出调度结果\"\"\"\n        if self.schedule_result:\n            df = pd.DataFrame(self.schedule_result)\n            df.to_excel(filename, index=False)\n            return True\n        return False\n\n# 使用示例\nif __name__ == \"__main__\":\n    scheduler = TransportScheduler()\n    \n    # 加载数据\n    if scheduler.load_data(\"orders.xlsx\", \"vehicles.xlsx\"):\n        print(\"数据加载成功\")\n        \n        # 验证数据\n        validation = scheduler.validate_data()\n        if all(validation.values()):\n            print(\"数据验证通过\")\n            \n            # 计算调度方案\n            schedule = scheduler.calculate_optimal_schedule()\n            scheduler.schedule_result = schedule\n            \n            print(f\"生成调度方案，共 {len(schedule)} 个分配\")\n            \n            # 导出结果\n            scheduler.export_schedule(\"schedule_result.xlsx\")\n            print(\"调度结果已导出\")\n        else:\n            print(\"数据验证失败\")\n    else:\n        print(\"数据加载失败\")",language:"python"})}),(0,t.jsx)(U,{value:"config",className:"flex-1 mt-4",children:(0,t.jsx)(n,{code:"# 配置文件\n# config.yaml\n\n# 数据库配置\ndatabase:\n  host: localhost\n  port: 5432\n  name: transport_db\n  user: admin\n  password: password\n\n# 调度算法参数\nscheduler:\n  # 车辆利用率目标 (%)\n  target_utilization: 85\n  \n  # 最大配送距离 (km)\n  max_delivery_distance: 200\n  \n  # 时间窗口容忍度 (小时)\n  time_window_tolerance: 2\n  \n  # 优化目标权重\n  weights:\n    cost: 0.4\n    time: 0.3\n    utilization: 0.3\n\n# API 配置\napi:\n  host: 0.0.0.0\n  port: 8000\n  debug: true\n  \n# 文件上传配置\nupload:\n  max_file_size: 10MB\n  allowed_extensions:\n    - xlsx\n    - xls\n    - csv\n  \n# 日志配置\nlogging:\n  level: INFO\n  file: logs/scheduler.log\n  max_size: 100MB\n  backup_count: 5",language:"yaml"})}),(0,t.jsx)(U,{value:"sql",className:"flex-1 mt-4",children:(0,t.jsx)(n,{code:"-- 数据库表结构\n-- 订单表\nCREATE TABLE orders (\n    id SERIAL PRIMARY KEY,\n    order_no VARCHAR(50) UNIQUE NOT NULL,\n    customer_name VARCHAR(100) NOT NULL,\n    material_type VARCHAR(100) NOT NULL,\n    weight DECIMAL(10,2) NOT NULL,\n    delivery_date DATE NOT NULL,\n    status VARCHAR(20) DEFAULT 'pending',\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- 车辆表\nCREATE TABLE vehicles (\n    id SERIAL PRIMARY KEY,\n    vehicle_no VARCHAR(50) UNIQUE NOT NULL,\n    vehicle_type VARCHAR(50) NOT NULL,\n    capacity DECIMAL(10,2) NOT NULL,\n    status VARCHAR(20) DEFAULT 'available',\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- 调度结果表\nCREATE TABLE schedules (\n    id SERIAL PRIMARY KEY,\n    vehicle_id INTEGER REFERENCES vehicles(id),\n    order_ids INTEGER[] NOT NULL,\n    route TEXT,\n    scheduled_date DATE NOT NULL,\n    total_weight DECIMAL(10,2),\n    utilization_rate DECIMAL(5,2),\n    status VARCHAR(20) DEFAULT 'planned',\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- 创建索引\nCREATE INDEX idx_orders_delivery_date ON orders(delivery_date);\nCREATE INDEX idx_vehicles_status ON vehicles(status);\nCREATE INDEX idx_schedules_scheduled_date ON schedules(scheduled_date);\n\n-- 插入示例数据\nINSERT INTO orders (order_no, customer_name, material_type, weight, delivery_date) VALUES\n('ORD001', '客户A', '钢材', 2.5, '2024-01-15'),\n('ORD002', '客户B', '水泥', 3.2, '2024-01-16'),\n('ORD003', '客户C', '砖块', 1.8, '2024-01-17');\n\nINSERT INTO vehicles (vehicle_no, vehicle_type, capacity, status) VALUES\n('VEH001', '大货车', 5.0, 'available'),\n('VEH002', '中货车', 3.0, 'available'),\n('VEH003', '小货车', 2.0, 'maintenance');",language:"sql"})})]})})]})}function ek(){let{canvas:e,setCanvasMode:s}=z();return(0,t.jsxs)("div",{className:"flex-1 flex flex-col bg-gray-50",children:[(0,t.jsxs)("div",{className:"h-14 bg-white border-b flex items-center justify-between px-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("h2",{className:"font-medium",children:"应用构建器"}),(0,t.jsx)("div",{className:"h-4 w-px bg-gray-300"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"实时预览 • 自动保存"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(o,{variant:"outline",size:"sm",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"配置"]}),(0,t.jsxs)(o,{variant:"outline",size:"sm",children:[(0,t.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"全屏"]}),(0,t.jsxs)(o,{size:"sm",className:"bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"运行测试"]})]})]}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)(M,{value:e.mode,onValueChange:e=>s(e),className:"h-full flex flex-col",children:[(0,t.jsxs)(I,{className:"grid w-full grid-cols-3 mx-6 mt-4 mb-0",children:[(0,t.jsxs)(S,{value:"flow",className:"flex items-center space-x-2",children:[(0,t.jsx)(ei.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"流程设计"})]}),(0,t.jsxs)(S,{value:"preview",className:"flex items-center space-x-2",children:[(0,t.jsx)(ee.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"应用预览"})]}),(0,t.jsxs)(S,{value:"code",className:"flex items-center space-x-2",children:[(0,t.jsx)(er.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"代码查看"})]})]}),(0,t.jsx)(U,{value:"flow",className:"flex-1 m-6 mt-4",children:(0,t.jsx)("div",{className:"h-full bg-white rounded-lg border shadow-sm",children:(0,t.jsx)(ej,{})})}),(0,t.jsx)(U,{value:"preview",className:"flex-1 m-6 mt-4",children:(0,t.jsx)("div",{className:"h-full bg-white rounded-lg border shadow-sm",children:(0,t.jsx)(e_,{})})}),(0,t.jsx)(U,{value:"code",className:"flex-1 m-6 mt-4",children:(0,t.jsx)("div",{className:"h-full bg-white rounded-lg border shadow-sm",children:(0,t.jsx)(eT,{})})})]})})]})}function eR(){return(0,t.jsxs)("div",{className:"h-screen flex flex-col bg-gray-50",children:[(0,t.jsx)(L,{}),(0,t.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,t.jsx)(el,{}),(0,t.jsx)(ek,{})]})]})}},9956:(e,s,a)=>{Promise.resolve().then(a.bind(a,2865))}},e=>{var s=s=>e(e.s=s);e.O(0,[294,702,609,441,684,358],()=>s(9956)),_N_E=e.O()}]);