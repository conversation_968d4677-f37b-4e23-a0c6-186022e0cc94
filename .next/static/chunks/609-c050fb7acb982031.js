(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[609],{81:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]])},333:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("thumbs-up",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]])},381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},394:(e,t,n)=>{"use strict";function r(){}function o(e){return null==e?r:function(){return this.querySelector(e)}}n.d(t,{A:()=>o})},425:(e,t,n)=>{"use strict";n.d(t,{h:()=>d,n:()=>c});var r=n(2115),o=n(5643);let i=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},a=e=>e?i(e):i,{useDebugValue:l}=r,{useSyncExternalStoreWithSelector:s}=o,u=e=>e;function c(e,t=u,n){let r=s(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return l(r),r}let f=(e,t)=>{let n=a(e),r=(e,r=t)=>c(n,e,r);return Object.assign(r,n),r},d=(e,t)=>e?f(e,t):f},492:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]])},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},664:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-output",[["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M4 7V4a2 2 0 0 1 2-2 2 2 0 0 0-2 2",key:"1vk7w2"}],["path",{d:"M4.063 20.999a2 2 0 0 0 2 1L18 22a2 2 0 0 0 2-2V7l-5-5H6",key:"1jink5"}],["path",{d:"m5 11-3 3",key:"1dgrs4"}],["path",{d:"m5 17-3-3h10",key:"1mvvaf"}]])},704:(e,t,n)=>{"use strict";n.d(t,{B8:()=>R,UC:()=>N,bL:()=>C,l9:()=>j});var r=n(2115),o=n(5185),i=n(6081),a=n(9196),l=n(8905),s=n(3655),u=n(4315),c=n(5845),f=n(1285),d=n(5155),h="Tabs",[p,m]=(0,i.A)(h,[a.RG]),v=(0,a.RG)(),[g,y]=p(h),w=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:p="automatic",...m}=e,v=(0,u.jH)(l),[y,w]=(0,c.i)({prop:r,onChange:o,defaultProp:null!=i?i:"",caller:h});return(0,d.jsx)(g,{scope:n,baseId:(0,f.B)(),value:y,onValueChange:w,orientation:a,dir:v,activationMode:p,children:(0,d.jsx)(s.sG.div,{dir:v,"data-orientation":a,...m,ref:t})})});w.displayName=h;var b="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=y(b,n),l=v(n);return(0,d.jsx)(a.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,d.jsx)(s.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});x.displayName=b;var k="TabsTrigger",_=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...l}=e,u=y(k,n),c=v(n),f=M(u.baseId,r),h=S(u.baseId,r),p=r===u.value;return(0,d.jsx)(a.q7,{asChild:!0,...c,focusable:!i,active:p,children:(0,d.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:f,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==u.activationMode;p||i||!e||u.onValueChange(r)})})})});_.displayName=k;var E="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:a,...u}=e,c=y(E,n),f=M(c.baseId,o),h=S(c.baseId,o),p=o===c.value,m=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(l.C,{present:i||p,children:n=>{let{present:r}=n;return(0,d.jsx)(s.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:h,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&a})}})});function M(e,t){return"".concat(e,"-trigger-").concat(t)}function S(e,t){return"".concat(e,"-content-").concat(t)}A.displayName=E;var C=w,R=x,j=_,N=A},806:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>a,GK:()=>i,Rw:()=>o,vr:()=>r});let r={passive:!1},o={capture:!0,passive:!1};function i(e){e.stopImmediatePropagation()}function a(e){e.preventDefault(),e.stopImmediatePropagation()}},1235:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r={value:()=>{}};function o(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw Error("illegal type: "+e);r[e]=[]}return new i(r)}function i(e){this._=e}function a(e,t,n){for(var o=0,i=e.length;o<i;++o)if(e[o].name===t){e[o]=r,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}i.prototype=o.prototype={constructor:i,on:function(e,t){var n,r=this._,o=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),i=-1,l=o.length;if(arguments.length<2){for(;++i<l;)if((n=(e=o[i]).type)&&(n=function(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}(r[n],e.name)))return n;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++i<l;)if(n=(e=o[i]).type)r[n]=a(r[n],e.name,t);else if(null==t)for(n in r)r[n]=a(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new i(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=Array(n),i=0;i<n;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(r=this._[e],i=0,n=r.length;i<n;++i)r[i].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};let l=o},1285:(e,t,n)=>{"use strict";n.d(t,{B:()=>s});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function s(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},1366:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1414:(e,t,n)=>{"use strict";e.exports=n(2436)},1687:()=>{},1774:(e,t,n)=>{"use strict";n.d(t,{A:()=>o,j:()=>i});var r=n(7271);function o(e,t,n){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}:function(e,t,n){return function(){this.style.setProperty(e,t,n)}})(e,t,null==n?"":n)):i(this.node(),e)}function i(e,t){return e.style.getPropertyValue(t)||(0,r.A)(e).getComputedStyle(e,null).getPropertyValue(t)}},1788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1981:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("maximize-2",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"m21 3-7 7",key:"1l2asr"}],["path",{d:"m3 21 7-7",key:"tjx5ai"}],["path",{d:"M9 21H3v-6",key:"wtvkvv"}]])},2085:(e,t,n)=>{"use strict";n.d(t,{F:()=>a});var r=n(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.$,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:l}=t,s=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let i=o(t)||o(r);return a[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,s,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...u}[t]):({...l,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2178:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},2332:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-code",[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]])},2436:(e,t,n)=>{"use strict";var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return l(function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})},[e,n,t]),a(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},2486:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2525:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2596:(e,t,n)=>{"use strict";function r(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}n.d(t,{$:()=>r})},2657:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2712:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},2903:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(4498);function o(e){return"string"==typeof e?new r.LN([[document.querySelector(e)]],[document.documentElement]):new r.LN([[e]],r.zr)}},3219:(e,t,n)=>{"use strict";n.d(t,{s_:()=>eJ,GS:()=>eB});var r,o=n(1235),i=n(9204);function a(e){return((e=Math.exp(e))+1/e)/2}let l=function e(t,n,r){function o(e,o){var i,l,s=e[0],u=e[1],c=e[2],f=o[0],d=o[1],h=o[2],p=f-s,m=d-u,v=p*p+m*m;if(v<1e-12)l=Math.log(h/c)/t,i=function(e){return[s+e*p,u+e*m,c*Math.exp(t*e*l)]};else{var g=Math.sqrt(v),y=(h*h-c*c+r*v)/(2*c*n*g),w=(h*h-c*c-r*v)/(2*h*n*g),b=Math.log(Math.sqrt(y*y+1)-y);l=(Math.log(Math.sqrt(w*w+1)-w)-b)/t,i=function(e){var r,o,i=e*l,f=a(b),d=c/(n*g)*(f*(((r=Math.exp(2*(r=t*i+b)))-1)/(r+1))-((o=Math.exp(o=b))-1/o)/2);return[s+d*p,u+d*m,c*f/a(t*i+b)]}}return i.duration=1e3*l*t/Math.SQRT2,i}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4);var s,u,c=n(2903),f=n(4897),d=n(4498),h=0,p=0,m=0,v=0,g=0,y=0,w="object"==typeof performance&&performance.now?performance:Date,b="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function x(){return g||(b(k),g=w.now()+y)}function k(){g=0}function _(){this._call=this._time=this._next=null}function E(e,t,n){var r=new _;return r.restart(e,t,n),r}function A(){g=(v=w.now())+y,h=p=0;try{x(),++h;for(var e,t=s;t;)(e=g-t._time)>=0&&t._call.call(void 0,e),t=t._next;--h}finally{h=0,function(){for(var e,t,n=s,r=1/0;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:s=t);u=e,S(r)}(),g=0}}function M(){var e=w.now(),t=e-v;t>1e3&&(y-=t,v=e)}function S(e){!h&&(p&&(p=clearTimeout(p)),e-g>24?(e<1/0&&(p=setTimeout(A,e-w.now()-y)),m&&(m=clearInterval(m))):(m||(v=w.now(),m=setInterval(M,1e3)),h=1,b(A)))}function C(e,t,n){var r=new _;return t=null==t?0:+t,r.restart(n=>{r.stop(),e(n+t)},t,n),r}_.prototype=E.prototype={constructor:_,restart:function(e,t,n){if("function"!=typeof e)throw TypeError("callback is not a function");n=(null==n?x():+n)+(null==t?0:+t),this._next||u===this||(u?u._next=this:s=this,u=this),this._call=e,this._time=n,S()},stop:function(){this._call&&(this._call=null,this._time=1/0,S())}};var R=(0,o.A)("start","end","cancel","interrupt"),j=[];function N(e,t,n,r,o,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var r,o=e.__transition;function i(s){var u,c,f,d;if(1!==n.state)return l();for(u in o)if((d=o[u]).name===n.name){if(3===d.state)return C(i);4===d.state?(d.state=6,d.timer.stop(),d.on.call("interrupt",e,e.__data__,d.index,d.group),delete o[u]):+u<t&&(d.state=6,d.timer.stop(),d.on.call("cancel",e,e.__data__,d.index,d.group),delete o[u])}if(C(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(u=0,n.state=3,r=Array(f=n.tween.length),c=-1;u<f;++u)(d=n.tween[u].value.call(e,e.__data__,n.index,n.group))&&(r[++c]=d);r.length=c+1}}function a(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=r.length;++i<a;)r[i].call(e,o);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var r in n.state=6,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=E(function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)},0,n.time)}(e,n,{name:t,index:r,group:o,on:R,tween:j,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function T(e,t){var n=z(e,t);if(n.state>0)throw Error("too late; already scheduled");return n}function P(e,t){var n=z(e,t);if(n.state>3)throw Error("too late; already running");return n}function z(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw Error("transition not found");return n}function L(e,t){var n,r,o,i=e.__transition,a=!0;if(i){for(o in t=null==t?null:t+"",i){if((n=i[o]).name!==t){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[o]}a&&delete e.__transition}}function O(e,t){return e*=1,t*=1,function(n){return e*(1-n)+t*n}}var D=180/Math.PI,I={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function H(e,t,n,r,o,i){var a,l,s;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(s=e*n+t*r)&&(n-=e*s,r-=t*s),(l=Math.sqrt(n*n+r*r))&&(n/=l,r/=l,s/=l),e*r<t*n&&(e=-e,t=-t,s=-s,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*D,skewX:Math.atan(s)*D,scaleX:a,scaleY:l}}function $(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(i,a){var l,s,u,c,f=[],d=[];return i=e(i),a=e(a),!function(e,r,o,i,a,l){if(e!==o||r!==i){var s=a.push("translate(",null,t,null,n);l.push({i:s-4,x:O(e,o)},{i:s-2,x:O(r,i)})}else(o||i)&&a.push("translate("+o+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,f,d),l=i.rotate,s=a.rotate,l!==s?(l-s>180?s+=360:s-l>180&&(l+=360),d.push({i:f.push(o(f)+"rotate(",null,r)-2,x:O(l,s)})):s&&f.push(o(f)+"rotate("+s+r),u=i.skewX,c=a.skewX,u!==c?d.push({i:f.push(o(f)+"skewX(",null,r)-2,x:O(u,c)}):c&&f.push(o(f)+"skewX("+c+r),!function(e,t,n,r,i,a){if(e!==n||t!==r){var l=i.push(o(i)+"scale(",null,",",null,")");a.push({i:l-4,x:O(e,n)},{i:l-2,x:O(t,r)})}else(1!==n||1!==r)&&i.push(o(i)+"scale("+n+","+r+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,f,d),i=a=null,function(e){for(var t,n=-1,r=d.length;++n<r;)f[(t=d[n]).i]=t.x(e);return f.join("")}}}var F=$(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?I:H(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),V=$(function(e){return null==e?I:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",e),e=r.transform.baseVal.consolidate())?H((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):I},", ",")",")"),W=n(6102);function B(e,t,n){var r=e._id;return e.each(function(){var e=P(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)}),function(e){return z(e,r).value[t]}}function G(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function q(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function X(){}var Y="\\s*([+-]?\\d+)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",U="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Z=/^#([0-9a-f]{3,8})$/,Q=RegExp(`^rgb\\(${Y},${Y},${Y}\\)$`),J=RegExp(`^rgb\\(${U},${U},${U}\\)$`),ee=RegExp(`^rgba\\(${Y},${Y},${Y},${K}\\)$`),et=RegExp(`^rgba\\(${U},${U},${U},${K}\\)$`),en=RegExp(`^hsl\\(${K},${U},${U}\\)$`),er=RegExp(`^hsla\\(${K},${U},${U},${K}\\)$`),eo={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function ei(){return this.rgb().formatHex()}function ea(){return this.rgb().formatRgb()}function el(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Z.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?es(t):3===n?new ef(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?eu(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?eu(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Q.exec(e))?new ef(t[1],t[2],t[3],1):(t=J.exec(e))?new ef(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=ee.exec(e))?eu(t[1],t[2],t[3],t[4]):(t=et.exec(e))?eu(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=en.exec(e))?eg(t[1],t[2]/100,t[3]/100,1):(t=er.exec(e))?eg(t[1],t[2]/100,t[3]/100,t[4]):eo.hasOwnProperty(e)?es(eo[e]):"transparent"===e?new ef(NaN,NaN,NaN,0):null}function es(e){return new ef(e>>16&255,e>>8&255,255&e,1)}function eu(e,t,n,r){return r<=0&&(e=t=n=NaN),new ef(e,t,n,r)}function ec(e,t,n,r){var o;return 1==arguments.length?((o=e)instanceof X||(o=el(o)),o)?new ef((o=o.rgb()).r,o.g,o.b,o.opacity):new ef:new ef(e,t,n,null==r?1:r)}function ef(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function ed(){return`#${ev(this.r)}${ev(this.g)}${ev(this.b)}`}function eh(){let e=ep(this.opacity);return`${1===e?"rgb(":"rgba("}${em(this.r)}, ${em(this.g)}, ${em(this.b)}${1===e?")":`, ${e})`}`}function ep(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function em(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ev(e){return((e=em(e))<16?"0":"")+e.toString(16)}function eg(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new ew(e,t,n,r)}function ey(e){if(e instanceof ew)return new ew(e.h,e.s,e.l,e.opacity);if(e instanceof X||(e=el(e)),!e)return new ew;if(e instanceof ew)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=NaN,l=i-o,s=(i+o)/2;return l?(a=t===i?(n-r)/l+(n<r)*6:n===i?(r-t)/l+2:(t-n)/l+4,l/=s<.5?i+o:2-i-o,a*=60):l=s>0&&s<1?0:a,new ew(a,l,s,e.opacity)}function ew(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function eb(e){return(e=(e||0)%360)<0?e+360:e}function ex(e){return Math.max(0,Math.min(1,e||0))}function ek(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}function e_(e,t,n,r,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*n+(1+3*e+3*i-3*a)*r+a*o)/6}G(X,el,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:ei,formatHex:ei,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return ey(this).formatHsl()},formatRgb:ea,toString:ea}),G(ef,ec,q(X,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ef(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ef(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ef(em(this.r),em(this.g),em(this.b),ep(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ed,formatHex:ed,formatHex8:function(){return`#${ev(this.r)}${ev(this.g)}${ev(this.b)}${ev((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:eh,toString:eh})),G(ew,function(e,t,n,r){return 1==arguments.length?ey(e):new ew(e,t,n,null==r?1:r)},q(X,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ew(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ew(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new ef(ek(e>=240?e-240:e+120,o,r),ek(e,o,r),ek(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new ew(eb(this.h),ex(this.s),ex(this.l),ep(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=ep(this.opacity);return`${1===e?"hsl(":"hsla("}${eb(this.h)}, ${100*ex(this.s)}%, ${100*ex(this.l)}%${1===e?")":`, ${e})`}`}}));let eE=e=>()=>e;function eA(e,t){var n,r,o=t-e;return o?(n=e,r=o,function(e){return n+e*r}):eE(isNaN(e)?t:e)}let eM=function e(t){var n,r=1==(n=+t)?eA:function(e,t){var r,o,i;return t-e?(r=e,o=t,r=Math.pow(r,i=n),o=Math.pow(o,i)-r,i=1/i,function(e){return Math.pow(r+e*o,i)}):eE(isNaN(e)?t:e)};function o(e,t){var n=r((e=ec(e)).r,(t=ec(t)).r),o=r(e.g,t.g),i=r(e.b,t.b),a=eA(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function eS(e){return function(t){var n,r,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(n=0;n<o;++n)r=ec(t[n]),i[n]=r.r||0,a[n]=r.g||0,l[n]=r.b||0;return i=e(i),a=e(a),l=e(l),r.opacity=1,function(e){return r.r=i(e),r.g=a(e),r.b=l(e),r+""}}}eS(function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),o=e[r],i=e[r+1],a=r>0?e[r-1]:2*o-i,l=r<t-1?e[r+2]:2*i-o;return e_((n-r/t)*t,a,o,i,l)}}),eS(function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),o=e[(r+t-1)%t],i=e[r%t],a=e[(r+1)%t],l=e[(r+2)%t];return e_((n-r/t)*t,o,i,a,l)}});var eC=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eR=RegExp(eC.source,"g");function ej(e,t){var n;return("number"==typeof t?O:t instanceof el?eM:(n=el(t))?(t=n,eM):function(e,t){var n,r,o,i,a,l=eC.lastIndex=eR.lastIndex=0,s=-1,u=[],c=[];for(e+="",t+="";(o=eC.exec(e))&&(i=eR.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),u[s]?u[s]+=a:u[++s]=a),(o=o[0])===(i=i[0])?u[s]?u[s]+=i:u[++s]=i:(u[++s]=null,c.push({i:s,x:O(o,i)})),l=eR.lastIndex;return l<t.length&&(a=t.slice(l),u[s]?u[s]+=a:u[++s]=a),u.length<2?c[0]?(n=c[0].x,function(e){return n(e)+""}):(r=t,function(){return r}):(t=c.length,function(e){for(var n,r=0;r<t;++r)u[(n=c[r]).i]=n.x(e);return u.join("")})})(e,t)}var eN=n(3875),eT=n(394),eP=n(9293),ez=d.Ay.prototype.constructor,eL=n(1774);function eO(e){return function(){this.style.removeProperty(e)}}var eD=0;function eI(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}var eH=d.Ay.prototype;eI.prototype=(function(e){return(0,d.Ay)().transition(e)}).prototype={constructor:eI,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=(0,eT.A)(e));for(var r=this._groups,o=r.length,i=Array(o),a=0;a<o;++a)for(var l,s,u=r[a],c=u.length,f=i[a]=Array(c),d=0;d<c;++d)(l=u[d])&&(s=e.call(l,l.__data__,d,u))&&("__data__"in l&&(s.__data__=l.__data__),f[d]=s,N(f[d],t,n,d,f,z(l,n)));return new eI(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=(0,eP.A)(e));for(var r=this._groups,o=r.length,i=[],a=[],l=0;l<o;++l)for(var s,u=r[l],c=u.length,f=0;f<c;++f)if(s=u[f]){for(var d,h=e.call(s,s.__data__,f,u),p=z(s,n),m=0,v=h.length;m<v;++m)(d=h[m])&&N(d,t,n,m,h,p);i.push(h),a.push(s)}return new eI(i,a,t,n)},selectChild:eH.selectChild,selectChildren:eH.selectChildren,filter:function(e){"function"!=typeof e&&(e=(0,eN.A)(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],l=a.length,s=r[o]=[],u=0;u<l;++u)(i=a[u])&&e.call(i,i.__data__,u,a)&&s.push(i);return new eI(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=Array(r),l=0;l<i;++l)for(var s,u=t[l],c=n[l],f=u.length,d=a[l]=Array(f),h=0;h<f;++h)(s=u[h]||c[h])&&(d[h]=s);for(;l<r;++l)a[l]=t[l];return new eI(a,this._parents,this._name,this._id)},selection:function(){return new ez(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=++eD,r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],s=l.length,u=0;u<s;++u)if(a=l[u]){var c=z(a,t);N(a,e,n,u,l,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new eI(r,this._parents,e,n)},call:eH.call,nodes:eH.nodes,node:eH.node,size:eH.size,empty:eH.empty,each:eH.each,on:function(e,t){var n,r,o,i,a,l,s=this._id;return arguments.length<2?z(this.node(),s).on.on(e):this.each((n=s,r=e,o=t,l=(r+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?T:P,function(){var e=l(this,n),t=e.on;t!==i&&(a=(i=t).copy()).on(r,o),e.on=a}))},attr:function(e,t){var n=(0,W.A)(e),r="transform"===n?V:ej;return this.attrTween(e,"function"==typeof t?(n.local?function(e,t,n){var r,o,i;return function(){var a,l,s=n(this);return null==s?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local))===(l=s+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,s))}}:function(e,t,n){var r,o,i;return function(){var a,l,s=n(this);return null==s?void this.removeAttribute(e):(a=this.getAttribute(e))===(l=s+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,s))}})(n,r,B(this,"attr."+e,t)):null==t?(n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(n):(n.local?function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===r?o:o=t(r=a,n)}}:function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===r?o:o=t(r=a,n)}})(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();var r=(0,W.A)(e);return this.tween(n,(r.local?function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttributeNS(e.space,e.local,o.call(this,t))}),n}return o._value=t,o}:function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttribute(e,o.call(this,t))}),n}return o._value=t,o})(r,t))},style:function(e,t,n){var r,o,i,a,l,s,u,c,f,d,h,p,m,v,g,y,w,b,x,k,_,E="transform"==(e+="")?F:ej;return null==t?this.styleTween(e,(r=e,function(){var e=(0,eL.j)(this,r),t=(this.style.removeProperty(r),(0,eL.j)(this,r));return e===t?null:e===o&&t===i?a:a=E(o=e,i=t)})).on("end.style."+e,eO(e)):"function"==typeof t?this.styleTween(e,(l=e,s=B(this,"style."+e,t),function(){var e=(0,eL.j)(this,l),t=s(this),n=t+"";return null==t&&(this.style.removeProperty(l),n=t=(0,eL.j)(this,l)),e===n?null:e===u&&n===c?f:(c=n,f=E(u=e,t))})).each((d=this._id,w="end."+(y="style."+(h=e)),function(){var e=P(this,d),t=e.on,n=null==e.value[y]?g||(g=eO(h)):void 0;(t!==p||v!==n)&&(m=(p=t).copy()).on(w,v=n),e.on=m})):this.styleTween(e,(b=e,_=t+"",function(){var e=(0,eL.j)(this,b);return e===_?null:e===x?k:k=E(x=e,t)}),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw Error();return this.tween(r,function(e,t,n){var r,o;function i(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&function(t){this.style.setProperty(e,i.call(this,t),n)}),r}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){var t,n;return this.tween("text","function"==typeof e?(t=B(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(n=null==e?"":e+"",function(){this.textContent=n}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){this.textContent=r.call(this,e)}),t}return r._value=e,r}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=z(this.node(),n).tween,i=0,a=o.length;i<a;++i)if((r=o[i]).name===e)return r.value;return null}return this.each((null==t?function(e,t){var n,r;return function(){var o=P(this,e),i=o.tween;if(i!==n){r=n=i;for(var a=0,l=r.length;a<l;++a)if(r[a].name===t){(r=r.slice()).splice(a,1);break}}o.tween=r}}:function(e,t,n){var r,o;if("function"!=typeof n)throw Error();return function(){var i=P(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var l={name:t,value:n},s=0,u=o.length;s<u;++s)if(o[s].name===t){o[s]=l;break}s===u&&o.push(l)}i.tween=o}})(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){T(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){T(this,e).delay=t}})(t,e)):z(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){P(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){P(this,e).duration=t}})(t,e)):z(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){P(this,e).ease=t}}(t,e)):z(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw Error();P(this,t).ease=n}))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,a){var l={value:a},s={value:function(){0==--o&&i()}};n.each(function(){var n=P(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(l),t._.interrupt.push(l),t._.end.push(s)),n.on=t}),0===o&&i()})},[Symbol.iterator]:eH[Symbol.iterator]};var e$={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};d.Ay.prototype.interrupt=function(e){return this.each(function(){L(this,e)})},d.Ay.prototype.transition=function(e){var t,n;e instanceof eI?(t=e._id,e=e._name):(t=++eD,(n=e$).time=x(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],s=l.length,u=0;u<s;++u)(a=l[u])&&N(a,e,t,u,l,n||function(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return n}(a,t));return new eI(r,this._parents,e,t)};let eF=e=>()=>e;function eV(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function eW(e,t,n){this.k=e,this.x=t,this.y=n}eW.prototype={constructor:eW,scale:function(e){return 1===e?this:new eW(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new eW(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var eB=new eW(1,0,0);function eG(e){e.stopImmediatePropagation()}function eq(e){e.preventDefault(),e.stopImmediatePropagation()}function eX(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function eY(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function eK(){return this.__zoom||eB}function eU(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function eZ(){return navigator.maxTouchPoints||"ontouchstart"in this}function eQ(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function eJ(){var e,t,n,r=eX,a=eY,s=eQ,u=eU,d=eZ,h=[0,1/0],p=[[-1/0,-1/0],[1/0,1/0]],m=250,v=l,g=(0,o.A)("start","zoom","end"),y=0,w=10;function b(e){e.property("__zoom",eK).on("wheel.zoom",S,{passive:!1}).on("mousedown.zoom",C).on("dblclick.zoom",R).filter(d).on("touchstart.zoom",j).on("touchmove.zoom",N).on("touchend.zoom touchcancel.zoom",T).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(e,t){return(t=Math.max(h[0],Math.min(h[1],t)))===e.k?e:new eW(t,e.x,e.y)}function k(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new eW(e.k,r,o)}function _(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function E(e,t,n,r){e.on("start.zoom",function(){A(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){A(this,arguments).event(r).end()}).tween("zoom",function(){var e=arguments,o=A(this,e).event(r),i=a.apply(this,e),l=null==n?_(i):"function"==typeof n?n.apply(this,e):n,s=Math.max(i[1][0]-i[0][0],i[1][1]-i[0][1]),u=this.__zoom,c="function"==typeof t?t.apply(this,e):t,f=v(u.invert(l).concat(s/u.k),c.invert(l).concat(s/c.k));return function(e){if(1===e)e=c;else{var t=f(e),n=s/t[2];e=new eW(n,l[0]-t[0]*n,l[1]-t[1]*n)}o.zoom(null,e)}})}function A(e,t,n){return!n&&e.__zooming||new M(e,t)}function M(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=a.apply(e,t),this.taps=0}function S(e,...t){if(r.apply(this,arguments)){var n=A(this,t).event(e),o=this.__zoom,i=Math.max(h[0],Math.min(h[1],o.k*Math.pow(2,u.apply(this,arguments)))),a=(0,f.A)(e);if(n.wheel)(n.mouse[0][0]!==a[0]||n.mouse[0][1]!==a[1])&&(n.mouse[1]=o.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(o.k===i)return;n.mouse=[a,o.invert(a)],L(this),n.start()}eq(e),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",s(k(x(o,i),n.mouse[0],n.mouse[1]),n.extent,p))}}function C(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,a=A(this,t,!0).event(e),l=(0,c.A)(e.view).on("mousemove.zoom",function(e){if(eq(e),!a.moved){var t=e.clientX-d,n=e.clientY-h;a.moved=t*t+n*n>y}a.event(e).zoom("mouse",s(k(a.that.__zoom,a.mouse[0]=(0,f.A)(e,o),a.mouse[1]),a.extent,p))},!0).on("mouseup.zoom",function(e){l.on("mousemove.zoom mouseup.zoom",null),(0,i.y)(e.view,a.moved),eq(e),a.event(e).end()},!0),u=(0,f.A)(e,o),d=e.clientX,h=e.clientY;(0,i.A)(e.view),eG(e),a.mouse=[u,this.__zoom.invert(u)],L(this),a.start()}}function R(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,o=(0,f.A)(e.changedTouches?e.changedTouches[0]:e,this),i=n.invert(o),l=n.k*(e.shiftKey?.5:2),u=s(k(x(n,l),o,i),a.apply(this,t),p);eq(e),m>0?(0,c.A)(this).transition().duration(m).call(E,u,o,e):(0,c.A)(this).call(b.transform,u,o,e)}}function j(n,...o){if(r.apply(this,arguments)){var i,a,l,s,u=n.touches,c=u.length,d=A(this,o,n.changedTouches.length===c).event(n);for(eG(n),a=0;a<c;++a)l=u[a],s=[s=(0,f.A)(l,this),this.__zoom.invert(s),l.identifier],d.touch0?d.touch1||d.touch0[2]===s[2]||(d.touch1=s,d.taps=0):(d.touch0=s,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=s[0],e=setTimeout(function(){e=null},500)),L(this),d.start())}}function N(e,...t){if(this.__zooming){var n,r,o,i,a=A(this,t).event(e),l=e.changedTouches,u=l.length;for(eq(e),n=0;n<u;++n)r=l[n],o=(0,f.A)(r,this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=o:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=o);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],d=a.touch0[1],h=a.touch1[0],m=a.touch1[1],v=(v=h[0]-c[0])*v+(v=h[1]-c[1])*v,g=(g=m[0]-d[0])*g+(g=m[1]-d[1])*g;r=x(r,Math.sqrt(v/g)),o=[(c[0]+h[0])/2,(c[1]+h[1])/2],i=[(d[0]+m[0])/2,(d[1]+m[1])/2]}else{if(!a.touch0)return;o=a.touch0[0],i=a.touch0[1]}a.zoom("touch",s(k(r,o,i),a.extent,p))}}function T(e,...r){if(this.__zooming){var o,i,a=A(this,r).event(e),l=e.changedTouches,s=l.length;for(eG(e),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<s;++o)i=l[o],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=(0,f.A)(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<w)){var u=(0,c.A)(this).on("dblclick.zoom");u&&u.apply(this,arguments)}}}return b.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",eK),e!==o?E(e,t,n,r):o.interrupt().each(function(){A(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},b.scaleBy=function(e,t,n,r){b.scaleTo(e,function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n},n,r)},b.scaleTo=function(e,t,n,r){b.transform(e,function(){var e=a.apply(this,arguments),r=this.__zoom,o=null==n?_(e):"function"==typeof n?n.apply(this,arguments):n,i=r.invert(o),l="function"==typeof t?t.apply(this,arguments):t;return s(k(x(r,l),o,i),e,p)},n,r)},b.translateBy=function(e,t,n,r){b.transform(e,function(){return s(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),a.apply(this,arguments),p)},null,r)},b.translateTo=function(e,t,n,r,o){b.transform(e,function(){var e=a.apply(this,arguments),o=this.__zoom,i=null==r?_(e):"function"==typeof r?r.apply(this,arguments):r;return s(eB.translate(i[0],i[1]).scale(o.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,p)},r,o)},M.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=(0,c.A)(this.that).datum();g.call(e,this.that,new eV(e,{sourceEvent:this.sourceEvent,target:b,type:e,transform:this.that.__zoom,dispatch:g}),t)}},b.wheelDelta=function(e){return arguments.length?(u="function"==typeof e?e:eF(+e),b):u},b.filter=function(e){return arguments.length?(r="function"==typeof e?e:eF(!!e),b):r},b.touchable=function(e){return arguments.length?(d="function"==typeof e?e:eF(!!e),b):d},b.extent=function(e){return arguments.length?(a="function"==typeof e?e:eF([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),b):a},b.scaleExtent=function(e){return arguments.length?(h[0]=+e[0],h[1]=+e[1],b):[h[0],h[1]]},b.translateExtent=function(e){return arguments.length?(p[0][0]=+e[0][0],p[1][0]=+e[1][0],p[0][1]=+e[0][1],p[1][1]=+e[1][1],b):[[p[0][0],p[0][1]],[p[1][0],p[1][1]]]},b.constrain=function(e){return arguments.length?(s=e,b):s},b.duration=function(e){return arguments.length?(m=+e,b):m},b.interpolate=function(e){return arguments.length?(v=e,b):v},b.on=function(){var e=g.on.apply(g,arguments);return e===g?b:e},b.clickDistance=function(e){return arguments.length?(y=(e*=1)*e,b):Math.sqrt(y)},b.tapDistance=function(e){return arguments.length?(w=+e,b):w},b}eW.prototype},3314:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]])},3454:(e,t,n)=>{"use strict";n.d(t,{o:()=>y});var r=n(2115),o=n(5694);function i(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var a=n(3219),l=n(2903),s=n(4897),u=n(4211);let c=({id:e,x:t,y:n,width:i,height:a,style:l,color:s,strokeColor:u,strokeWidth:c,className:f,borderRadius:d,shapeRendering:h,onClick:p,selected:m})=>{let{background:v,backgroundColor:g}=l||{};return r.createElement("rect",{className:(0,o.A)(["react-flow__minimap-node",{selected:m},f]),x:t,y:n,rx:d,ry:d,width:i,height:a,fill:s||v||g,stroke:u,strokeWidth:c,shapeRendering:h,onClick:p?t=>p(t,e):void 0})};c.displayName="MiniMapNode";var f=(0,r.memo)(c);let d=e=>e.nodeOrigin,h=e=>e.getNodes().filter(e=>!e.hidden&&e.width&&e.height),p=e=>e instanceof Function?e:()=>e;var m=(0,r.memo)(function({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:o=5,nodeStrokeWidth:a=2,nodeComponent:l=f,onClick:s}){let c=(0,u.Pj)(h,i),m=(0,u.Pj)(d),v=p(t),g=p(e),y=p(n),w="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return r.createElement(r.Fragment,null,c.map(e=>{let{x:t,y:n}=(0,u.Cz)(e,m).positionAbsolute;return r.createElement(l,{key:e.id,x:t,y:n,width:e.width,height:e.height,style:e.style,selected:e.selected,className:y(e),color:v(e),borderRadius:o,strokeColor:g(e),strokeWidth:a,shapeRendering:w,onClick:s,id:e.id})}))});let v=e=>{let t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?(0,u.Mi)((0,u.Jo)(t,e.nodeOrigin),n):n,rfId:e.rfId}};function g({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:c="#e2e2e2",nodeClassName:f="",nodeBorderRadius:d=5,nodeStrokeWidth:h=2,nodeComponent:p,maskColor:g="rgb(240, 240, 240, 0.6)",maskStrokeColor:y="none",maskStrokeWidth:w=1,position:b="bottom-right",onClick:x,onNodeClick:k,pannable:_=!1,zoomable:E=!1,ariaLabel:A="React Flow mini map",inversePan:M=!1,zoomStep:S=10,offsetScale:C=5}){let R=(0,u.PI)(),j=(0,r.useRef)(null),{boundingRect:N,viewBB:T,rfId:P}=(0,u.Pj)(v,i),z=e?.width??200,L=e?.height??150,O=Math.max(N.width/z,N.height/L),D=O*z,I=O*L,H=C*O,$=N.x-(D-N.width)/2-H,F=N.y-(I-N.height)/2-H,V=D+2*H,W=I+2*H,B=`react-flow__minimap-desc-${P}`,G=(0,r.useRef)(0);G.current=O,(0,r.useEffect)(()=>{if(j.current){let e=(0,l.A)(j.current),t=(0,a.s_)().on("zoom",_?e=>{let{transform:t,d3Selection:n,d3Zoom:r,translateExtent:o,width:i,height:l}=R.getState();if("mousemove"!==e.sourceEvent.type||!n||!r)return;let s=G.current*Math.max(1,t[2])*(M?-1:1),u={x:t[0]-e.sourceEvent.movementX*s,y:t[1]-e.sourceEvent.movementY*s},c=a.GS.translate(u.x,u.y).scale(t[2]),f=r.constrain()(c,[[0,0],[i,l]],o);r.transform(n,f)}:null).on("zoom.wheel",E?e=>{let{transform:t,d3Selection:n,d3Zoom:r}=R.getState();if("wheel"!==e.sourceEvent.type||!n||!r)return;let o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*S,i=t[2]*Math.pow(2,o);r.scaleTo(n,i)}:null);return e.call(t),()=>{e.on("zoom",null)}}},[_,E,M,S]);let q=x?e=>{let t=(0,s.A)(e);x(e,{x:t[0],y:t[1]})}:void 0,X=k?(e,t)=>{k(e,R.getState().nodeInternals.get(t))}:void 0;return r.createElement(u.Zk,{position:b,style:e,className:(0,o.A)(["react-flow__minimap",t]),"data-testid":"rf__minimap"},r.createElement("svg",{width:z,height:L,viewBox:`${$} ${F} ${V} ${W}`,role:"img","aria-labelledby":B,ref:j,onClick:q},A&&r.createElement("title",{id:B},A),r.createElement(m,{onClick:X,nodeColor:c,nodeStrokeColor:n,nodeBorderRadius:d,nodeClassName:f,nodeStrokeWidth:h,nodeComponent:p}),r.createElement("path",{className:"react-flow__minimap-mask",d:`M${$-H},${F-H}h${V+2*H}v${W+2*H}h${-V-2*H}z
        M${T.x},${T.y}h${T.width}v${T.height}h${-T.width}z`,fill:g,fillRule:"evenodd",stroke:y,strokeWidth:w,pointerEvents:"none"})))}g.displayName="MiniMap";var y=(0,r.memo)(g)},3655:(e,t,n)=>{"use strict";n.d(t,{hO:()=>s,sG:()=>l});var r=n(2115),o=n(7650),i=n(9708),a=n(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3875:(e,t,n)=>{"use strict";function r(e){return function(){return this.matches(e)}}function o(e){return function(t){return t.matches(e)}}n.d(t,{A:()=>r,j:()=>o})},4011:(e,t,n)=>{"use strict";n.d(t,{H4:()=>k,bL:()=>x});var r=n(2115),o=n(6081),i=n(9033),a=n(2712),l=n(3655),s=n(1414);function u(){return()=>{}}var c=n(5155),f="Avatar",[d,h]=(0,o.A)(f),[p,m]=d(f),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,c.jsx)(p,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,c.jsx)(l.sG.span,{...o,ref:t})})});v.displayName=f;var g="AvatarImage";r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:f=()=>{},...d}=e,h=m(g,n),p=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,s.useSyncExternalStore)(u,()=>!0,()=>!1),l=r.useRef(null),c=i?(l.current||(l.current=new window.Image),l.current):null,[f,d]=r.useState(()=>b(c,e));return(0,a.N)(()=>{d(b(c,e))},[c,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),n&&(c.referrerPolicy=n),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,o,n]),f}(o,d),v=(0,i.c)(e=>{f(e),h.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&v(p)},[p,v]),"loaded"===p?(0,c.jsx)(l.sG.img,{...d,ref:t,src:o}):null}).displayName=g;var y="AvatarFallback",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=m(y,n),[s,u]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==a.imageLoadingStatus?(0,c.jsx)(l.sG.span,{...i,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=y;var x=v,k=w},4213:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4229:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4261:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},4315:(e,t,n)=>{"use strict";n.d(t,{jH:()=>i});var r=n(2115);n(5155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},4355:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},4357:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4498:(e,t,n)=>{"use strict";n.d(t,{LN:()=>I,Ay:()=>$,zr:()=>D});var r=n(394),o=n(9293),i=n(3875),a=Array.prototype.find;function l(){return this.firstElementChild}var s=Array.prototype.filter;function u(){return Array.from(this.children)}function c(e){return Array(e.length)}function f(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function d(e,t,n,r,o,i){for(var a,l=0,s=t.length,u=i.length;l<u;++l)(a=t[l])?(a.__data__=i[l],r[l]=a):n[l]=new f(e,i[l]);for(;l<s;++l)(a=t[l])&&(o[l]=a)}function h(e,t,n,r,o,i,a){var l,s,u,c=new Map,d=t.length,h=i.length,p=Array(d);for(l=0;l<d;++l)(s=t[l])&&(p[l]=u=a.call(s,s.__data__,l,t)+"",c.has(u)?o[l]=s:c.set(u,s));for(l=0;l<h;++l)u=a.call(e,i[l],l,i)+"",(s=c.get(u))?(r[l]=s,s.__data__=i[l],c.delete(u)):n[l]=new f(e,i[l]);for(l=0;l<d;++l)(s=t[l])&&c.get(p[l])===s&&(o[l]=s)}function p(e){return e.__data__}function m(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}f.prototype={constructor:f,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var v=n(6102),g=n(1774);function y(e){return e.trim().split(/^|\s+/)}function w(e){return e.classList||new b(e)}function b(e){this._node=e,this._names=y(e.getAttribute("class")||"")}function x(e,t){for(var n=w(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function k(e,t){for(var n=w(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function _(){this.textContent=""}function E(){this.innerHTML=""}function A(){this.nextSibling&&this.parentNode.appendChild(this)}function M(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}b.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var S=n(5131);function C(e){var t=(0,v.A)(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===S.g&&t.documentElement.namespaceURI===S.g?t.createElement(e):t.createElementNS(n,e)}})(t)}function R(){return null}function j(){var e=this.parentNode;e&&e.removeChild(this)}function N(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function T(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function P(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,i=t.length;r<i;++r)(n=t[r],e.type&&n.type!==e.type||n.name!==e.name)?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function z(e,t,n){return function(){var r,o=this.__on,i=function(e){t.call(this,e,this.__data__)};if(o){for(var a=0,l=o.length;a<l;++a)if((r=o[a]).type===e.type&&r.name===e.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),r.value=t;return}}this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},o?o.push(r):this.__on=[r]}}var L=n(7271);function O(e,t,n){var r=(0,L.A)(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}var D=[null];function I(e,t){this._groups=e,this._parents=t}function H(){return new I([[document.documentElement]],D)}I.prototype=H.prototype={constructor:I,select:function(e){"function"!=typeof e&&(e=(0,r.A)(e));for(var t=this._groups,n=t.length,o=Array(n),i=0;i<n;++i)for(var a,l,s=t[i],u=s.length,c=o[i]=Array(u),f=0;f<u;++f)(a=s[f])&&(l=e.call(a,a.__data__,f,s))&&("__data__"in a&&(l.__data__=a.__data__),c[f]=l);return new I(o,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=(0,o.A)(e);for(var n=this._groups,r=n.length,i=[],a=[],l=0;l<r;++l)for(var s,u=n[l],c=u.length,f=0;f<c;++f)(s=u[f])&&(i.push(e.call(s,s.__data__,f,u)),a.push(s));return new I(i,a)},selectChild:function(e){var t;return this.select(null==e?l:(t="function"==typeof e?e:(0,i.j)(e),function(){return a.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?u:(t="function"==typeof e?e:(0,i.j)(e),function(){return s.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=(0,i.A)(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var a,l=t[o],s=l.length,u=r[o]=[],c=0;c<s;++c)(a=l[c])&&e.call(a,a.__data__,c,l)&&u.push(a);return new I(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,p);var n=t?h:d,r=this._parents,o=this._groups;"function"!=typeof e&&(x=e,e=function(){return x});for(var i=o.length,a=Array(i),l=Array(i),s=Array(i),u=0;u<i;++u){var c=r[u],f=o[u],m=f.length,v="object"==typeof(b=e.call(c,c&&c.__data__,u,r))&&"length"in b?b:Array.from(b),g=v.length,y=l[u]=Array(g),w=a[u]=Array(g);n(c,f,y,w,s[u]=Array(m),v,t);for(var b,x,k,_,E=0,A=0;E<g;++E)if(k=y[E]){for(E>=A&&(A=E+1);!(_=w[A])&&++A<g;);k._next=_||null}}return(a=new I(a,r))._enter=l,a._exit=s,a},enter:function(){return new I(this._enter||this._groups.map(c),this._parents)},exit:function(){return new I(this._exit||this._groups.map(c),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?i.remove():n(i),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),l=Array(o),s=0;s<a;++s)for(var u,c=n[s],f=r[s],d=c.length,h=l[s]=Array(d),p=0;p<d;++p)(u=c[p]||f[p])&&(h[p]=u);for(;s<o;++s)l[s]=n[s];return new I(l,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],i=o.length-1,a=o[i];--i>=0;)(r=o[i])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=m);for(var n=this._groups,r=n.length,o=Array(r),i=0;i<r;++i){for(var a,l=n[i],s=l.length,u=o[i]=Array(s),c=0;c<s;++c)(a=l[c])&&(u[c]=a);u.sort(t)}return new I(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,i=t[n],a=0,l=i.length;a<l;++a)(o=i[a])&&e.call(o,o.__data__,a,i);return this},attr:function(e,t){var n=(0,v.A)(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?n.local?function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}:function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}:n.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(n,t))},style:g.A,property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var n=y(e+"");if(arguments.length<2){for(var r=w(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?x:k)(this,e)}}:t?function(e){return function(){x(this,e)}}:function(e){return function(){k(this,e)}})(n,t))},text:function(e){return arguments.length?this.each(null==e?_:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?E:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(A)},lower:function(){return this.each(M)},append:function(e){var t="function"==typeof e?e:C(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n="function"==typeof e?e:C(e),o=null==t?R:"function"==typeof t?t:(0,r.A)(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)})},remove:function(){return this.each(j)},clone:function(e){return this.select(e?T:N)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,i=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}),a=i.length;if(arguments.length<2){var l=this.node().__on;if(l){for(var s,u=0,c=l.length;u<c;++u)for(r=0,s=l[u];r<a;++r)if((o=i[r]).type===s.type&&o.name===s.name)return s.value}return}for(r=0,l=t?z:P;r<a;++r)this.each(l(i[r],t,n));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return O(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return O(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],i=0,a=o.length;i<a;++i)(r=o[i])&&(yield r)}};let $=H},4516:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4897:(e,t,n)=>{"use strict";function r(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}n.d(t,{A:()=>r})},4940:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("pen-line",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},5131:(e,t,n)=>{"use strict";n.d(t,{A:()=>o,g:()=>r});var r="http://www.w3.org/1999/xhtml";let o={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},5185:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},5339:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5453:(e,t,n)=>{"use strict";n.d(t,{v:()=>s});var r=n(2115);let o=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},i=e=>e?o(e):o,a=e=>e,l=e=>{let t=i(e),n=e=>(function(e,t=a){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},s=e=>e?l(e):l},5541:(e,t,n)=>{"use strict";n.d(t,{V:()=>p,_:()=>r});var r,o=n(2115),i=n(5694),a=n(4211);function l(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}function s({color:e,dimensions:t,lineWidth:n}){return o.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function u({color:e,radius:t}){return o.createElement("circle",{cx:t,cy:t,r:t,fill:e})}!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(r||(r={}));let c={[r.Dots]:"#91919a",[r.Lines]:"#eee",[r.Cross]:"#e2e2e2"},f={[r.Dots]:1,[r.Lines]:1,[r.Cross]:6},d=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function h({id:e,variant:t=r.Dots,gap:n=20,size:h,lineWidth:p=1,offset:m=2,color:v,style:g,className:y}){let w=(0,o.useRef)(null),{transform:b,patternId:x}=(0,a.Pj)(d,l),k=v||c[t],_=h||f[t],E=t===r.Dots,A=t===r.Cross,M=Array.isArray(n)?n:[n,n],S=[M[0]*b[2]||1,M[1]*b[2]||1],C=_*b[2],R=A?[C,C]:S,j=E?[C/m,C/m]:[R[0]/m,R[1]/m];return o.createElement("svg",{className:(0,i.A)(["react-flow__background",y]),style:{...g,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:w,"data-testid":"rf__background"},o.createElement("pattern",{id:x+e,x:b[0]%S[0],y:b[1]%S[1],width:S[0],height:S[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${j[0]},-${j[1]})`},E?o.createElement(u,{color:k,radius:C/m}):o.createElement(s,{dimensions:R,color:k,lineWidth:p})),o.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${x+e})`}))}h.displayName="Background";var p=(0,o.memo)(h)},5580:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("thumbs-down",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]])},5623:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5643:(e,t,n)=>{"use strict";e.exports=n(6115)},5690:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5694:(e,t,n)=>{"use strict";n.d(t,{A:()=>function e(t){if("string"==typeof t||"number"==typeof t)return""+t;let n="";if(Array.isArray(t))for(let r=0,o;r<t.length;r++)""!==(o=e(t[r]))&&(n+=(n&&" ")+o);else for(let e in t)t[e]&&(n+=(n&&" ")+e);return n}})},5845:(e,t,n)=>{"use strict";n.d(t,{i:()=>l});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,s]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else l(t)},[u,e,l,s])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(2115),o=n(5155);function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let s=t=>{let{scope:n,children:i,...s}=t,u=n?.[e]?.[l]||a,c=r.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:i})};return s.displayName=t+"Provider",[s,function(n,o){let s=o?.[e]?.[l]||a,u=r.useContext(s);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},6101:(e,t,n)=>{"use strict";n.d(t,{s:()=>a,t:()=>i});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function a(...e){return r.useCallback(i(...e),e)}},6102:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(5131);function o(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),r.A.hasOwnProperty(t)?{space:r.A[t],local:e}:e}},6115:(e,t,n)=>{"use strict";var r=n(2115),o=n(1414),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var h=a(e,(f=u(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&d.hasValue){var t=d.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,o]))[0],f[1]);return s(function(){d.hasValue=!0,d.value=h},[h]),c(h),h}},6606:(e,t,n)=>{"use strict";n.d(t,{UC:()=>ru,q7:()=>rc,ZL:()=>rs,bL:()=>ra,wv:()=>rf,l9:()=>rl});var r,o,i,a=n(2115),l=n(5185),s=n(6101),u=n(6081),c=n(5845),f=n(3655),d=n(7328),h=n(4315),p=n(9033),m=n(5155),v="dismissableLayer.update",g=a.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=a.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:h,onDismiss:y,...x}=e,k=a.useContext(g),[_,E]=a.useState(null),A=null!=(r=null==_?void 0:_.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,M]=a.useState({}),S=(0,s.s)(t,e=>E(e)),C=Array.from(k.layers),[R]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),j=C.indexOf(R),N=_?C.indexOf(_):-1,T=k.layersWithOutsidePointerEventsDisabled.size>0,P=N>=j,z=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,p.c)(e),o=a.useRef(!1),i=a.useRef(()=>{});return a.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...k.branches].some(e=>e.contains(t));P&&!n&&(null==c||c(e),null==h||h(e),e.defaultPrevented||null==y||y())},A),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,p.c)(e),o=a.useRef(!1);return a.useEffect(()=>{let e=e=>{e.target&&!o.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==d||d(e),null==h||h(e),e.defaultPrevented||null==y||y())},A);return!function(e,t=globalThis?.document){let n=(0,p.c)(e);a.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===k.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},A),a.useEffect(()=>{if(_)return i&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(o=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(_)),k.layers.add(_),w(),()=>{i&&1===k.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=o)}},[_,A,i,k]),a.useEffect(()=>()=>{_&&(k.layers.delete(_),k.layersWithOutsidePointerEventsDisabled.delete(_),w())},[_,k]),a.useEffect(()=>{let e=()=>M({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,m.jsx)(f.sG.div,{...x,ref:S,style:{pointerEvents:T?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,z.onPointerDownCapture)})});function w(){let e=new CustomEvent(v);document.dispatchEvent(e)}function b(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,f.hO)(i,a):i.dispatchEvent(a)}y.displayName="DismissableLayer",a.forwardRef((e,t)=>{let n=a.useContext(g),r=a.useRef(null),o=(0,s.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,m.jsx)(f.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var x=0;function k(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var _="focusScope.autoFocusOnMount",E="focusScope.autoFocusOnUnmount",A={bubbles:!1,cancelable:!0},M=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[u,c]=a.useState(null),d=(0,p.c)(o),h=(0,p.c)(i),v=a.useRef(null),g=(0,s.s)(t,e=>c(e)),y=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(r){let e=function(e){if(y.paused||!u)return;let t=e.target;u.contains(t)?v.current=t:R(v.current,{select:!0})},t=function(e){if(y.paused||!u)return;let t=e.relatedTarget;null!==t&&(u.contains(t)||R(v.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&R(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,y.paused]),a.useEffect(()=>{if(u){j.add(y);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(_,A);u.addEventListener(_,d),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(R(r,{select:t}),document.activeElement!==n)return}(S(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&R(u))}return()=>{u.removeEventListener(_,d),setTimeout(()=>{let t=new CustomEvent(E,A);u.addEventListener(E,h),u.dispatchEvent(t),t.defaultPrevented||R(null!=e?e:document.body,{select:!0}),u.removeEventListener(E,h),j.remove(y)},0)}}},[u,d,h,y]);let w=a.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=S(e);return[C(t,e),C(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&R(i,{select:!0})):(e.preventDefault(),n&&R(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,m.jsx)(f.sG.div,{tabIndex:-1,...l,ref:g,onKeyDown:w})});function S(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function C(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function R(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}M.displayName="FocusScope";var j=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=N(e,t)).unshift(t)},remove(t){var n;null==(n=(e=N(e,t))[0])||n.resume()}}}();function N(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var T=n(1285);let P=["top","right","bottom","left"],z=Math.min,L=Math.max,O=Math.round,D=Math.floor,I=e=>({x:e,y:e}),H={left:"right",right:"left",bottom:"top",top:"bottom"},$={start:"end",end:"start"};function F(e,t){return"function"==typeof e?e(t):e}function V(e){return e.split("-")[0]}function W(e){return e.split("-")[1]}function B(e){return"x"===e?"y":"x"}function G(e){return"y"===e?"height":"width"}function q(e){return["top","bottom"].includes(V(e))?"y":"x"}function X(e){return e.replace(/start|end/g,e=>$[e])}function Y(e){return e.replace(/left|right|bottom|top/g,e=>H[e])}function K(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function U(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Z(e,t,n){let r,{reference:o,floating:i}=e,a=q(t),l=B(q(t)),s=G(l),u=V(t),c="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,h=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(W(t)){case"start":r[l]-=h*(n&&c?-1:1);break;case"end":r[l]+=h*(n&&c?-1:1)}return r}let Q=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=Z(u,r,s),d=r,h={},p=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=g?g:f,h={...h,[i]:{...h[i],...y}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=Z(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:h}};async function J(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:h=0}=F(t,e),p=K(h),m=l[d?"floating"===f?"reference":"floating":f],v=U(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=U(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-b.top+p.top)/w.y,bottom:(b.bottom-v.bottom+p.bottom)/w.y,left:(v.left-b.left+p.left)/w.x,right:(b.right-v.right+p.right)/w.x}}function ee(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function et(e){return P.some(t=>e[t]>=0)}async function en(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=V(n),l=W(n),s="y"===q(n),u=["left","top"].includes(a)?-1:1,c=i&&s?-1:1,f=F(t,e),{mainAxis:d,crossAxis:h,alignmentAxis:p}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof p&&(h="end"===l?-1*p:p),s?{x:h*c,y:d*u}:{x:d*u,y:h*c}}function er(){return"undefined"!=typeof window}function eo(e){return el(e)?(e.nodeName||"").toLowerCase():"#document"}function ei(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ea(e){var t;return null==(t=(el(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function el(e){return!!er()&&(e instanceof Node||e instanceof ei(e).Node)}function es(e){return!!er()&&(e instanceof Element||e instanceof ei(e).Element)}function eu(e){return!!er()&&(e instanceof HTMLElement||e instanceof ei(e).HTMLElement)}function ec(e){return!!er()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ei(e).ShadowRoot)}function ef(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ev(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ed(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eh(e){let t=ep(),n=es(e)?ev(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ep(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function em(e){return["html","body","#document"].includes(eo(e))}function ev(e){return ei(e).getComputedStyle(e)}function eg(e){return es(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ey(e){if("html"===eo(e))return e;let t=e.assignedSlot||e.parentNode||ec(e)&&e.host||ea(e);return ec(t)?t.host:t}function ew(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ey(t);return em(n)?t.ownerDocument?t.ownerDocument.body:t.body:eu(n)&&ef(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=ei(o);if(i){let e=eb(a);return t.concat(a,a.visualViewport||[],ef(o)?o:[],e&&n?ew(e):[])}return t.concat(o,ew(o,[],n))}function eb(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ex(e){let t=ev(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=eu(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=O(n)!==i||O(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function ek(e){return es(e)?e:e.contextElement}function e_(e){let t=ek(e);if(!eu(t))return I(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ex(t),a=(i?O(n.width):n.width)/r,l=(i?O(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eE=I(0);function eA(e){let t=ei(e);return ep()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eE}function eM(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ek(e),l=I(1);t&&(r?es(r)&&(l=e_(r)):l=e_(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===ei(a))&&o)?eA(a):I(0),u=(i.left+s.x)/l.x,c=(i.top+s.y)/l.y,f=i.width/l.x,d=i.height/l.y;if(a){let e=ei(a),t=r&&es(r)?ei(r):r,n=e,o=eb(n);for(;o&&r&&t!==n;){let e=e_(o),t=o.getBoundingClientRect(),r=ev(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,f*=e.x,d*=e.y,u+=i,c+=a,o=eb(n=ei(o))}}return U({width:f,height:d,x:u,y:c})}function eS(e,t){let n=eg(e).scrollLeft;return t?t.left+n:eM(ea(e)).left+n}function eC(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eS(e,r)),y:r.top+t.scrollTop}}function eR(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ei(e),r=ea(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=ep();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=ea(e),n=eg(e),r=e.ownerDocument.body,o=L(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=L(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eS(e),l=-n.scrollTop;return"rtl"===ev(r).direction&&(a+=L(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(ea(e));else if(es(t))r=function(e,t){let n=eM(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=eu(e)?e_(e):I(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eA(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return U(r)}function ej(e){return"static"===ev(e).position}function eN(e,t){if(!eu(e)||"fixed"===ev(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ea(e)===n&&(n=n.ownerDocument.body),n}function eT(e,t){let n=ei(e);if(ed(e))return n;if(!eu(e)){let t=ey(e);for(;t&&!em(t);){if(es(t)&&!ej(t))return t;t=ey(t)}return n}let r=eN(e,t);for(;r&&["table","td","th"].includes(eo(r))&&ej(r);)r=eN(r,t);return r&&em(r)&&ej(r)&&!eh(r)?n:r||function(e){let t=ey(e);for(;eu(t)&&!em(t);){if(eh(t))return t;if(ed(t))break;t=ey(t)}return null}(e)||n}let eP=async function(e){let t=this.getOffsetParent||eT,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=eu(t),o=ea(t),i="fixed"===n,a=eM(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=I(0);if(r||!r&&!i)if(("body"!==eo(t)||ef(o))&&(l=eg(t)),r){let e=eM(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eS(o));i&&!r&&o&&(s.x=eS(o));let u=!o||r||i?I(0):eC(o,l);return{x:a.left+l.scrollLeft-s.x-u.x,y:a.top+l.scrollTop-s.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ez={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=ea(r),l=!!t&&ed(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},u=I(1),c=I(0),f=eu(r);if((f||!f&&!i)&&(("body"!==eo(r)||ef(a))&&(s=eg(r)),eu(r))){let e=eM(r);u=e_(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!a||f||i?I(0):eC(a,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+d.x,y:n.y*u.y-s.scrollTop*u.y+c.y+d.y}},getDocumentElement:ea,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ed(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ew(e,[],!1).filter(e=>es(e)&&"body"!==eo(e)),o=null,i="fixed"===ev(e).position,a=i?ey(e):e;for(;es(a)&&!em(a);){let t=ev(a),n=eh(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ef(a)&&!n&&function e(t,n){let r=ey(t);return!(r===n||!es(r)||em(r))&&("fixed"===ev(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=ey(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eR(t,n,o);return e.top=L(r.top,e.top),e.right=z(r.right,e.right),e.bottom=z(r.bottom,e.bottom),e.left=L(r.left,e.left),e},eR(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eT,getElementRects:eP,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ex(e);return{width:t,height:n}},getScale:e_,isElement:es,isRTL:function(e){return"rtl"===ev(e).direction}};function eL(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eO=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:u,padding:c=0}=F(e,t)||{};if(null==u)return{};let f=K(c),d={x:n,y:r},h=B(q(o)),p=G(h),m=await a.getDimensions(u),v="y"===h,g=v?"clientHeight":"clientWidth",y=i.reference[p]+i.reference[h]-d[h]-i.floating[p],w=d[h]-i.reference[h],b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),x=b?b[g]:0;x&&await (null==a.isElement?void 0:a.isElement(b))||(x=l.floating[g]||i.floating[p]);let k=x/2-m[p]/2-1,_=z(f[v?"top":"left"],k),E=z(f[v?"bottom":"right"],k),A=x-m[p]-E,M=x/2-m[p]/2+(y/2-w/2),S=L(_,z(M,A)),C=!s.arrow&&null!=W(o)&&M!==S&&i.reference[p]/2-(M<_?_:E)-m[p]/2<0,R=C?M<_?M-_:M-A:0;return{[h]:d[h]+R,data:{[h]:S,centerOffset:M-S-R,...C&&{alignmentOffset:R}},reset:C}}}),eD=(e,t,n)=>{let r=new Map,o={platform:ez,...n},i={...o.platform,_c:r};return Q(e,t,{...o,platform:i})};var eI=n(7650),eH="undefined"!=typeof document?a.useLayoutEffect:function(){};function e$(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!e$(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!e$(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eF(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eV(e,t){let n=eF(e);return Math.round(t*n)/n}function eW(e){let t=a.useRef(e);return eH(()=>{t.current=e}),t}let eB=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eO({element:n.current,padding:r}).fn(t):{}:n?eO({element:n,padding:r}).fn(t):{}}}),eG=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await en(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),eq=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=F(e,t),u={x:n,y:r},c=await J(t,s),f=q(V(o)),d=B(f),h=u[d],p=u[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=L(n,z(h,r))}if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=L(n,z(p,r))}let m=l.fn({...t,[d]:h,[f]:p});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:a}}}}}}(e),options:[e,t]}),eX=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:u=!0}=F(e,t),c={x:n,y:r},f=q(o),d=B(f),h=c[d],p=c[f],m=F(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+v.mainAxis,n=i.reference[d]+i.reference[e]-v.mainAxis;h<t?h=t:h>n&&(h=n)}if(u){var g,y;let e="y"===d?"width":"height",t=["top","left"].includes(V(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[f])||0)+(t?0:v.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[f])||0)-(t?v.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[d]:h,[f]:p}}}}(e),options:[e,t]}),eY=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:h=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=F(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let b=V(l),x=q(c),k=V(c)===c,_=await (null==f.isRTL?void 0:f.isRTL(d.floating)),E=m||(k||!y?[Y(c)]:function(e){let t=Y(e);return[X(e),t,X(t)]}(c)),A="none"!==g;!m&&A&&E.push(...function(e,t,n,r){let o=W(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(V(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(X)))),i}(c,y,g,_));let M=[c,...E],S=await J(t,w),C=[],R=(null==(r=s.flip)?void 0:r.overflows)||[];if(h&&C.push(S[b]),p){let e=function(e,t,n){void 0===n&&(n=!1);let r=W(e),o=B(q(e)),i=G(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Y(a)),[a,Y(a)]}(l,u,_);C.push(S[e[0]],S[e[1]])}if(R=[...R,{placement:l,overflows:C}],!C.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=M[e];if(t&&("alignment"!==p||x===q(t)||R.every(e=>e.overflows[0]>0&&q(e.placement)===x)))return{data:{index:e,overflows:R},reset:{placement:t}};let n=null==(i=R.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=R.filter(e=>{if(A){let t=q(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eK=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:s,elements:u}=t,{apply:c=()=>{},...f}=F(e,t),d=await J(t,f),h=V(a),p=W(a),m="y"===q(a),{width:v,height:g}=l.floating;"top"===h||"bottom"===h?(o=h,i=p===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=h,o="end"===p?"top":"bottom");let y=g-d.top-d.bottom,w=v-d.left-d.right,b=z(g-d[o],y),x=z(v-d[i],w),k=!t.middlewareData.shift,_=b,E=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(E=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(_=y),k&&!p){let e=L(d.left,0),t=L(d.right,0),n=L(d.top,0),r=L(d.bottom,0);m?E=v-2*(0!==e||0!==t?e+t:L(d.left,d.right)):_=g-2*(0!==n||0!==r?n+r:L(d.top,d.bottom))}await c({...t,availableWidth:E,availableHeight:_});let A=await s.getDimensions(u.floating);return v!==A.width||g!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eU=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=F(e,t);switch(r){case"referenceHidden":{let e=ee(await J(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:et(e)}}}case"escaped":{let e=ee(await J(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:et(e)}}}default:return{}}}}}(e),options:[e,t]}),eZ=(e,t)=>({...eB(e),options:[e,t]});var eQ=a.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,m.jsx)(f.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,m.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eQ.displayName="Arrow";var eJ=n(2712),e0="Popper",[e1,e2]=(0,u.A)(e0),[e5,e3]=e1(e0),e4=e=>{let{__scopePopper:t,children:n}=e,[r,o]=a.useState(null);return(0,m.jsx)(e5,{scope:t,anchor:r,onAnchorChange:o,children:n})};e4.displayName=e0;var e9="PopperAnchor",e6=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,i=e3(e9,n),l=a.useRef(null),u=(0,s.s)(t,l);return a.useEffect(()=>{i.onAnchorChange((null==r?void 0:r.current)||l.current)}),r?null:(0,m.jsx)(f.sG.div,{...o,ref:u})});e6.displayName=e9;var e8="PopperContent",[e7,te]=e1(e8),tt=a.forwardRef((e,t)=>{var n,r,o,i,l,u,c,d;let{__scopePopper:h,side:v="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:b=0,avoidCollisions:x=!0,collisionBoundary:k=[],collisionPadding:_=0,sticky:E="partial",hideWhenDetached:A=!1,updatePositionStrategy:M="optimized",onPlaced:S,...C}=e,R=e3(e8,h),[j,N]=a.useState(null),T=(0,s.s)(t,e=>N(e)),[P,O]=a.useState(null),I=function(e){let[t,n]=a.useState(void 0);return(0,eJ.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(P),H=null!=(c=null==I?void 0:I.width)?c:0,$=null!=(d=null==I?void 0:I.height)?d:0,F="number"==typeof _?_:{top:0,right:0,bottom:0,left:0,..._},V=Array.isArray(k)?k:[k],W=V.length>0,B={padding:F,boundary:V.filter(ti),altBoundary:W},{refs:G,floatingStyles:q,placement:X,isPositioned:Y,middlewareData:K}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[f,d]=a.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=a.useState(r);e$(h,r)||p(r);let[m,v]=a.useState(null),[g,y]=a.useState(null),w=a.useCallback(e=>{e!==_.current&&(_.current=e,v(e))},[]),b=a.useCallback(e=>{e!==E.current&&(E.current=e,y(e))},[]),x=i||m,k=l||g,_=a.useRef(null),E=a.useRef(null),A=a.useRef(f),M=null!=u,S=eW(u),C=eW(o),R=eW(c),j=a.useCallback(()=>{if(!_.current||!E.current)return;let e={placement:t,strategy:n,middleware:h};C.current&&(e.platform=C.current),eD(_.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};N.current&&!e$(A.current,t)&&(A.current=t,eI.flushSync(()=>{d(t)}))})},[h,t,n,C,R]);eH(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let N=a.useRef(!1);eH(()=>(N.current=!0,()=>{N.current=!1}),[]),eH(()=>{if(x&&(_.current=x),k&&(E.current=k),x&&k){if(S.current)return S.current(x,k,j);j()}},[x,k,j,S,M]);let T=a.useMemo(()=>({reference:_,floating:E,setReference:w,setFloating:b}),[w,b]),P=a.useMemo(()=>({reference:x,floating:k}),[x,k]),z=a.useMemo(()=>{let e={position:n,left:0,top:0};if(!P.floating)return e;let t=eV(P.floating,f.x),r=eV(P.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eF(P.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,P.floating,f.x,f.y]);return a.useMemo(()=>({...f,update:j,refs:T,elements:P,floatingStyles:z}),[f,j,T,P,z])}({strategy:"fixed",placement:v+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=ek(e),f=i||a?[...c?ew(c):[],...ew(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let d=c&&s?function(e,t){let n,r=null,o=ea(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:f,width:d,height:h}=u;if(l||t(),!d||!h)return;let p=D(f),m=D(o.clientWidth-(c+d)),v={rootMargin:-p+"px "+-m+"px "+-D(o.clientHeight-(f+h))+"px "+-D(c)+"px",threshold:L(0,z(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eL(u,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(c,n):null,h=-1,p=null;l&&(p=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=p)||e.observe(t)})),n()}),c&&!u&&p.observe(c),p.observe(t));let m=u?eM(e):null;return u&&function t(){let r=eM(e);m&&!eL(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=p)||e.disconnect(),p=null,u&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===M})},elements:{reference:R.anchor},middleware:[eG({mainAxis:g+$,alignmentAxis:w}),x&&eq({mainAxis:!0,crossAxis:!1,limiter:"partial"===E?eX():void 0,...B}),x&&eY({...B}),eK({...B,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),P&&eZ({element:P,padding:b}),ta({arrowWidth:H,arrowHeight:$}),A&&eU({strategy:"referenceHidden",...B})]}),[U,Z]=tl(X),Q=(0,p.c)(S);(0,eJ.N)(()=>{Y&&(null==Q||Q())},[Y,Q]);let J=null==(n=K.arrow)?void 0:n.x,ee=null==(r=K.arrow)?void 0:r.y,et=(null==(o=K.arrow)?void 0:o.centerOffset)!==0,[en,er]=a.useState();return(0,eJ.N)(()=>{j&&er(window.getComputedStyle(j).zIndex)},[j]),(0,m.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:Y?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(i=K.transformOrigin)?void 0:i.x,null==(l=K.transformOrigin)?void 0:l.y].join(" "),...(null==(u=K.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,m.jsx)(e7,{scope:h,placedSide:U,onArrowChange:O,arrowX:J,arrowY:ee,shouldHideArrow:et,children:(0,m.jsx)(f.sG.div,{"data-side":U,"data-align":Z,...C,ref:T,style:{...C.style,animation:Y?void 0:"none"}})})})});tt.displayName=e8;var tn="PopperArrow",tr={top:"bottom",right:"left",bottom:"top",left:"right"},to=a.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=te(tn,n),i=tr[o.placedSide];return(0,m.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,m.jsx)(eQ,{...r,ref:t,style:{...r.style,display:"block"}})})});function ti(e){return null!==e}to.displayName=tn;var ta=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[h,p]=tl(l),m={start:"0%",center:"50%",end:"100%"}[p],v=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+f/2,g=(null!=(a=null==(o=u.arrow)?void 0:o.y)?a:0)+d/2,y="",w="";return"bottom"===h?(y=c?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===h?(y=c?m:"".concat(v,"px"),w="".concat(s.floating.height+d,"px")):"right"===h?(y="".concat(-d,"px"),w=c?m:"".concat(g,"px")):"left"===h&&(y="".concat(s.floating.width+d,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function tl(e){let[t,n="center"]=e.split("-");return[t,n]}var ts=a.forwardRef((e,t)=>{var n,r;let{container:o,...i}=e,[l,s]=a.useState(!1);(0,eJ.N)(()=>s(!0),[]);let u=o||l&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return u?eI.createPortal((0,m.jsx)(f.sG.div,{...i,ref:t}),u):null});ts.displayName="Portal";var tu=n(8905),tc=n(9196),tf=n(9708),td=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},th=new WeakMap,tp=new WeakMap,tm={},tv=0,tg=function(e){return e&&(e.host||tg(e.parentNode))},ty=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=tg(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tm[n]||(tm[n]=new WeakMap);var i=tm[n],a=[],l=new Set,s=new Set(o),u=function(e){!e||l.has(e)||(l.add(e),u(e.parentNode))};o.forEach(u);var c=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(th.get(e)||0)+1,u=(i.get(e)||0)+1;th.set(e,s),i.set(e,u),a.push(e),1===s&&o&&tp.set(e,!0),1===u&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),l.clear(),tv++,function(){a.forEach(function(e){var t=th.get(e)-1,o=i.get(e)-1;th.set(e,t),i.set(e,o),t||(tp.has(e)||e.removeAttribute(r),tp.delete(e)),o||e.removeAttribute(n)}),--tv||(th=new WeakMap,th=new WeakMap,tp=new WeakMap,tm={})}},tw=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||td(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),ty(r,o,n,"aria-hidden")):function(){return null}},tb=function(){return(tb=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function tx(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var tk=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),t_="width-before-scroll-bar";function tE(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var tA="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,tM=new WeakMap;function tS(e){return e}var tC=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=tS),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return i.options=tb({async:!0,ssr:!1},e),i}(),tR=function(){},tj=a.forwardRef(function(e,t){var n,r,o,i,l=a.useRef(null),s=a.useState({onScrollCapture:tR,onWheelCapture:tR,onTouchMoveCapture:tR}),u=s[0],c=s[1],f=e.forwardProps,d=e.children,h=e.className,p=e.removeScrollBar,m=e.enabled,v=e.shards,g=e.sideCar,y=e.noRelative,w=e.noIsolation,b=e.inert,x=e.allowPinchZoom,k=e.as,_=e.gapMode,E=tx(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),A=(n=[l,t],r=function(e){return n.forEach(function(t){return tE(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,tA(function(){var e=tM.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||tE(e,null)}),r.forEach(function(e){t.has(e)||tE(e,o)})}tM.set(i,n)},[n]),i),M=tb(tb({},E),u);return a.createElement(a.Fragment,null,m&&a.createElement(g,{sideCar:tC,removeScrollBar:p,shards:v,noRelative:y,noIsolation:w,inert:b,setCallbacks:c,allowPinchZoom:!!x,lockRef:l,gapMode:_}),f?a.cloneElement(a.Children.only(d),tb(tb({},M),{ref:A})):a.createElement(void 0===k?"div":k,tb({},M,{className:h,ref:A}),d))});tj.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},tj.classNames={fullWidth:t_,zeroRight:tk};var tN=function(e){var t=e.sideCar,n=tx(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,tb({},n))};tN.isSideCarExport=!0;var tT=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tP=function(){var e=tT();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},tz=function(){var e=tP();return function(t){return e(t.styles,t.dynamic),null}},tL={left:0,top:0,right:0,gap:0},tO=function(e){return parseInt(e||"",10)||0},tD=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[tO(n),tO(r),tO(o)]},tI=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tL;var t=tD(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},tH=tz(),t$="data-scroll-locked",tF=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(t$,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(tk," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(t_," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(tk," .").concat(tk," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(t_," .").concat(t_," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(t$,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},tV=function(){var e=parseInt(document.body.getAttribute(t$)||"0",10);return isFinite(e)?e:0},tW=function(){a.useEffect(function(){return document.body.setAttribute(t$,(tV()+1).toString()),function(){var e=tV()-1;e<=0?document.body.removeAttribute(t$):document.body.setAttribute(t$,e.toString())}},[])},tB=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;tW();var i=a.useMemo(function(){return tI(o)},[o]);return a.createElement(tH,{styles:tF(i,!t,o,n?"":"!important")})},tG=!1;if("undefined"!=typeof window)try{var tq=Object.defineProperty({},"passive",{get:function(){return tG=!0,!0}});window.addEventListener("test",tq,tq),window.removeEventListener("test",tq,tq)}catch(e){tG=!1}var tX=!!tG&&{passive:!1},tY=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},tK=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),tU(e,r)){var o=tZ(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},tU=function(e,t){return"v"===e?tY(t,"overflowY"):tY(t,"overflowX")},tZ=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},tQ=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,u=t.contains(s),c=!1,f=l>0,d=0,h=0;do{if(!s)break;var p=tZ(e,s),m=p[0],v=p[1]-p[2]-a*m;(m||v)&&tU(e,s)&&(d+=v,h+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return f&&(o&&1>Math.abs(d)||!o&&l>d)?c=!0:!f&&(o&&1>Math.abs(h)||!o&&-l>h)&&(c=!0),c},tJ=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},t0=function(e){return[e.deltaX,e.deltaY]},t1=function(e){return e&&"current"in e?e.current:e},t2=0,t5=[];let t3=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(t2++)[0],i=a.useState(tz)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(t1),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=tJ(e),a=n.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,f=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===f&&"range"===c.type)return!1;var d=tK(f,c);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=tK(f,c)),!d)return!1;if(!r.current&&"changedTouches"in e&&(s||u)&&(r.current=o),!o)return!0;var h=r.current||o;return tQ(h,t,e,"h"===h?s:u,!0)},[]),u=a.useCallback(function(e){if(t5.length&&t5[t5.length-1]===i){var n="deltaY"in e?t0(e):tJ(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(t1).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=a.useCallback(function(e){n.current=tJ(e),r.current=void 0},[]),d=a.useCallback(function(t){c(t.type,t0(t),t.target,s(t,e.lockRef.current))},[]),h=a.useCallback(function(t){c(t.type,tJ(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return t5.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:h}),document.addEventListener("wheel",u,tX),document.addEventListener("touchmove",u,tX),document.addEventListener("touchstart",f,tX),function(){t5=t5.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,tX),document.removeEventListener("touchmove",u,tX),document.removeEventListener("touchstart",f,tX)}},[]);var p=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?a.createElement(tB,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},tC.useMedium(r),tN);var t4=a.forwardRef(function(e,t){return a.createElement(tj,tb({},e,{ref:t,sideCar:t3}))});t4.classNames=tj.classNames;var t9=["Enter"," "],t6=["ArrowUp","PageDown","End"],t8=["ArrowDown","PageUp","Home",...t6],t7={ltr:[...t9,"ArrowRight"],rtl:[...t9,"ArrowLeft"]},ne={ltr:["ArrowLeft"],rtl:["ArrowRight"]},nt="Menu",[nn,nr,no]=(0,d.N)(nt),[ni,na]=(0,u.A)(nt,[no,e2,tc.RG]),nl=e2(),ns=(0,tc.RG)(),[nu,nc]=ni(nt),[nf,nd]=ni(nt),nh=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:i,modal:l=!0}=e,s=nl(t),[u,c]=a.useState(null),f=a.useRef(!1),d=(0,p.c)(i),v=(0,h.jH)(o);return a.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,m.jsx)(e4,{...s,children:(0,m.jsx)(nu,{scope:t,open:n,onOpenChange:d,content:u,onContentChange:c,children:(0,m.jsx)(nf,{scope:t,onClose:a.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:v,modal:l,children:r})})})};nh.displayName=nt;var np=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nl(n);return(0,m.jsx)(e6,{...o,...r,ref:t})});np.displayName="MenuAnchor";var nm="MenuPortal",[nv,ng]=ni(nm,{forceMount:void 0}),ny=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=nc(nm,t);return(0,m.jsx)(nv,{scope:t,forceMount:n,children:(0,m.jsx)(tu.C,{present:n||i.open,children:(0,m.jsx)(ts,{asChild:!0,container:o,children:r})})})};ny.displayName=nm;var nw="MenuContent",[nb,nx]=ni(nw),nk=a.forwardRef((e,t)=>{let n=ng(nw,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nc(nw,e.__scopeMenu),a=nd(nw,e.__scopeMenu);return(0,m.jsx)(nn.Provider,{scope:e.__scopeMenu,children:(0,m.jsx)(tu.C,{present:r||i.open,children:(0,m.jsx)(nn.Slot,{scope:e.__scopeMenu,children:a.modal?(0,m.jsx)(n_,{...o,ref:t}):(0,m.jsx)(nE,{...o,ref:t})})})})}),n_=a.forwardRef((e,t)=>{let n=nc(nw,e.__scopeMenu),r=a.useRef(null),o=(0,s.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return tw(e)},[]),(0,m.jsx)(nM,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),nE=a.forwardRef((e,t)=>{let n=nc(nw,e.__scopeMenu);return(0,m.jsx)(nM,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),nA=(0,tf.TL)("MenuContent.ScrollLock"),nM=a.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:f,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:p,onInteractOutside:v,onDismiss:g,disableOutsideScroll:w,...b}=e,_=nc(nw,n),E=nd(nw,n),A=nl(n),S=ns(n),C=nr(n),[R,j]=a.useState(null),N=a.useRef(null),T=(0,s.s)(t,N,_.onContentChange),P=a.useRef(0),z=a.useRef(""),L=a.useRef(0),O=a.useRef(null),D=a.useRef("right"),I=a.useRef(0),H=w?t4:a.Fragment,$=e=>{var t,n;let r=z.current+e,o=C().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),s=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){z.current=t,window.clearTimeout(P.current),""!==t&&(P.current=window.setTimeout(()=>e(""),1e3))}(r),s&&setTimeout(()=>s.focus())};a.useEffect(()=>()=>window.clearTimeout(P.current),[]),a.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:k()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:k()),x++,()=>{1===x&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),x--}},[]);let F=a.useCallback(e=>{var t,n;return D.current===(null==(t=O.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],s=a.x,u=a.y,c=l.x,f=l.y;u>r!=f>r&&n<(c-s)*(r-u)/(f-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=O.current)?void 0:n.area)},[]);return(0,m.jsx)(nb,{scope:n,searchRef:z,onItemEnter:a.useCallback(e=>{F(e)&&e.preventDefault()},[F]),onItemLeave:a.useCallback(e=>{var t;F(e)||(null==(t=N.current)||t.focus(),j(null))},[F]),onTriggerLeave:a.useCallback(e=>{F(e)&&e.preventDefault()},[F]),pointerGraceTimerRef:L,onPointerGraceIntentChange:a.useCallback(e=>{O.current=e},[]),children:(0,m.jsx)(H,{...w?{as:nA,allowPinchZoom:!0}:void 0,children:(0,m.jsx)(M,{asChild:!0,trapped:o,onMountAutoFocus:(0,l.m)(i,e=>{var t;e.preventDefault(),null==(t=N.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,m.jsx)(y,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:p,onInteractOutside:v,onDismiss:g,children:(0,m.jsx)(tc.bL,{asChild:!0,...S,dir:E.dir,orientation:"vertical",loop:r,currentTabStopId:R,onCurrentTabStopIdChange:j,onEntryFocus:(0,l.m)(f,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,m.jsx)(tt,{role:"menu","aria-orientation":"vertical","data-state":nQ(_.open),"data-radix-menu-content":"",dir:E.dir,...A,...b,ref:T,style:{outline:"none",...b.style},onKeyDown:(0,l.m)(b.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&$(e.key));let o=N.current;if(e.target!==o||!t8.includes(e.key))return;e.preventDefault();let i=C().filter(e=>!e.disabled).map(e=>e.ref.current);t6.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,l.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(P.current),z.current="")}),onPointerMove:(0,l.m)(e.onPointerMove,n1(e=>{let t=e.target,n=I.current!==e.clientX;e.currentTarget.contains(t)&&n&&(D.current=e.clientX>I.current?"right":"left",I.current=e.clientX)}))})})})})})})});nk.displayName=nw;var nS=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,m.jsx)(f.sG.div,{role:"group",...r,ref:t})});nS.displayName="MenuGroup";var nC=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,m.jsx)(f.sG.div,{...r,ref:t})});nC.displayName="MenuLabel";var nR="MenuItem",nj="menu.itemSelect",nN=a.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,i=a.useRef(null),u=nd(nR,e.__scopeMenu),c=nx(nR,e.__scopeMenu),d=(0,s.s)(t,i),h=a.useRef(!1);return(0,m.jsx)(nT,{...o,ref:d,disabled:n,onClick:(0,l.m)(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(nj,{bubbles:!0,cancelable:!0});e.addEventListener(nj,e=>null==r?void 0:r(e),{once:!0}),(0,f.hO)(e,t),t.defaultPrevented?h.current=!1:u.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),h.current=!0},onPointerUp:(0,l.m)(e.onPointerUp,e=>{var t;h.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;n||t&&" "===e.key||t9.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});nN.displayName=nR;var nT=a.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...i}=e,u=nx(nR,n),c=ns(n),d=a.useRef(null),h=(0,s.s)(t,d),[p,v]=a.useState(!1),[g,y]=a.useState("");return a.useEffect(()=>{let e=d.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,m.jsx)(nn.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:g,children:(0,m.jsx)(tc.q7,{asChild:!0,...c,focusable:!r,children:(0,m.jsx)(f.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:h,onPointerMove:(0,l.m)(e.onPointerMove,n1(e=>{r?u.onItemLeave(e):(u.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,l.m)(e.onPointerLeave,n1(e=>u.onItemLeave(e))),onFocus:(0,l.m)(e.onFocus,()=>v(!0)),onBlur:(0,l.m)(e.onBlur,()=>v(!1))})})})}),nP=a.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,m.jsx)(nF,{scope:e.__scopeMenu,checked:n,children:(0,m.jsx)(nN,{role:"menuitemcheckbox","aria-checked":nJ(n)?"mixed":n,...o,ref:t,"data-state":n0(n),onSelect:(0,l.m)(o.onSelect,()=>null==r?void 0:r(!!nJ(n)||!n),{checkForDefaultPrevented:!1})})})});nP.displayName="MenuCheckboxItem";var nz="MenuRadioGroup",[nL,nO]=ni(nz,{value:void 0,onValueChange:()=>{}}),nD=a.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,p.c)(r);return(0,m.jsx)(nL,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,m.jsx)(nS,{...o,ref:t})})});nD.displayName=nz;var nI="MenuRadioItem",nH=a.forwardRef((e,t)=>{let{value:n,...r}=e,o=nO(nI,e.__scopeMenu),i=n===o.value;return(0,m.jsx)(nF,{scope:e.__scopeMenu,checked:i,children:(0,m.jsx)(nN,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":n0(i),onSelect:(0,l.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});nH.displayName=nI;var n$="MenuItemIndicator",[nF,nV]=ni(n$,{checked:!1}),nW=a.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=nV(n$,n);return(0,m.jsx)(tu.C,{present:r||nJ(i.checked)||!0===i.checked,children:(0,m.jsx)(f.sG.span,{...o,ref:t,"data-state":n0(i.checked)})})});nW.displayName=n$;var nB=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,m.jsx)(f.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});nB.displayName="MenuSeparator";var nG=a.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=nl(n);return(0,m.jsx)(to,{...o,...r,ref:t})});nG.displayName="MenuArrow";var[nq,nX]=ni("MenuSub"),nY="MenuSubTrigger",nK=a.forwardRef((e,t)=>{let n=nc(nY,e.__scopeMenu),r=nd(nY,e.__scopeMenu),o=nX(nY,e.__scopeMenu),i=nx(nY,e.__scopeMenu),u=a.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:f}=i,d={__scopeMenu:e.__scopeMenu},h=a.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return a.useEffect(()=>h,[h]),a.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),f(null)}},[c,f]),(0,m.jsx)(np,{asChild:!0,...d,children:(0,m.jsx)(nT,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":nQ(n.open),...e,ref:(0,s.t)(t,o.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,l.m)(e.onPointerMove,n1(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||u.current||(i.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,l.m)(e.onPointerLeave,n1(e=>{var t,r;h();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,a="right"===t,l=o[a?"left":"right"],s=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:s,y:o.top},{x:s,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,l.m)(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&t7[r.dir].includes(t.key)){var a;n.onOpenChange(!0),null==(a=n.content)||a.focus(),t.preventDefault()}})})})});nK.displayName=nY;var nU="MenuSubContent",nZ=a.forwardRef((e,t)=>{let n=ng(nw,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=nc(nw,e.__scopeMenu),u=nd(nw,e.__scopeMenu),c=nX(nU,e.__scopeMenu),f=a.useRef(null),d=(0,s.s)(t,f);return(0,m.jsx)(nn.Provider,{scope:e.__scopeMenu,children:(0,m.jsx)(tu.C,{present:r||i.open,children:(0,m.jsx)(nn.Slot,{scope:e.__scopeMenu,children:(0,m.jsx)(nM,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:d,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,l.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=ne[u.dir].includes(e.key);if(t&&n){var r;i.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function nQ(e){return e?"open":"closed"}function nJ(e){return"indeterminate"===e}function n0(e){return nJ(e)?"indeterminate":e?"checked":"unchecked"}function n1(e){return t=>"mouse"===t.pointerType?e(t):void 0}nZ.displayName=nU;var n2="DropdownMenu",[n5,n3]=(0,u.A)(n2,[na]),n4=na(),[n9,n6]=n5(n2),n8=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:i,onOpenChange:l,modal:s=!0}=e,u=n4(t),f=a.useRef(null),[d,h]=(0,c.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:n2});return(0,m.jsx)(n9,{scope:t,triggerId:(0,T.B)(),triggerRef:f,contentId:(0,T.B)(),open:d,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),modal:s,children:(0,m.jsx)(nh,{...u,open:d,onOpenChange:h,dir:r,modal:s,children:n})})};n8.displayName=n2;var n7="DropdownMenuTrigger",re=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=n6(n7,n),a=n4(n);return(0,m.jsx)(np,{asChild:!0,...a,children:(0,m.jsx)(f.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,s.t)(t,i.triggerRef),onPointerDown:(0,l.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});re.displayName=n7;var rt=e=>{let{__scopeDropdownMenu:t,...n}=e,r=n4(t);return(0,m.jsx)(ny,{...r,...n})};rt.displayName="DropdownMenuPortal";var rn="DropdownMenuContent",rr=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n6(rn,n),i=n4(n),s=a.useRef(!1);return(0,m.jsx)(nk,{id:o.contentId,"aria-labelledby":o.triggerId,...i,...r,ref:t,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;s.current||null==(t=o.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,l.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rr.displayName=rn,a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nS,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nC,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var ro=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nN,{...o,...r,ref:t})});ro.displayName="DropdownMenuItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nP,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nD,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nH,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nW,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var ri=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nB,{...o,...r,ref:t})});ri.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nG,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nK,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=n4(n);return(0,m.jsx)(nZ,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var ra=n8,rl=re,rs=rt,ru=rr,rc=ro,rf=ri},6672:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var r=n(1235),o=n(2903),i=n(4897),a=n(9204),l=n(806);let s=e=>()=>e;function u(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:l,dx:s,dy:u,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:l,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:c}})}function c(e){return!e.ctrlKey&&!e.button}function f(){return this.parentNode}function d(e,t){return null==t?{x:e.x,y:e.y}:t}function h(){return navigator.maxTouchPoints||"ontouchstart"in this}function p(){var e,t,n,p,m=c,v=f,g=d,y=h,w={},b=(0,r.A)("start","drag","end"),x=0,k=0;function _(e){e.on("mousedown.drag",E).filter(y).on("touchstart.drag",S).on("touchmove.drag",C,l.vr).on("touchend.drag touchcancel.drag",R).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function E(r,i){if(!p&&m.call(this,r,i)){var s=j(this,v.call(this,r,i),r,i,"mouse");s&&((0,o.A)(r.view).on("mousemove.drag",A,l.Rw).on("mouseup.drag",M,l.Rw),(0,a.A)(r.view),(0,l.GK)(r),n=!1,e=r.clientX,t=r.clientY,s("start",r))}}function A(r){if((0,l.Ay)(r),!n){var o=r.clientX-e,i=r.clientY-t;n=o*o+i*i>k}w.mouse("drag",r)}function M(e){(0,o.A)(e.view).on("mousemove.drag mouseup.drag",null),(0,a.y)(e.view,n),(0,l.Ay)(e),w.mouse("end",e)}function S(e,t){if(m.call(this,e,t)){var n,r,o=e.changedTouches,i=v.call(this,e,t),a=o.length;for(n=0;n<a;++n)(r=j(this,i,e,t,o[n].identifier,o[n]))&&((0,l.GK)(e),r("start",e,o[n]))}}function C(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=w[r[t].identifier])&&((0,l.Ay)(e),n("drag",e,r[t]))}function R(e){var t,n,r=e.changedTouches,o=r.length;for(p&&clearTimeout(p),p=setTimeout(function(){p=null},500),t=0;t<o;++t)(n=w[r[t].identifier])&&((0,l.GK)(e),n("end",e,r[t]))}function j(e,t,n,r,o,a){var l,s,c,f=b.copy(),d=(0,i.A)(a||n,t);if(null!=(c=g.call(e,new u("beforestart",{sourceEvent:n,target:_,identifier:o,active:x,x:d[0],y:d[1],dx:0,dy:0,dispatch:f}),r)))return l=c.x-d[0]||0,s=c.y-d[1]||0,function n(a,h,p){var m,v=d;switch(a){case"start":w[o]=n,m=x++;break;case"end":delete w[o],--x;case"drag":d=(0,i.A)(p||h,t),m=x}f.call(a,e,new u(a,{sourceEvent:h,subject:c,target:_,identifier:o,active:m,x:d[0]+l,y:d[1]+s,dx:d[0]-v[0],dy:d[1]-v[1],dispatch:f}),r)}}return _.filter=function(e){return arguments.length?(m="function"==typeof e?e:s(!!e),_):m},_.container=function(e){return arguments.length?(v="function"==typeof e?e:s(e),_):v},_.subject=function(e){return arguments.length?(g="function"==typeof e?e:s(e),_):g},_.touchable=function(e){return arguments.length?(y="function"==typeof e?e:s(!!e),_):y},_.on=function(){var e=b.on.apply(b,arguments);return e===b?_:e},_.clickDistance=function(e){return arguments.length?(k=(e*=1)*e,_):Math.sqrt(k)},_}u.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e}},7108:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7271:(e,t,n)=>{"use strict";function r(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}n.d(t,{A:()=>r})},7328:(e,t,n)=>{"use strict";function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>d});var a,l=n(2115),s=n(6081),u=n(6101),c=n(9708),f=n(5155);function d(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,f.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let d=e+"CollectionSlot",h=(0,c.TL)(d),p=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(d,n),a=(0,u.s)(t,o.collectionRef);return(0,f.jsx)(h,{ref:a,children:r})});p.displayName=d;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,c.TL)(m),y=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=l.useRef(null),s=(0,u.s)(t,a),c=i(m,n);return l.useEffect(()=>(c.itemMap.set(a,{ref:a,...o}),()=>void c.itemMap.delete(a))),(0,f.jsx)(g,{...{[v]:""},ref:s,children:r})});return y.displayName=m,[{Provider:a,Slot:p,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var h=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=m(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function m(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},7434:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7550:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8653:(e,t,n)=>{"use strict";function r(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}n.d(t,{x:()=>r})},8749:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8905:(e,t,n)=>{"use strict";n.d(t,{C:()=>a});var r=n(2115),o=n(6101),i=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),s=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(s.current);c.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=c.current,o=l(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),u.current=e}},[e,d]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(s.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=l(s.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(t),s="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),u=(0,o.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||a.isPresent?r.cloneElement(s,{ref:u}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{"use strict";n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9051:(e,t,n)=>{"use strict";n.d(t,{OK:()=>Y,bL:()=>q,VM:()=>k,lr:()=>P,LM:()=>X});var r=n(2115),o=n(3655),i=n(8905),a=n(6081),l=n(6101),s=n(9033),u=n(4315),c=n(2712),f=n(5185),d=n(5155),h="ScrollArea",[p,m]=(0,a.A)(h),[v,g]=p(h),y=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:a,scrollHideDelay:s=600,...c}=e,[f,h]=r.useState(null),[p,m]=r.useState(null),[g,y]=r.useState(null),[w,b]=r.useState(null),[x,k]=r.useState(null),[_,E]=r.useState(0),[A,M]=r.useState(0),[S,C]=r.useState(!1),[R,j]=r.useState(!1),N=(0,l.s)(t,e=>h(e)),T=(0,u.jH)(a);return(0,d.jsx)(v,{scope:n,type:i,dir:T,scrollHideDelay:s,scrollArea:f,viewport:p,onViewportChange:m,content:g,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:S,onScrollbarXEnabledChange:C,scrollbarY:x,onScrollbarYChange:k,scrollbarYEnabled:R,onScrollbarYEnabledChange:j,onCornerWidthChange:E,onCornerHeightChange:M,children:(0,d.jsx)(o.sG.div,{dir:T,...c,ref:N,style:{position:"relative","--radix-scroll-area-corner-width":_+"px","--radix-scroll-area-corner-height":A+"px",...e.style}})})});y.displayName=h;var w="ScrollAreaViewport",b=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:a,...s}=e,u=g(w,n),c=r.useRef(null),f=(0,l.s)(t,c,u.onViewportChange);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,d.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:f,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,d.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});b.displayName=w;var x="ScrollAreaScrollbar",k=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=g(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=i,s="horizontal"===e.orientation;return r.useEffect(()=>(s?a(!0):l(!0),()=>{s?a(!1):l(!1)}),[s,a,l]),"hover"===i.type?(0,d.jsx)(_,{...o,ref:t,forceMount:n}):"scroll"===i.type?(0,d.jsx)(E,{...o,ref:t,forceMount:n}):"auto"===i.type?(0,d.jsx)(A,{...o,ref:t,forceMount:n}):"always"===i.type?(0,d.jsx)(M,{...o,ref:t}):null});k.displayName=x;var _=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,a=g(x,e.__scopeScrollArea),[l,s]=r.useState(!1);return r.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),s(!0)},r=()=>{t=window.setTimeout(()=>s(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[a.scrollArea,a.scrollHideDelay]),(0,d.jsx)(i.C,{present:n||l,children:(0,d.jsx)(A,{"data-state":l?"visible":"hidden",...o,ref:t})})}),E=r.forwardRef((e,t)=>{var n,o;let{forceMount:a,...l}=e,s=g(x,e.__scopeScrollArea),u="horizontal"===e.orientation,c=B(()=>p("SCROLL_END"),100),[h,p]=(n="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>{let n=o[e][t];return null!=n?n:e},n));return r.useEffect(()=>{if("idle"===h){let e=window.setTimeout(()=>p("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[h,s.scrollHideDelay,p]),r.useEffect(()=>{let e=s.viewport,t=u?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(p("SCROLL"),c()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,u,p,c]),(0,d.jsx)(i.C,{present:a||"hidden"!==h,children:(0,d.jsx)(M,{"data-state":"hidden"===h?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),A=r.forwardRef((e,t)=>{let n=g(x,e.__scopeScrollArea),{forceMount:o,...a}=e,[l,s]=r.useState(!1),u="horizontal"===e.orientation,c=B(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;s(u?e:t)}},10);return G(n.viewport,c),G(n.content,c),(0,d.jsx)(i.C,{present:o||l,children:(0,d.jsx)(M,{"data-state":l?"visible":"hidden",...a,ref:t})})}),M=r.forwardRef((e,t)=>{let{orientation:n="vertical",...o}=e,i=g(x,e.__scopeScrollArea),a=r.useRef(null),l=r.useRef(0),[s,u]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=H(s.viewport,s.content),f={...o,sizes:s,onSizesChange:u,hasThumb:!!(c>0&&c<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function h(e,t){return function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=$(n),i=t||o/2,a=n.scrollbar.paddingStart+i,l=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),s=n.content-n.viewport;return V([a,l],"ltr"===r?[0,s]:[-1*s,0])(e)}(e,l.current,s,t)}return"horizontal"===n?(0,d.jsx)(S,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=F(i.viewport.scrollLeft,s,i.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=h(e,i.dir))}}):"vertical"===n?(0,d.jsx)(C,{...f,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=F(i.viewport.scrollTop,s);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=h(e))}}):null}),S=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=g(x,e.__scopeScrollArea),[s,u]=r.useState(),c=r.useRef(null),f=(0,l.s)(t,c,a.onScrollbarXChange);return r.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,d.jsx)(N,{"data-orientation":"horizontal",...i,ref:f,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":$(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&s&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:I(s.paddingLeft),paddingEnd:I(s.paddingRight)}})}})}),C=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=g(x,e.__scopeScrollArea),[s,u]=r.useState(),c=r.useRef(null),f=(0,l.s)(t,c,a.onScrollbarYChange);return r.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,d.jsx)(N,{"data-orientation":"vertical",...i,ref:f,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":$(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&s&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:I(s.paddingTop),paddingEnd:I(s.paddingBottom)}})}})}),[R,j]=p(x),N=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:a,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:h,onThumbPositionChange:p,onDragScroll:m,onWheelScroll:v,onResize:y,...w}=e,b=g(x,n),[k,_]=r.useState(null),E=(0,l.s)(t,e=>_(e)),A=r.useRef(null),M=r.useRef(""),S=b.viewport,C=i.content-i.viewport,j=(0,s.c)(v),N=(0,s.c)(p),T=B(y,10);function P(e){A.current&&m({x:e.clientX-A.current.left,y:e.clientY-A.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;(null==k?void 0:k.contains(t))&&j(e,C)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[S,k,C,j]),r.useEffect(N,[i,N]),G(k,T),G(b.content,T),(0,d.jsx)(R,{scope:n,scrollbar:k,hasThumb:a,onThumbChange:(0,s.c)(u),onThumbPointerUp:(0,s.c)(c),onThumbPositionChange:N,onThumbPointerDown:(0,s.c)(h),children:(0,d.jsx)(o.sG.div,{...w,ref:E,style:{position:"absolute",...w.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),A.current=k.getBoundingClientRect(),M.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),P(e))}),onPointerMove:(0,f.m)(e.onPointerMove,P),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=M.current,b.viewport&&(b.viewport.style.scrollBehavior=""),A.current=null})})})}),T="ScrollAreaThumb",P=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=j(T,e.__scopeScrollArea);return(0,d.jsx)(i.C,{present:n||o.hasThumb,children:(0,d.jsx)(z,{ref:t,...r})})}),z=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...a}=e,s=g(T,n),u=j(T,n),{onThumbPositionChange:c}=u,h=(0,l.s)(t,e=>u.onThumbChange(e)),p=r.useRef(void 0),m=B(()=>{p.current&&(p.current(),p.current=void 0)},100);return r.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{m(),p.current||(p.current=W(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,c]),(0,d.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:h,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;u.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,f.m)(e.onPointerUp,u.onThumbPointerUp)})});P.displayName=T;var L="ScrollAreaCorner",O=r.forwardRef((e,t)=>{let n=g(L,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,d.jsx)(D,{...e,ref:t}):null});O.displayName=L;var D=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,a=g(L,n),[l,s]=r.useState(0),[u,c]=r.useState(0),f=!!(l&&u);return G(a.scrollbarX,()=>{var e;let t=(null==(e=a.scrollbarX)?void 0:e.offsetHeight)||0;a.onCornerHeightChange(t),c(t)}),G(a.scrollbarY,()=>{var e;let t=(null==(e=a.scrollbarY)?void 0:e.offsetWidth)||0;a.onCornerWidthChange(t),s(t)}),f?(0,d.jsx)(o.sG.div,{...i,ref:t,style:{width:l,height:u,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function I(e){return e?parseInt(e,10):0}function H(e,t){let n=e/t;return isNaN(n)?0:n}function $(e){let t=H(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function F(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=$(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,l=function(e,[t,n]){return Math.min(n,Math.max(t,e))}(e,"ltr"===n?[0,a]:[-1*a,0]);return V([0,a],[0,i-r])(l)}function V(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var W=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,l=n.top!==i.top;(a||l)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function B(e,t){let n=(0,s.c)(e),o=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(n,t)},[n,t])}function G(e,t){let n=(0,s.c)(t);(0,c.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var q=y,X=b,Y=O},9074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9196:(e,t,n)=>{"use strict";n.d(t,{RG:()=>x,bL:()=>j,q7:()=>N});var r=n(2115),o=n(5185),i=n(7328),a=n(6101),l=n(6081),s=n(1285),u=n(3655),c=n(9033),f=n(5845),d=n(4315),h=n(5155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,i.N)(v),[b,x]=(0,l.A)(v,[w]),[k,_]=b(v),E=r.forwardRef((e,t)=>(0,h.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(A,{...e,ref:t})})}));E.displayName=v;var A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:s,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:_=!1,...E}=e,A=r.useRef(null),M=(0,a.s)(t,A),S=(0,d.jH)(s),[C,j]=(0,f.i)({prop:g,defaultProp:null!=w?w:null,onChange:b,caller:v}),[N,T]=r.useState(!1),P=(0,c.c)(x),z=y(n),L=r.useRef(!1),[O,D]=r.useState(0);return r.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,P),()=>e.removeEventListener(p,P)},[P]),(0,h.jsx)(k,{scope:n,orientation:i,dir:S,loop:l,currentTabStopId:C,onItemFocus:r.useCallback(e=>j(e),[j]),onItemShiftTab:r.useCallback(()=>T(!0),[]),onFocusableItemAdd:r.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>D(e=>e-1),[]),children:(0,h.jsx)(u.sG.div,{tabIndex:N||0===O?-1:0,"data-orientation":i,...E,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!N){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=z().filter(e=>e.focusable);R([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),_)}}L.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),M="RovingFocusGroupItem",S=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...f}=e,d=(0,s.B)(),p=l||d,m=_(M,n),v=m.currentTabStopId===p,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:k}=m;return r.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,h.jsx)(g.ItemSlot,{scope:n,id:p,focusable:i,active:a,children:(0,h.jsx)(u.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return C[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>R(n))}}),children:"function"==typeof c?c({isCurrentTabStop:v,hasTabStop:null!=k}):c})})});S.displayName=M;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function R(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var j=E,N=S},9204:(e,t,n)=>{"use strict";n.d(t,{A:()=>i,y:()=>a});var r=n(2903),o=n(806);function i(e){var t=e.document.documentElement,n=(0,r.A)(e).on("dragstart.drag",o.Ay,o.Rw);"onselectstart"in t?n.on("selectstart.drag",o.Ay,o.Rw):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function a(e,t){var n=e.document.documentElement,i=(0,r.A)(e).on("dragstart.drag",null);t&&(i.on("click.drag",o.Ay,o.Rw),setTimeout(function(){i.on("click.drag",null)},0)),"onselectstart"in n?i.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}},9293:(e,t,n)=>{"use strict";function r(){return[]}function o(e){return null==e?r:function(){return this.querySelectorAll(e)}}n.d(t,{A:()=>o})},9295:(e,t,n)=>{"use strict";n.d(t,{H:()=>m});var r=n(2115),o=n(5694);function i(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var a=n(4211);function l(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},r.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function s(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},r.createElement("path",{d:"M0 0h32v4.2H0z"}))}function u(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},r.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function c(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function f(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let d=({children:e,className:t,...n})=>r.createElement("button",{type:"button",className:(0,o.A)(["react-flow__controls-button",t]),...n},e);d.displayName="ControlButton";let h=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),p=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:p=!0,fitViewOptions:m,onZoomIn:v,onZoomOut:g,onFitView:y,onInteractiveChange:w,className:b,children:x,position:k="bottom-left"})=>{let _=(0,a.PI)(),[E,A]=(0,r.useState)(!1),{isInteractive:M,minZoomReached:S,maxZoomReached:C}=(0,a.Pj)(h,i),{zoomIn:R,zoomOut:j,fitView:N}=(0,a.VH)();return((0,r.useEffect)(()=>{A(!0)},[]),E)?r.createElement(a.Zk,{className:(0,o.A)(["react-flow__controls",b]),position:k,style:e,"data-testid":"rf__controls"},t&&r.createElement(r.Fragment,null,r.createElement(d,{onClick:()=>{R(),v?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:C},r.createElement(l,null)),r.createElement(d,{onClick:()=>{j(),g?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:S},r.createElement(s,null))),n&&r.createElement(d,{className:"react-flow__controls-fitview",onClick:()=>{N(m),y?.()},title:"fit view","aria-label":"fit view"},r.createElement(u,null)),p&&r.createElement(d,{className:"react-flow__controls-interactive",onClick:()=>{_.setState({nodesDraggable:!M,nodesConnectable:!M,elementsSelectable:!M}),w?.(!M)},title:"toggle interactivity","aria-label":"toggle interactivity"},M?r.createElement(f,null):r.createElement(c,null)),x):null};p.displayName="Controls";var m=(0,r.memo)(p)},9621:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9688:(e,t,n)=>{"use strict";n.d(t,{QP:()=>eu});let r=e=>{let t=l(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),o(n,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}},o=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],r=t.nextPart.get(n),i=r?o(e.slice(1),r):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},i=/^\[(.+)\]$/,a=e=>{if(i.test(e)){let t=i.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},l=e=>{let{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(let e in n)s(n[e],r,e,t);return r},s=(e,t,n,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=n;return}if("function"==typeof e)return c(e)?void s(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach(([e,o])=>{s(o,u(t,e),n,r)})})},u=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},c=e=>e.isThemeGetter,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,r=new Map,o=(o,i)=>{n.set(o,i),++t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},d=e=>{let{prefix:t,experimentalParseClassName:n}=e,r=e=>{let t,n=[],r=0,o=0,i=0;for(let a=0;a<e.length;a++){let l=e[a];if(0===r&&0===o){if(":"===l){n.push(e.slice(i,a)),i=a+1;continue}if("/"===l){t=a;continue}}"["===l?r++:"]"===l?r--:"("===l?o++:")"===l&&o--}let a=0===n.length?e:e.substring(i),l=h(a);return{modifiers:n,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){let e=r;r=t=>n({className:t,parseClassName:e})}return r},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,p=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let n=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)}),n.push(...r.sort()),n}},m=e=>({cache:f(e.cacheSize),parseClassName:d(e),sortModifiers:p(e),...r(e)}),v=/\s+/,g=(e,t)=>{let{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:i}=t,a=[],l=e.trim().split(v),s="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:u,modifiers:c,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:h}=n(t);if(u){s=t+(s.length>0?" "+s:s);continue}let p=!!h,m=r(p?d.substring(0,h):d);if(!m){if(!p||!(m=r(d))){s=t+(s.length>0?" "+s:s);continue}p=!1}let v=i(c).join(":"),g=f?v+"!":v,y=g+m;if(a.includes(y))continue;a.push(y);let w=o(m,p);for(let e=0;e<w.length;++e){let t=w[e];a.push(g+t)}s=t+(s.length>0?" "+s:s)}return s};function y(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=w(e))&&(r&&(r+=" "),r+=t);return r}let w=e=>{let t;if("string"==typeof e)return e;let n="";for(let r=0;r<e.length;r++)e[r]&&(t=w(e[r]))&&(n&&(n+=" "),n+=t);return n},b=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,_=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,C=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=e=>_.test(e),j=e=>!!e&&!Number.isNaN(Number(e)),N=e=>!!e&&Number.isInteger(Number(e)),T=e=>e.endsWith("%")&&j(e.slice(0,-1)),P=e=>E.test(e),z=()=>!0,L=e=>A.test(e)&&!M.test(e),O=()=>!1,D=e=>S.test(e),I=e=>C.test(e),H=e=>!F(e)&&!X(e),$=e=>ee(e,eo,O),F=e=>x.test(e),V=e=>ee(e,ei,L),W=e=>ee(e,ea,j),B=e=>ee(e,en,O),G=e=>ee(e,er,I),q=e=>ee(e,es,D),X=e=>k.test(e),Y=e=>et(e,ei),K=e=>et(e,el),U=e=>et(e,en),Z=e=>et(e,eo),Q=e=>et(e,er),J=e=>et(e,es,!0),ee=(e,t,n)=>{let r=x.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},et=(e,t,n=!1)=>{let r=k.exec(e);return!!r&&(r[1]?t(r[1]):n)},en=e=>"position"===e||"percentage"===e,er=e=>"image"===e||"url"===e,eo=e=>"length"===e||"size"===e||"bg-size"===e,ei=e=>"length"===e,ea=e=>"number"===e,el=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let eu=function(e,...t){let n,r,o,i=function(l){return r=(n=m(t.reduce((e,t)=>t(e),e()))).cache.get,o=n.cache.set,i=a,a(l)};function a(e){let t=r(e);if(t)return t;let i=g(e,n);return o(e,i),i}return function(){return i(y.apply(null,arguments))}}(()=>{let e=b("color"),t=b("font"),n=b("text"),r=b("font-weight"),o=b("tracking"),i=b("leading"),a=b("breakpoint"),l=b("container"),s=b("spacing"),u=b("radius"),c=b("shadow"),f=b("inset-shadow"),d=b("text-shadow"),h=b("drop-shadow"),p=b("blur"),m=b("perspective"),v=b("aspect"),g=b("ease"),y=b("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),X,F],_=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],A=()=>[X,F,s],M=()=>[R,"full","auto",...A()],S=()=>[N,"none","subgrid",X,F],C=()=>["auto",{span:["full",N,X,F]},N,X,F],L=()=>[N,"auto",X,F],O=()=>["auto","min","max","fr",X,F],D=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...A()],et=()=>[R,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...A()],en=()=>[e,X,F],er=()=>[...x(),U,B,{position:[X,F]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",Z,$,{size:[X,F]}],ea=()=>[T,Y,V],el=()=>["","none","full",u,X,F],es=()=>["",j,Y,V],eu=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ef=()=>[j,T,U,B],ed=()=>["","none",p,X,F],eh=()=>["none",j,X,F],ep=()=>["none",j,X,F],em=()=>[j,X,F],ev=()=>[R,"full",...A()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[P],breakpoint:[P],color:[z],container:[P],"drop-shadow":[P],ease:["in","out","in-out"],font:[H],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[P],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[P],shadow:[P],spacing:["px",j],text:[P],"text-shadow":[P],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",R,F,X,v]}],container:["container"],columns:[{columns:[j,F,X,l]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:M()}],"inset-x":[{"inset-x":M()}],"inset-y":[{"inset-y":M()}],start:[{start:M()}],end:[{end:M()}],top:[{top:M()}],right:[{right:M()}],bottom:[{bottom:M()}],left:[{left:M()}],visibility:["visible","invisible","collapse"],z:[{z:[N,"auto",X,F]}],basis:[{basis:[R,"full","auto",l,...A()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[j,R,"auto","initial","none",F]}],grow:[{grow:["",j,X,F]}],shrink:[{shrink:["",j,X,F]}],order:[{order:[N,"first","last","none",X,F]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":O()}],"auto-rows":[{"auto-rows":O()}],gap:[{gap:A()}],"gap-x":[{"gap-x":A()}],"gap-y":[{"gap-y":A()}],"justify-content":[{justify:[...D(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...D()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":D()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:A()}],px:[{px:A()}],py:[{py:A()}],ps:[{ps:A()}],pe:[{pe:A()}],pt:[{pt:A()}],pr:[{pr:A()}],pb:[{pb:A()}],pl:[{pl:A()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":A()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":A()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",n,Y,V]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,X,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",T,F]}],"font-family":[{font:[K,F,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,X,F]}],"line-clamp":[{"line-clamp":[j,"none",X,W]}],leading:[{leading:[i,...A()]}],"list-image":[{"list-image":["none",X,F]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:en()}],"text-color":[{text:en()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[j,"from-font","auto",X,V]}],"text-decoration-color":[{decoration:en()}],"underline-offset":[{"underline-offset":[j,"auto",X,F]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},N,X,F],radial:["",X,F],conic:[N,X,F]},Q,G]}],"bg-color":[{bg:en()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:en()}],"gradient-via":[{via:en()}],"gradient-to":[{to:en()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:en()}],"border-color-x":[{"border-x":en()}],"border-color-y":[{"border-y":en()}],"border-color-s":[{"border-s":en()}],"border-color-e":[{"border-e":en()}],"border-color-t":[{"border-t":en()}],"border-color-r":[{"border-r":en()}],"border-color-b":[{"border-b":en()}],"border-color-l":[{"border-l":en()}],"divide-color":[{divide:en()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[j,X,F]}],"outline-w":[{outline:["",j,Y,V]}],"outline-color":[{outline:en()}],shadow:[{shadow:["","none",c,J,q]}],"shadow-color":[{shadow:en()}],"inset-shadow":[{"inset-shadow":["none",f,J,q]}],"inset-shadow-color":[{"inset-shadow":en()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:en()}],"ring-offset-w":[{"ring-offset":[j,V]}],"ring-offset-color":[{"ring-offset":en()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":en()}],"text-shadow":[{"text-shadow":["none",d,J,q]}],"text-shadow-color":[{"text-shadow":en()}],opacity:[{opacity:[j,X,F]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[j]}],"mask-image-linear-from-pos":[{"mask-linear-from":ef()}],"mask-image-linear-to-pos":[{"mask-linear-to":ef()}],"mask-image-linear-from-color":[{"mask-linear-from":en()}],"mask-image-linear-to-color":[{"mask-linear-to":en()}],"mask-image-t-from-pos":[{"mask-t-from":ef()}],"mask-image-t-to-pos":[{"mask-t-to":ef()}],"mask-image-t-from-color":[{"mask-t-from":en()}],"mask-image-t-to-color":[{"mask-t-to":en()}],"mask-image-r-from-pos":[{"mask-r-from":ef()}],"mask-image-r-to-pos":[{"mask-r-to":ef()}],"mask-image-r-from-color":[{"mask-r-from":en()}],"mask-image-r-to-color":[{"mask-r-to":en()}],"mask-image-b-from-pos":[{"mask-b-from":ef()}],"mask-image-b-to-pos":[{"mask-b-to":ef()}],"mask-image-b-from-color":[{"mask-b-from":en()}],"mask-image-b-to-color":[{"mask-b-to":en()}],"mask-image-l-from-pos":[{"mask-l-from":ef()}],"mask-image-l-to-pos":[{"mask-l-to":ef()}],"mask-image-l-from-color":[{"mask-l-from":en()}],"mask-image-l-to-color":[{"mask-l-to":en()}],"mask-image-x-from-pos":[{"mask-x-from":ef()}],"mask-image-x-to-pos":[{"mask-x-to":ef()}],"mask-image-x-from-color":[{"mask-x-from":en()}],"mask-image-x-to-color":[{"mask-x-to":en()}],"mask-image-y-from-pos":[{"mask-y-from":ef()}],"mask-image-y-to-pos":[{"mask-y-to":ef()}],"mask-image-y-from-color":[{"mask-y-from":en()}],"mask-image-y-to-color":[{"mask-y-to":en()}],"mask-image-radial":[{"mask-radial":[X,F]}],"mask-image-radial-from-pos":[{"mask-radial-from":ef()}],"mask-image-radial-to-pos":[{"mask-radial-to":ef()}],"mask-image-radial-from-color":[{"mask-radial-from":en()}],"mask-image-radial-to-color":[{"mask-radial-to":en()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[j]}],"mask-image-conic-from-pos":[{"mask-conic-from":ef()}],"mask-image-conic-to-pos":[{"mask-conic-to":ef()}],"mask-image-conic-from-color":[{"mask-conic-from":en()}],"mask-image-conic-to-color":[{"mask-conic-to":en()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,F]}],filter:[{filter:["","none",X,F]}],blur:[{blur:ed()}],brightness:[{brightness:[j,X,F]}],contrast:[{contrast:[j,X,F]}],"drop-shadow":[{"drop-shadow":["","none",h,J,q]}],"drop-shadow-color":[{"drop-shadow":en()}],grayscale:[{grayscale:["",j,X,F]}],"hue-rotate":[{"hue-rotate":[j,X,F]}],invert:[{invert:["",j,X,F]}],saturate:[{saturate:[j,X,F]}],sepia:[{sepia:["",j,X,F]}],"backdrop-filter":[{"backdrop-filter":["","none",X,F]}],"backdrop-blur":[{"backdrop-blur":ed()}],"backdrop-brightness":[{"backdrop-brightness":[j,X,F]}],"backdrop-contrast":[{"backdrop-contrast":[j,X,F]}],"backdrop-grayscale":[{"backdrop-grayscale":["",j,X,F]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[j,X,F]}],"backdrop-invert":[{"backdrop-invert":["",j,X,F]}],"backdrop-opacity":[{"backdrop-opacity":[j,X,F]}],"backdrop-saturate":[{"backdrop-saturate":[j,X,F]}],"backdrop-sepia":[{"backdrop-sepia":["",j,X,F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":A()}],"border-spacing-x":[{"border-spacing-x":A()}],"border-spacing-y":[{"border-spacing-y":A()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,F]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[j,"initial",X,F]}],ease:[{ease:["linear","initial",g,X,F]}],delay:[{delay:[j,X,F]}],animate:[{animate:["none",y,X,F]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,X,F]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:eh()}],"rotate-x":[{"rotate-x":eh()}],"rotate-y":[{"rotate-y":eh()}],"rotate-z":[{"rotate-z":eh()}],scale:[{scale:ep()}],"scale-x":[{"scale-x":ep()}],"scale-y":[{"scale-y":ep()}],"scale-z":[{"scale-z":ep()}],"scale-3d":["scale-3d"],skew:[{skew:em()}],"skew-x":[{"skew-x":em()}],"skew-y":[{"skew-y":em()}],transform:[{transform:[X,F,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ev()}],"translate-x":[{"translate-x":ev()}],"translate-y":[{"translate-y":ev()}],"translate-z":[{"translate-z":ev()}],"translate-none":["translate-none"],accent:[{accent:en()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:en()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,F]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,F]}],fill:[{fill:["none",...en()]}],"stroke-w":[{stroke:[j,Y,V,W]}],stroke:[{stroke:["none",...en()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,n)=>{"use strict";n.d(t,{DX:()=>l,TL:()=>a});var r=n(2115),o=n(6101),i=n(5155);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var a;let e,l,s=(a=n,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,o.t)(t,s):s),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...a}=e,l=r.Children.toArray(o),s=l.find(u);if(s){let e=s.props.children,o=l.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var l=a("Slot"),s=Symbol("radix.slottable");function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},9799:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},9869:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9890:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},9946:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),a=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:c="",children:f,iconNode:d,...h}=e;return(0,r.createElement)("svg",{ref:t,...u,width:o,height:o,stroke:n,strokeWidth:a?24*Number(i)/Number(o):i,className:l("lucide",c),...!f&&!s(h)&&{"aria-hidden":"true"},...h},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(f)?f:[f]])}),f=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:s,...u}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:l("lucide-".concat(o(a(e))),"lucide-".concat(e),s),...u})});return n.displayName=a(e),n}}}]);