"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                // 检测内容格式并转换为 HTML\n                if ((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.isMarkdown)(fileContent)) {\n                    setHtmlContent((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.markdownToHtml)(fileContent));\n                } else {\n                    setHtmlContent(fileContent);\n                }\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            // 将 HTML 内容转换为 Markdown 保存\n            const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n            updateFile(file.id, contentToSave);\n            setHasChanges(false);\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: [\n                    editMode === 'visual' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                            content: htmlContent,\n                            onChange: handleContentChange,\n                            placeholder: \"开始编写文档...\",\n                            editable: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    editMode === 'markdown' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Textarea, {\n                            value: content,\n                            onChange: (e)=>handleContentChange(e.target.value),\n                            className: \"w-full h-full resize-none font-mono text-sm\",\n                            placeholder: \"# 开始编写 Markdown 文档...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    editMode === 'preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full p-4 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: htmlContent\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-t bg-gray-50 text-xs text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    file.type === 'document' ? '📄' : file.type === 'data' ? '📊' : '⚙️',\n                                    \" \",\n                                    file.type\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"编辑模式: \",\n                                    editMode === 'visual' ? '可视化' : editMode === 'markdown' ? 'Markdown' : '预览'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600\",\n                                children: \"● 未保存\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: (()=>{\n                            const stats = getContentStats(editMode === 'visual' ? htmlContent : content);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            stats.words,\n                                            \" 词\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            stats.characters,\n                                            \" 字符\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            stats.lines,\n                                            \" 行\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"约 \",\n                                            stats.readingTime,\n                                            \" 分钟阅读\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true);\n                        })()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});