"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/RichTextEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/RichTextEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RichTextEditor: () => (/* binding */ RichTextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(app-pages-browser)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/extension-placeholder */ \"(app-pages-browser)/./node_modules/@tiptap/extension-placeholder/dist/index.js\");\n/* harmony import */ var _tiptap_extension_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-link */ \"(app-pages-browser)/./node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var _tiptap_extension_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-image */ \"(app-pages-browser)/./node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/extension-table */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table_row__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tiptap/extension-table-row */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table-row/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tiptap/extension-table-header */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table-header/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table_cell__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tiptap/extension-table-cell */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table-cell/dist/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heading-1.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heading-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heading-3.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bold.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/italic.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/strikethrough.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ RichTextEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RichTextEditor(param) {\n    let { content, onChange, placeholder = \"开始编写文档...\", editable = true } = param;\n    _s();\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_11__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n                bulletList: {\n                    keepMarks: true,\n                    keepAttributes: false\n                },\n                orderedList: {\n                    keepMarks: true,\n                    keepAttributes: false\n                }\n            }),\n            _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_2__[\"default\"].configure({\n                placeholder\n            }),\n            _tiptap_extension_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"].configure({\n                openOnClick: false,\n                HTMLAttributes: {\n                    class: 'text-blue-600 underline cursor-pointer'\n                }\n            }),\n            _tiptap_extension_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"].configure({\n                HTMLAttributes: {\n                    class: 'max-w-full h-auto rounded-lg'\n                }\n            }),\n            _tiptap_extension_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"].configure({\n                resizable: true\n            }),\n            _tiptap_extension_table_row__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            _tiptap_extension_table_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            _tiptap_extension_table_cell__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        ],\n        content,\n        editable,\n        onUpdate: {\n            \"RichTextEditor.useEditor[editor]\": (param)=>{\n                let { editor } = param;\n                onChange(editor.getHTML());\n            }\n        }[\"RichTextEditor.useEditor[editor]\"]\n    });\n    const addImage = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)({\n        \"RichTextEditor.useCallback[addImage]\": ()=>{\n            const url = window.prompt('请输入图片URL:');\n            if (url && editor) {\n                editor.chain().focus().setImage({\n                    src: url\n                }).run();\n            }\n        }\n    }[\"RichTextEditor.useCallback[addImage]\"], [\n        editor\n    ]);\n    const addLink = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)({\n        \"RichTextEditor.useCallback[addLink]\": ()=>{\n            const previousUrl = editor === null || editor === void 0 ? void 0 : editor.getAttributes('link').href;\n            const url = window.prompt('请输入链接URL:', previousUrl);\n            if (url === null) {\n                return;\n            }\n            if (url === '') {\n                editor === null || editor === void 0 ? void 0 : editor.chain().focus().extendMarkRange('link').unsetLink().run();\n                return;\n            }\n            editor === null || editor === void 0 ? void 0 : editor.chain().focus().extendMarkRange('link').setLink({\n                href: url\n            }).run();\n        }\n    }[\"RichTextEditor.useCallback[addLink]\"], [\n        editor\n    ]);\n    const addTable = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)({\n        \"RichTextEditor.useCallback[addTable]\": ()=>{\n            editor === null || editor === void 0 ? void 0 : editor.chain().focus().insertTable({\n                rows: 3,\n                cols: 3,\n                withHeaderRow: true\n            }).run();\n        }\n    }[\"RichTextEditor.useCallback[addTable]\"], [\n        editor\n    ]);\n    if (!editor) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-2 flex flex-wrap gap-1 bg-gray-50 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().undo().run(),\n                                disabled: !editor.can().chain().focus().undo().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().redo().run(),\n                                disabled: !editor.can().chain().focus().redo().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('heading', {\n                                    level: 1\n                                }) ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 1\n                                    }).run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('heading', {\n                                    level: 2\n                                }) ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 2\n                                    }).run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('heading', {\n                                    level: 3\n                                }) ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 3\n                                    }).run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('bold') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleBold().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('italic') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleItalic().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('strike') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleStrike().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('code') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleCode().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('bulletList') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleBulletList().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('orderedList') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleOrderedList().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('blockquote') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleBlockquote().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: addLink,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: addImage,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: addTable,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-[400px] max-h-[600px] overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tiptap_react__WEBPACK_IMPORTED_MODULE_11__.EditorContent, {\n                    editor: editor,\n                    className: \"prose prose-sm max-w-none p-4 focus:outline-none\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(RichTextEditor, \"obHF6um5WkDgj3AYbPTJMtwpjwQ=\", false, function() {\n    return [\n        _tiptap_react__WEBPACK_IMPORTED_MODULE_11__.useEditor\n    ];\n});\n_c = RichTextEditor;\nvar _c;\n$RefreshReg$(_c, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1JpY2hUZXh0RWRpdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdEO0FBQ1o7QUFDVztBQUNkO0FBQ0U7QUFDQTtBQUNPO0FBQ007QUFDSjtBQUNMO0FBcUIxQjtBQUNjO0FBUzVCLFNBQVMyQixlQUFlLEtBS1Q7UUFMUyxFQUM3QkMsT0FBTyxFQUNQQyxRQUFRLEVBQ1JDLGNBQWMsV0FBVyxFQUN6QkMsV0FBVyxJQUFJLEVBQ0ssR0FMUzs7SUFNN0IsTUFBTUMsU0FBU2hDLHlEQUFTQSxDQUFDO1FBQ3ZCaUMsWUFBWTtZQUNWL0IsMkRBQVVBLENBQUNnQyxTQUFTLENBQUM7Z0JBQ25CQyxZQUFZO29CQUNWQyxXQUFXO29CQUNYQyxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxhQUFhO29CQUNYRixXQUFXO29CQUNYQyxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFDQWxDLHFFQUFXQSxDQUFDK0IsU0FBUyxDQUFDO2dCQUNwQko7WUFDRjtZQUNBMUIsOERBQUlBLENBQUM4QixTQUFTLENBQUM7Z0JBQ2JLLGFBQWE7Z0JBQ2JDLGdCQUFnQjtvQkFDZEMsT0FBTztnQkFDVDtZQUNGO1lBQ0FwQywrREFBS0EsQ0FBQzZCLFNBQVMsQ0FBQztnQkFDZE0sZ0JBQWdCO29CQUNkQyxPQUFPO2dCQUNUO1lBQ0Y7WUFDQW5DLCtEQUFLQSxDQUFDNEIsU0FBUyxDQUFDO2dCQUNkUSxXQUFXO1lBQ2I7WUFDQW5DLG1FQUFRQTtZQUNSQyxzRUFBV0E7WUFDWEMsb0VBQVNBO1NBQ1Y7UUFDRG1CO1FBQ0FHO1FBQ0FZLFFBQVE7Z0RBQUU7b0JBQUMsRUFBRVgsTUFBTSxFQUFFO2dCQUNuQkgsU0FBU0csT0FBT1ksT0FBTztZQUN6Qjs7SUFDRjtJQUVBLE1BQU1DLFdBQVduQixtREFBV0E7Z0RBQUM7WUFDM0IsTUFBTW9CLE1BQU1DLE9BQU9DLE1BQU0sQ0FBQztZQUMxQixJQUFJRixPQUFPZCxRQUFRO2dCQUNqQkEsT0FBT2lCLEtBQUssR0FBR0MsS0FBSyxHQUFHQyxRQUFRLENBQUM7b0JBQUVDLEtBQUtOO2dCQUFJLEdBQUdPLEdBQUc7WUFDbkQ7UUFDRjsrQ0FBRztRQUFDckI7S0FBTztJQUVYLE1BQU1zQixVQUFVNUIsbURBQVdBOytDQUFDO1lBQzFCLE1BQU02QixjQUFjdkIsbUJBQUFBLDZCQUFBQSxPQUFRd0IsYUFBYSxDQUFDLFFBQVFDLElBQUk7WUFDdEQsTUFBTVgsTUFBTUMsT0FBT0MsTUFBTSxDQUFDLGFBQWFPO1lBRXZDLElBQUlULFFBQVEsTUFBTTtnQkFDaEI7WUFDRjtZQUVBLElBQUlBLFFBQVEsSUFBSTtnQkFDZGQsbUJBQUFBLDZCQUFBQSxPQUFRaUIsS0FBSyxHQUFHQyxLQUFLLEdBQUdRLGVBQWUsQ0FBQyxRQUFRQyxTQUFTLEdBQUdOLEdBQUc7Z0JBQy9EO1lBQ0Y7WUFFQXJCLG1CQUFBQSw2QkFBQUEsT0FBUWlCLEtBQUssR0FBR0MsS0FBSyxHQUFHUSxlQUFlLENBQUMsUUFBUUUsT0FBTyxDQUFDO2dCQUFFSCxNQUFNWDtZQUFJLEdBQUdPLEdBQUc7UUFDNUU7OENBQUc7UUFBQ3JCO0tBQU87SUFFWCxNQUFNNkIsV0FBV25DLG1EQUFXQTtnREFBQztZQUMzQk0sbUJBQUFBLDZCQUFBQSxPQUFRaUIsS0FBSyxHQUFHQyxLQUFLLEdBQUdZLFdBQVcsQ0FBQztnQkFBRUMsTUFBTTtnQkFBR0MsTUFBTTtnQkFBR0MsZUFBZTtZQUFLLEdBQUdaLEdBQUc7UUFDcEY7K0NBQUc7UUFBQ3JCO0tBQU87SUFFWCxJQUFJLENBQUNBLFFBQVE7UUFDWCxPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ2tDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3pELHlEQUFNQTtnQ0FDTDBELFNBQVE7Z0NBQ1JDLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBR3FCLElBQUksR0FBR2xCLEdBQUc7Z0NBQ2hEbUIsVUFBVSxDQUFDeEMsT0FBT3lDLEdBQUcsR0FBR3hCLEtBQUssR0FBR0MsS0FBSyxHQUFHcUIsSUFBSSxHQUFHbEIsR0FBRztnQ0FDbERjLFdBQVU7MENBRVYsNEVBQUMzQyx3TEFBSUE7b0NBQUMyQyxXQUFVOzs7Ozs7Ozs7OzswQ0FFbEIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFRO2dDQUNSQyxNQUFLO2dDQUNMQyxTQUFTLElBQU10QyxPQUFPaUIsS0FBSyxHQUFHQyxLQUFLLEdBQUd3QixJQUFJLEdBQUdyQixHQUFHO2dDQUNoRG1CLFVBQVUsQ0FBQ3hDLE9BQU95QyxHQUFHLEdBQUd4QixLQUFLLEdBQUdDLEtBQUssR0FBR3dCLElBQUksR0FBR3JCLEdBQUc7Z0NBQ2xEYyxXQUFVOzBDQUVWLDRFQUFDMUMsd0xBQUlBO29DQUFDMEMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3BCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxXQUFXO29DQUFFQyxPQUFPO2dDQUFFLEtBQUssWUFBWTtnQ0FDaEVQLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBRzJCLGFBQWEsQ0FBQzt3Q0FBRUQsT0FBTztvQ0FBRSxHQUFHdkIsR0FBRztnQ0FDckVjLFdBQVU7MENBRVYsNEVBQUNwRCx3TEFBUUE7b0NBQUNvRCxXQUFVOzs7Ozs7Ozs7OzswQ0FFdEIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxXQUFXO29DQUFFQyxPQUFPO2dDQUFFLEtBQUssWUFBWTtnQ0FDaEVQLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBRzJCLGFBQWEsQ0FBQzt3Q0FBRUQsT0FBTztvQ0FBRSxHQUFHdkIsR0FBRztnQ0FDckVjLFdBQVU7MENBRVYsNEVBQUNuRCx3TEFBUUE7b0NBQUNtRCxXQUFVOzs7Ozs7Ozs7OzswQ0FFdEIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxXQUFXO29DQUFFQyxPQUFPO2dDQUFFLEtBQUssWUFBWTtnQ0FDaEVQLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBRzJCLGFBQWEsQ0FBQzt3Q0FBRUQsT0FBTztvQ0FBRSxHQUFHdkIsR0FBRztnQ0FDckVjLFdBQVU7MENBRVYsNEVBQUNsRCx3TEFBUUE7b0NBQUNrRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLeEIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3pELHlEQUFNQTtnQ0FDTDBELFNBQVNwQyxPQUFPMkMsUUFBUSxDQUFDLFVBQVUsWUFBWTtnQ0FDL0NOLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBRzRCLFVBQVUsR0FBR3pCLEdBQUc7Z0NBQ3REYyxXQUFVOzBDQUVWLDRFQUFDeEQsd0xBQUlBO29DQUFDd0QsV0FBVTs7Ozs7Ozs7Ozs7MENBRWxCLDhEQUFDekQseURBQU1BO2dDQUNMMEQsU0FBU3BDLE9BQU8yQyxRQUFRLENBQUMsWUFBWSxZQUFZO2dDQUNqRE4sTUFBSztnQ0FDTEMsU0FBUyxJQUFNdEMsT0FBT2lCLEtBQUssR0FBR0MsS0FBSyxHQUFHNkIsWUFBWSxHQUFHMUIsR0FBRztnQ0FDeERjLFdBQVU7MENBRVYsNEVBQUN2RCx3TEFBTUE7b0NBQUN1RCxXQUFVOzs7Ozs7Ozs7OzswQ0FFcEIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxZQUFZLFlBQVk7Z0NBQ2pETixNQUFLO2dDQUNMQyxTQUFTLElBQU10QyxPQUFPaUIsS0FBSyxHQUFHQyxLQUFLLEdBQUc4QixZQUFZLEdBQUczQixHQUFHO2dDQUN4RGMsV0FBVTswQ0FFViw0RUFBQ3RELHdMQUFhQTtvQ0FBQ3NELFdBQVU7Ozs7Ozs7Ozs7OzBDQUUzQiw4REFBQ3pELHlEQUFNQTtnQ0FDTDBELFNBQVNwQyxPQUFPMkMsUUFBUSxDQUFDLFVBQVUsWUFBWTtnQ0FDL0NOLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBRytCLFVBQVUsR0FBRzVCLEdBQUc7Z0NBQ3REYyxXQUFVOzBDQUVWLDRFQUFDckQsd0xBQUlBO29DQUFDcUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3BCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxnQkFBZ0IsWUFBWTtnQ0FDckROLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBR2dDLGdCQUFnQixHQUFHN0IsR0FBRztnQ0FDNURjLFdBQVU7MENBRVYsNEVBQUNqRCx3TEFBSUE7b0NBQUNpRCxXQUFVOzs7Ozs7Ozs7OzswQ0FFbEIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxpQkFBaUIsWUFBWTtnQ0FDdEROLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBR2lDLGlCQUFpQixHQUFHOUIsR0FBRztnQ0FDN0RjLFdBQVU7MENBRVYsNEVBQUNoRCx3TEFBV0E7b0NBQUNnRCxXQUFVOzs7Ozs7Ozs7OzswQ0FFekIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFTcEMsT0FBTzJDLFFBQVEsQ0FBQyxnQkFBZ0IsWUFBWTtnQ0FDckROLE1BQUs7Z0NBQ0xDLFNBQVMsSUFBTXRDLE9BQU9pQixLQUFLLEdBQUdDLEtBQUssR0FBR2tDLGdCQUFnQixHQUFHL0IsR0FBRztnQ0FDNURjLFdBQVU7MENBRVYsNEVBQUMvQyx3TEFBS0E7b0NBQUMrQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLckIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3pELHlEQUFNQTtnQ0FDTDBELFNBQVE7Z0NBQ1JDLE1BQUs7Z0NBQ0xDLFNBQVNoQjtnQ0FDVGEsV0FBVTswQ0FFViw0RUFBQzlDLHdMQUFRQTtvQ0FBQzhDLFdBQVU7Ozs7Ozs7Ozs7OzBDQUV0Qiw4REFBQ3pELHlEQUFNQTtnQ0FDTDBELFNBQVE7Z0NBQ1JDLE1BQUs7Z0NBQ0xDLFNBQVN6QjtnQ0FDVHNCLFdBQVU7MENBRVYsNEVBQUM3Qyx3TEFBU0E7b0NBQUM2QyxXQUFVOzs7Ozs7Ozs7OzswQ0FFdkIsOERBQUN6RCx5REFBTUE7Z0NBQ0wwRCxTQUFRO2dDQUNSQyxNQUFLO2dDQUNMQyxTQUFTVDtnQ0FDVE0sV0FBVTswQ0FFViw0RUFBQzVDLHdMQUFTQTtvQ0FBQzRDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU0zQiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNsRSx5REFBYUE7b0JBQ1orQixRQUFRQTtvQkFDUm1DLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3BCO0dBek9nQnhDOztRQU1DM0IscURBQVNBOzs7S0FOVjJCIiwic291cmNlcyI6WyIvVXNlcnMvbGF5bmUvUHJvamVjdHMvYXdvcHJvamVjdHMvaWRlYWZsb3cvc3JjL2NvbXBvbmVudHMvUmljaFRleHRFZGl0b3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VFZGl0b3IsIEVkaXRvckNvbnRlbnQgfSBmcm9tICdAdGlwdGFwL3JlYWN0J1xuaW1wb3J0IFN0YXJ0ZXJLaXQgZnJvbSAnQHRpcHRhcC9zdGFydGVyLWtpdCdcbmltcG9ydCBQbGFjZWhvbGRlciBmcm9tICdAdGlwdGFwL2V4dGVuc2lvbi1wbGFjZWhvbGRlcidcbmltcG9ydCBMaW5rIGZyb20gJ0B0aXB0YXAvZXh0ZW5zaW9uLWxpbmsnXG5pbXBvcnQgSW1hZ2UgZnJvbSAnQHRpcHRhcC9leHRlbnNpb24taW1hZ2UnXG5pbXBvcnQgVGFibGUgZnJvbSAnQHRpcHRhcC9leHRlbnNpb24tdGFibGUnXG5pbXBvcnQgVGFibGVSb3cgZnJvbSAnQHRpcHRhcC9leHRlbnNpb24tdGFibGUtcm93J1xuaW1wb3J0IFRhYmxlSGVhZGVyIGZyb20gJ0B0aXB0YXAvZXh0ZW5zaW9uLXRhYmxlLWhlYWRlcidcbmltcG9ydCBUYWJsZUNlbGwgZnJvbSAnQHRpcHRhcC9leHRlbnNpb24tdGFibGUtY2VsbCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IFxuICBCb2xkLCBcbiAgSXRhbGljLCBcbiAgVW5kZXJsaW5lLFxuICBTdHJpa2V0aHJvdWdoLFxuICBDb2RlLFxuICBIZWFkaW5nMSxcbiAgSGVhZGluZzIsXG4gIEhlYWRpbmczLFxuICBMaXN0LFxuICBMaXN0T3JkZXJlZCxcbiAgUXVvdGUsXG4gIExpbmsgYXMgTGlua0ljb24sXG4gIEltYWdlIGFzIEltYWdlSWNvbixcbiAgVGFibGUgYXMgVGFibGVJY29uLFxuICBVbmRvLFxuICBSZWRvLFxuICBBbGlnbkxlZnQsXG4gIEFsaWduQ2VudGVyLFxuICBBbGlnblJpZ2h0XG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcblxuaW50ZXJmYWNlIFJpY2hUZXh0RWRpdG9yUHJvcHMge1xuICBjb250ZW50OiBzdHJpbmdcbiAgb25DaGFuZ2U6IChjb250ZW50OiBzdHJpbmcpID0+IHZvaWRcbiAgcGxhY2Vob2xkZXI/OiBzdHJpbmdcbiAgZWRpdGFibGU/OiBib29sZWFuXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBSaWNoVGV4dEVkaXRvcih7IFxuICBjb250ZW50LCBcbiAgb25DaGFuZ2UsIFxuICBwbGFjZWhvbGRlciA9IFwi5byA5aeL57yW5YaZ5paH5qGjLi4uXCIsXG4gIGVkaXRhYmxlID0gdHJ1ZSBcbn06IFJpY2hUZXh0RWRpdG9yUHJvcHMpIHtcbiAgY29uc3QgZWRpdG9yID0gdXNlRWRpdG9yKHtcbiAgICBleHRlbnNpb25zOiBbXG4gICAgICBTdGFydGVyS2l0LmNvbmZpZ3VyZSh7XG4gICAgICAgIGJ1bGxldExpc3Q6IHtcbiAgICAgICAgICBrZWVwTWFya3M6IHRydWUsXG4gICAgICAgICAga2VlcEF0dHJpYnV0ZXM6IGZhbHNlLFxuICAgICAgICB9LFxuICAgICAgICBvcmRlcmVkTGlzdDoge1xuICAgICAgICAgIGtlZXBNYXJrczogdHJ1ZSxcbiAgICAgICAgICBrZWVwQXR0cmlidXRlczogZmFsc2UsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbiAgICAgIFBsYWNlaG9sZGVyLmNvbmZpZ3VyZSh7XG4gICAgICAgIHBsYWNlaG9sZGVyLFxuICAgICAgfSksXG4gICAgICBMaW5rLmNvbmZpZ3VyZSh7XG4gICAgICAgIG9wZW5PbkNsaWNrOiBmYWxzZSxcbiAgICAgICAgSFRNTEF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICBjbGFzczogJ3RleHQtYmx1ZS02MDAgdW5kZXJsaW5lIGN1cnNvci1wb2ludGVyJyxcbiAgICAgICAgfSxcbiAgICAgIH0pLFxuICAgICAgSW1hZ2UuY29uZmlndXJlKHtcbiAgICAgICAgSFRNTEF0dHJpYnV0ZXM6IHtcbiAgICAgICAgICBjbGFzczogJ21heC13LWZ1bGwgaC1hdXRvIHJvdW5kZWQtbGcnLFxuICAgICAgICB9LFxuICAgICAgfSksXG4gICAgICBUYWJsZS5jb25maWd1cmUoe1xuICAgICAgICByZXNpemFibGU6IHRydWUsXG4gICAgICB9KSxcbiAgICAgIFRhYmxlUm93LFxuICAgICAgVGFibGVIZWFkZXIsXG4gICAgICBUYWJsZUNlbGwsXG4gICAgXSxcbiAgICBjb250ZW50LFxuICAgIGVkaXRhYmxlLFxuICAgIG9uVXBkYXRlOiAoeyBlZGl0b3IgfSkgPT4ge1xuICAgICAgb25DaGFuZ2UoZWRpdG9yLmdldEhUTUwoKSlcbiAgICB9LFxuICB9KVxuXG4gIGNvbnN0IGFkZEltYWdlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGNvbnN0IHVybCA9IHdpbmRvdy5wcm9tcHQoJ+ivt+i+k+WFpeWbvueJh1VSTDonKVxuICAgIGlmICh1cmwgJiYgZWRpdG9yKSB7XG4gICAgICBlZGl0b3IuY2hhaW4oKS5mb2N1cygpLnNldEltYWdlKHsgc3JjOiB1cmwgfSkucnVuKClcbiAgICB9XG4gIH0sIFtlZGl0b3JdKVxuXG4gIGNvbnN0IGFkZExpbmsgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY29uc3QgcHJldmlvdXNVcmwgPSBlZGl0b3I/LmdldEF0dHJpYnV0ZXMoJ2xpbmsnKS5ocmVmXG4gICAgY29uc3QgdXJsID0gd2luZG93LnByb21wdCgn6K+36L6T5YWl6ZO+5o6lVVJMOicsIHByZXZpb3VzVXJsKVxuICAgIFxuICAgIGlmICh1cmwgPT09IG51bGwpIHtcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmICh1cmwgPT09ICcnKSB7XG4gICAgICBlZGl0b3I/LmNoYWluKCkuZm9jdXMoKS5leHRlbmRNYXJrUmFuZ2UoJ2xpbmsnKS51bnNldExpbmsoKS5ydW4oKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgZWRpdG9yPy5jaGFpbigpLmZvY3VzKCkuZXh0ZW5kTWFya1JhbmdlKCdsaW5rJykuc2V0TGluayh7IGhyZWY6IHVybCB9KS5ydW4oKVxuICB9LCBbZWRpdG9yXSlcblxuICBjb25zdCBhZGRUYWJsZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBlZGl0b3I/LmNoYWluKCkuZm9jdXMoKS5pbnNlcnRUYWJsZSh7IHJvd3M6IDMsIGNvbHM6IDMsIHdpdGhIZWFkZXJSb3c6IHRydWUgfSkucnVuKClcbiAgfSwgW2VkaXRvcl0pXG5cbiAgaWYgKCFlZGl0b3IpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImgtZnVsbCBmbGV4IGZsZXgtY29sIGJnLXdoaXRlXCI+XG4gICAgICB7Lyog5bel5YW35qCPICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItYiBwLTIgZmxleCBmbGV4LXdyYXAgZ2FwLTEgYmctZ3JheS01MCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgIHsvKiDmkqTplIDph43lgZogKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMSBib3JkZXItciBwci0yIG1yLTJcIj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkudW5kbygpLnJ1bigpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0b3IuY2FuKCkuY2hhaW4oKS5mb2N1cygpLnVuZG8oKS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VW5kbyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkucmVkbygpLnJ1bigpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9eyFlZGl0b3IuY2FuKCkuY2hhaW4oKS5mb2N1cygpLnJlZG8oKS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8UmVkbyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOagh+mimCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0xIGJvcmRlci1yIHByLTIgbXItMlwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9e2VkaXRvci5pc0FjdGl2ZSgnaGVhZGluZycsIHsgbGV2ZWw6IDEgfSkgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkudG9nZ2xlSGVhZGluZyh7IGxldmVsOiAxIH0pLnJ1bigpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxIZWFkaW5nMSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PXtlZGl0b3IuaXNBY3RpdmUoJ2hlYWRpbmcnLCB7IGxldmVsOiAyIH0pID8gJ2RlZmF1bHQnIDogJ2dob3N0J31cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBlZGl0b3IuY2hhaW4oKS5mb2N1cygpLnRvZ2dsZUhlYWRpbmcoeyBsZXZlbDogMiB9KS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8SGVhZGluZzIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD17ZWRpdG9yLmlzQWN0aXZlKCdoZWFkaW5nJywgeyBsZXZlbDogMyB9KSA/ICdkZWZhdWx0JyA6ICdnaG9zdCd9XG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZWRpdG9yLmNoYWluKCkuZm9jdXMoKS50b2dnbGVIZWFkaW5nKHsgbGV2ZWw6IDMgfSkucnVuKCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdy04IHAtMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEhlYWRpbmczIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog5paH5pys5qC85byPICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTEgYm9yZGVyLXIgcHItMiBtci0yXCI+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD17ZWRpdG9yLmlzQWN0aXZlKCdib2xkJykgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkudG9nZ2xlQm9sZCgpLnJ1bigpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxCb2xkIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9e2VkaXRvci5pc0FjdGl2ZSgnaXRhbGljJykgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkudG9nZ2xlSXRhbGljKCkucnVuKCl9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdy04IHAtMFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEl0YWxpYyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PXtlZGl0b3IuaXNBY3RpdmUoJ3N0cmlrZScpID8gJ2RlZmF1bHQnIDogJ2dob3N0J31cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBlZGl0b3IuY2hhaW4oKS5mb2N1cygpLnRvZ2dsZVN0cmlrZSgpLnJ1bigpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTdHJpa2V0aHJvdWdoIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9e2VkaXRvci5pc0FjdGl2ZSgnY29kZScpID8gJ2RlZmF1bHQnIDogJ2dob3N0J31cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBlZGl0b3IuY2hhaW4oKS5mb2N1cygpLnRvZ2dsZUNvZGUoKS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8Q29kZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOWIl+ihqCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0xIGJvcmRlci1yIHByLTIgbXItMlwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9e2VkaXRvci5pc0FjdGl2ZSgnYnVsbGV0TGlzdCcpID8gJ2RlZmF1bHQnIDogJ2dob3N0J31cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBlZGl0b3IuY2hhaW4oKS5mb2N1cygpLnRvZ2dsZUJ1bGxldExpc3QoKS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8TGlzdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PXtlZGl0b3IuaXNBY3RpdmUoJ29yZGVyZWRMaXN0JykgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkudG9nZ2xlT3JkZXJlZExpc3QoKS5ydW4oKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8TGlzdE9yZGVyZWQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD17ZWRpdG9yLmlzQWN0aXZlKCdibG9ja3F1b3RlJykgPyAnZGVmYXVsdCcgOiAnZ2hvc3QnfVxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRvci5jaGFpbigpLmZvY3VzKCkudG9nZ2xlQmxvY2txdW90ZSgpLnJ1bigpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxRdW90ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOaPkuWFpSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0xXCI+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXthZGRMaW5rfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxMaW5rSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2FkZEltYWdlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxJbWFnZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXthZGRUYWJsZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtOCB3LTggcC0wXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VGFibGVJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog57yW6L6R5Zmo5YaF5a65ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1bNDAwcHhdIG1heC1oLVs2MDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIDxFZGl0b3JDb250ZW50IFxuICAgICAgICAgIGVkaXRvcj17ZWRpdG9yfSBcbiAgICAgICAgICBjbGFzc05hbWU9XCJwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lIHAtNCBmb2N1czpvdXRsaW5lLW5vbmVcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZGl0b3IiLCJFZGl0b3JDb250ZW50IiwiU3RhcnRlcktpdCIsIlBsYWNlaG9sZGVyIiwiTGluayIsIkltYWdlIiwiVGFibGUiLCJUYWJsZVJvdyIsIlRhYmxlSGVhZGVyIiwiVGFibGVDZWxsIiwiQnV0dG9uIiwiQm9sZCIsIkl0YWxpYyIsIlN0cmlrZXRocm91Z2giLCJDb2RlIiwiSGVhZGluZzEiLCJIZWFkaW5nMiIsIkhlYWRpbmczIiwiTGlzdCIsIkxpc3RPcmRlcmVkIiwiUXVvdGUiLCJMaW5rSWNvbiIsIkltYWdlSWNvbiIsIlRhYmxlSWNvbiIsIlVuZG8iLCJSZWRvIiwidXNlQ2FsbGJhY2siLCJSaWNoVGV4dEVkaXRvciIsImNvbnRlbnQiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwiZWRpdGFibGUiLCJlZGl0b3IiLCJleHRlbnNpb25zIiwiY29uZmlndXJlIiwiYnVsbGV0TGlzdCIsImtlZXBNYXJrcyIsImtlZXBBdHRyaWJ1dGVzIiwib3JkZXJlZExpc3QiLCJvcGVuT25DbGljayIsIkhUTUxBdHRyaWJ1dGVzIiwiY2xhc3MiLCJyZXNpemFibGUiLCJvblVwZGF0ZSIsImdldEhUTUwiLCJhZGRJbWFnZSIsInVybCIsIndpbmRvdyIsInByb21wdCIsImNoYWluIiwiZm9jdXMiLCJzZXRJbWFnZSIsInNyYyIsInJ1biIsImFkZExpbmsiLCJwcmV2aW91c1VybCIsImdldEF0dHJpYnV0ZXMiLCJocmVmIiwiZXh0ZW5kTWFya1JhbmdlIiwidW5zZXRMaW5rIiwic2V0TGluayIsImFkZFRhYmxlIiwiaW5zZXJ0VGFibGUiLCJyb3dzIiwiY29scyIsIndpdGhIZWFkZXJSb3ciLCJkaXYiLCJjbGFzc05hbWUiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJ1bmRvIiwiZGlzYWJsZWQiLCJjYW4iLCJyZWRvIiwiaXNBY3RpdmUiLCJsZXZlbCIsInRvZ2dsZUhlYWRpbmciLCJ0b2dnbGVCb2xkIiwidG9nZ2xlSXRhbGljIiwidG9nZ2xlU3RyaWtlIiwidG9nZ2xlQ29kZSIsInRvZ2dsZUJ1bGxldExpc3QiLCJ0b2dnbGVPcmRlcmVkTGlzdCIsInRvZ2dsZUJsb2NrcXVvdGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RichTextEditor.tsx\n"));

/***/ })

});