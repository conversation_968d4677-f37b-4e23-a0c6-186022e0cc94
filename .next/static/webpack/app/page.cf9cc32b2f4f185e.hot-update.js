"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 简单的 Markdown 到 HTML 转换函数（备用）\nfunction simpleMarkdownToHtml(markdown) {\n    return markdown.replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^\\* (.*$)/gim, '<li>$1</li>').replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/\\n/g, '<br>');\n}\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                console.log('File content:', fileContent);\n                // 检测内容格式并转换为 HTML\n                if ((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.isMarkdown)(fileContent)) {\n                    try {\n                        const htmlResult = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.markdownToHtml)(fileContent);\n                        console.log('Converted HTML:', htmlResult);\n                        setHtmlContent(htmlResult);\n                    } catch (error) {\n                        console.error('Markdown conversion failed, using simple converter:', error);\n                        // 使用简单转换器作为备用\n                        const simpleHtml = simpleMarkdownToHtml(fileContent);\n                        setHtmlContent(simpleHtml);\n                    }\n                } else {\n                    // 如果不是 Markdown，直接使用内容\n                    setHtmlContent(fileContent);\n                }\n                // 重置更改状态\n                setHasChanges(false);\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            // 将 HTML 内容转换为 Markdown 保存\n            const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n            updateFile(file.id, contentToSave);\n            setHasChanges(false);\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                    content: htmlContent,\n                    onChange: handleContentChange,\n                    placeholder: \"开始编写文档...\",\n                    editable: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});