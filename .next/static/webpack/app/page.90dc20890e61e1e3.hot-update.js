"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 简单的 Markdown 到 HTML 转换函数（备用）\nfunction simpleMarkdownToHtml(markdown) {\n    return markdown.replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^\\* (.*$)/gim, '<li>$1</li>').replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/\\n/g, '<br>');\n}\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                console.log('File content:', fileContent);\n                // 暂时使用简单的 Markdown 转换，确保内容能显示\n                if (fileContent.includes('#') || fileContent.includes('*') || fileContent.includes('-')) {\n                    // 简单的 Markdown 转换\n                    const simpleHtml = simpleMarkdownToHtml(fileContent);\n                    console.log('Simple HTML:', simpleHtml);\n                    setHtmlContent(simpleHtml);\n                } else {\n                    // 如果不是 Markdown，直接使用内容\n                    setHtmlContent(fileContent);\n                }\n                // 重置更改状态\n                setHasChanges(false);\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            try {\n                // 将 HTML 内容转换为 Markdown 保存\n                const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n                updateFile(file.id, contentToSave);\n                setHasChanges(false);\n                console.log('Content saved successfully');\n            } catch (error) {\n                console.error('Save failed:', error);\n                // 如果转换失败，直接保存 HTML 内容\n                updateFile(file.id, htmlContent);\n                setHasChanges(false);\n            }\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                    content: htmlContent,\n                    onChange: handleContentChange,\n                    placeholder: \"开始编写文档...\",\n                    editable: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});