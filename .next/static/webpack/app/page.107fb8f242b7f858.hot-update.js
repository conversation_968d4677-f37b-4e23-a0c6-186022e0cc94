"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 简单的 Markdown 到 HTML 转换函数（备用）\nfunction simpleMarkdownToHtml(markdown) {\n    return markdown.replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^\\* (.*$)/gim, '<li>$1</li>').replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/\\n/g, '<br>');\n}\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                console.log('File content:', fileContent);\n                // 检测内容格式并转换为 HTML\n                if ((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.isMarkdown)(fileContent)) {\n                    try {\n                        const htmlResult = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.markdownToHtml)(fileContent);\n                        console.log('Converted HTML:', htmlResult);\n                        setHtmlContent(htmlResult);\n                    } catch (error) {\n                        console.error('Markdown conversion failed, using simple converter:', error);\n                        // 使用简单转换器作为备用\n                        const simpleHtml = simpleMarkdownToHtml(fileContent);\n                        setHtmlContent(simpleHtml);\n                    }\n                } else {\n                    // 如果不是 Markdown，直接使用内容\n                    setHtmlContent(fileContent);\n                }\n                // 重置更改状态\n                setHasChanges(false);\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            try {\n                // 将 HTML 内容转换为 Markdown 保存\n                const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n                updateFile(file.id, contentToSave);\n                setHasChanges(false);\n                console.log('Content saved successfully');\n            } catch (error) {\n                console.error('Save failed:', error);\n                // 如果转换失败，直接保存 HTML 内容\n                updateFile(file.id, htmlContent);\n                setHasChanges(false);\n            }\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                    content: htmlContent,\n                    onChange: handleContentChange,\n                    placeholder: \"开始编写文档...\",\n                    editable: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});