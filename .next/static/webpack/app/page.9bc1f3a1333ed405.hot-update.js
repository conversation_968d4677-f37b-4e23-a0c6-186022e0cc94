"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 简单的 Markdown 到 HTML 转换函数（备用）\nfunction simpleMarkdownToHtml(markdown) {\n    let html = markdown// 标题\n    .replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^### (.*$)/gim, '<h3>$1</h3>')// 粗体和斜体\n    .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>')// 列表项\n    .replace(/^- (.*$)/gim, '<li>$1</li>').replace(/^\\* (.*$)/gim, '<li>$1</li>').replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>')// 段落\n    .replace(/\\n\\n/g, '</p><p>').replace(/\\n/g, '<br>');\n    // 包装在段落标签中\n    if (!html.startsWith('<h') && !html.startsWith('<li')) {\n        html = '<p>' + html + '</p>';\n    }\n    return html;\n}\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file && !isInitialized) {\n                const fileContent = file.content;\n                console.log('File content:', fileContent);\n                // 暂时使用简单的 Markdown 转换，确保内容能显示\n                if (fileContent.includes('#') || fileContent.includes('*') || fileContent.includes('-')) {\n                    // 简单的 Markdown 转换\n                    const simpleHtml = simpleMarkdownToHtml(fileContent);\n                    console.log('Simple HTML:', simpleHtml);\n                    setHtmlContent(simpleHtml);\n                } else {\n                    // 如果不是 Markdown，直接使用内容\n                    setHtmlContent(fileContent);\n                }\n                // 重置更改状态并标记为已初始化\n                setHasChanges(false);\n                setIsInitialized(true);\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file,\n        isInitialized\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            try {\n                // 将 HTML 内容转换为 Markdown 保存\n                const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n                updateFile(file.id, contentToSave);\n                setHasChanges(false);\n                console.log('Content saved successfully');\n            } catch (error) {\n                console.error('Save failed:', error);\n                // 如果转换失败，直接保存 HTML 内容\n                updateFile(file.id, htmlContent);\n                setHasChanges(false);\n            }\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                    content: htmlContent,\n                    onChange: handleContentChange,\n                    placeholder: \"开始编写文档...\",\n                    editable: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"aryhiv2V1jZan6vSPDB2o6fD5qo=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});