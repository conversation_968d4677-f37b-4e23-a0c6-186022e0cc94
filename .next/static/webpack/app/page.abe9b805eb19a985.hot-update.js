"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 简单的 Markdown 到 HTML 转换函数（备用）\nfunction simpleMarkdownToHtml(markdown) {\n    return markdown.replace(/^# (.*$)/gim, '<h1>$1</h1>').replace(/^## (.*$)/gim, '<h2>$1</h2>').replace(/^### (.*$)/gim, '<h3>$1</h3>').replace(/^\\* (.*$)/gim, '<li>$1</li>').replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>').replace(/\\*(.*?)\\*/g, '<em>$1</em>').replace(/\\n/g, '<br>');\n}\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                console.log('File content:', fileContent);\n                // 暂时使用简单的 Markdown 转换，确保内容能显示\n                if (fileContent.includes('#') || fileContent.includes('*') || fileContent.includes('-')) {\n                    // 简单的 Markdown 转换\n                    const simpleHtml = simpleMarkdownToHtml(fileContent);\n                    console.log('Simple HTML:', simpleHtml);\n                    setHtmlContent(simpleHtml);\n                } else {\n                    // 如果不是 Markdown，直接使用内容\n                    setHtmlContent(fileContent);\n                }\n                // 重置更改状态\n                setHasChanges(false);\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            try {\n                // 将 HTML 内容转换为 Markdown 保存\n                const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n                updateFile(file.id, contentToSave);\n                setHasChanges(false);\n                console.log('Content saved successfully');\n            } catch (error) {\n                console.error('Save failed:', error);\n                // 如果转换失败，直接保存 HTML 内容\n                updateFile(file.id, htmlContent);\n                setHasChanges(false);\n            }\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                    content: htmlContent,\n                    onChange: handleContentChange,\n                    placeholder: \"开始编写文档...\",\n                    editable: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});