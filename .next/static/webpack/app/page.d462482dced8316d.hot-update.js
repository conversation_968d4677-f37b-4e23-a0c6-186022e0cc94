"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/RichTextEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/RichTextEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RichTextEditor: () => (/* binding */ RichTextEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tiptap/react */ \"(app-pages-browser)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(app-pages-browser)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/extension-placeholder */ \"(app-pages-browser)/./node_modules/@tiptap/extension-placeholder/dist/index.js\");\n/* harmony import */ var _tiptap_extension_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-link */ \"(app-pages-browser)/./node_modules/@tiptap/extension-link/dist/index.js\");\n/* harmony import */ var _tiptap_extension_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tiptap/extension-image */ \"(app-pages-browser)/./node_modules/@tiptap/extension-image/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tiptap/extension-table */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table_row__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tiptap/extension-table-row */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table-row/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table_header__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tiptap/extension-table-header */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table-header/dist/index.js\");\n/* harmony import */ var _tiptap_extension_table_cell__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tiptap/extension-table-cell */ \"(app-pages-browser)/./node_modules/@tiptap/extension-table-cell/dist/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/redo.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heading-1.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heading-2.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heading-3.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bold.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/italic.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/strikethrough.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-ordered.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Bold,Code,Heading1,Heading2,Heading3,Image,Italic,Link,List,ListOrdered,Quote,Redo,Strikethrough,Table,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ RichTextEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RichTextEditor(param) {\n    let { content, onChange, placeholder = \"开始编写文档...\", editable = true } = param;\n    _s();\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_11__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n                bulletList: {\n                    keepMarks: true,\n                    keepAttributes: false\n                },\n                orderedList: {\n                    keepMarks: true,\n                    keepAttributes: false\n                }\n            }),\n            _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_2__[\"default\"].configure({\n                placeholder\n            }),\n            _tiptap_extension_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"].configure({\n                openOnClick: false,\n                HTMLAttributes: {\n                    class: 'text-blue-600 underline cursor-pointer'\n                }\n            }),\n            _tiptap_extension_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"].configure({\n                HTMLAttributes: {\n                    class: 'max-w-full h-auto rounded-lg'\n                }\n            }),\n            _tiptap_extension_table__WEBPACK_IMPORTED_MODULE_5__[\"default\"].configure({\n                resizable: true\n            }),\n            _tiptap_extension_table_row__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            _tiptap_extension_table_header__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            _tiptap_extension_table_cell__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        ],\n        content: content || '',\n        editable,\n        onUpdate: {\n            \"RichTextEditor.useEditor[editor]\": (param)=>{\n                let { editor } = param;\n                onChange(editor.getHTML());\n            }\n        }[\"RichTextEditor.useEditor[editor]\"]\n    });\n    // 当内容变化时更新编辑器\n    (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)({\n        \"RichTextEditor.useEffect\": ()=>{\n            if (editor && content !== editor.getHTML()) {\n                editor.commands.setContent(content || '', false);\n            }\n        }\n    }[\"RichTextEditor.useEffect\"], [\n        editor,\n        content\n    ]);\n    const addImage = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)({\n        \"RichTextEditor.useCallback[addImage]\": ()=>{\n            const url = window.prompt('请输入图片URL:');\n            if (url && editor) {\n                editor.chain().focus().setImage({\n                    src: url\n                }).run();\n            }\n        }\n    }[\"RichTextEditor.useCallback[addImage]\"], [\n        editor\n    ]);\n    const addLink = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)({\n        \"RichTextEditor.useCallback[addLink]\": ()=>{\n            const previousUrl = editor === null || editor === void 0 ? void 0 : editor.getAttributes('link').href;\n            const url = window.prompt('请输入链接URL:', previousUrl);\n            if (url === null) {\n                return;\n            }\n            if (url === '') {\n                editor === null || editor === void 0 ? void 0 : editor.chain().focus().extendMarkRange('link').unsetLink().run();\n                return;\n            }\n            editor === null || editor === void 0 ? void 0 : editor.chain().focus().extendMarkRange('link').setLink({\n                href: url\n            }).run();\n        }\n    }[\"RichTextEditor.useCallback[addLink]\"], [\n        editor\n    ]);\n    const addTable = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)({\n        \"RichTextEditor.useCallback[addTable]\": ()=>{\n            editor === null || editor === void 0 ? void 0 : editor.chain().focus().insertTable({\n                rows: 3,\n                cols: 3,\n                withHeaderRow: true\n            }).run();\n        }\n    }[\"RichTextEditor.useCallback[addTable]\"], [\n        editor\n    ]);\n    if (!editor) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b p-2 flex flex-wrap gap-1 bg-gray-50 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().undo().run(),\n                                disabled: !editor.can().chain().focus().undo().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().redo().run(),\n                                disabled: !editor.can().chain().focus().redo().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('heading', {\n                                    level: 1\n                                }) ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 1\n                                    }).run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('heading', {\n                                    level: 2\n                                }) ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 2\n                                    }).run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('heading', {\n                                    level: 3\n                                }) ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 3\n                                    }).run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('bold') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleBold().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('italic') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleItalic().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('strike') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleStrike().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('code') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleCode().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1 border-r pr-2 mr-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('bulletList') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleBulletList().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('orderedList') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleOrderedList().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: editor.isActive('blockquote') ? 'default' : 'ghost',\n                                size: \"sm\",\n                                onClick: ()=>editor.chain().focus().toggleBlockquote().run(),\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: addLink,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: addImage,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: addTable,\n                                className: \"h-8 w-8 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bold_Code_Heading1_Heading2_Heading3_Image_Italic_Link_List_ListOrdered_Quote_Redo_Strikethrough_Table_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tiptap_react__WEBPACK_IMPORTED_MODULE_11__.EditorContent, {\n                    editor: editor,\n                    className: \"prose prose-sm max-w-none h-full focus:outline-none\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(RichTextEditor, \"04f9IZI6/0V0tYwMkglF3YsCZT4=\", false, function() {\n    return [\n        _tiptap_react__WEBPACK_IMPORTED_MODULE_11__.useEditor\n    ];\n});\n_c = RichTextEditor;\nvar _c;\n$RefreshReg$(_c, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RichTextEditor.tsx\n"));

/***/ })

});