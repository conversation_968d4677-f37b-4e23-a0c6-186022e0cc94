"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                console.log('File content:', fileContent);\n                // 检测内容格式并转换为 HTML\n                if ((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.isMarkdown)(fileContent)) {\n                    const htmlResult = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.markdownToHtml)(fileContent);\n                    console.log('Converted HTML:', htmlResult);\n                    setHtmlContent(htmlResult);\n                } else {\n                    // 如果不是 Markdown，直接使用内容\n                    setHtmlContent(fileContent);\n                }\n                // 重置更改状态\n                setHasChanges(false);\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            // 将 HTML 内容转换为 Markdown 保存\n            const contentToSave = (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent);\n            updateFile(file.id, contentToSave);\n            setHasChanges(false);\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        setHtmlContent(newContent);\n        setHasChanges(true);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            size: \"sm\",\n                            onClick: handleSave,\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                \"保存\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                    content: htmlContent,\n                    onChange: handleContentChange,\n                    placeholder: \"开始编写文档...\",\n                    editable: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0RvY3VtZW50RWRpdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFMkM7QUFDSTtBQUNEO0FBQ0w7QUFDUTtBQUNnQztBQU8xRSxTQUFTVSxlQUFlLEtBQXVDO1FBQXZDLEVBQUVDLE1BQU0sRUFBRUMsTUFBTSxFQUF1QixHQUF2Qzs7SUFDN0IsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLFVBQVUsRUFBRSxHQUFHVCx1REFBV0E7SUFDekMsTUFBTSxDQUFDVSxhQUFhQyxlQUFlLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpQixZQUFZQyxjQUFjLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUU3QyxNQUFNbUIsT0FBT04sTUFBTU8sSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtYO0lBRXRDVixnREFBU0E7b0NBQUM7WUFDUixJQUFJa0IsTUFBTTtnQkFDUixNQUFNSSxjQUFjSixLQUFLSyxPQUFPO2dCQUNoQ0MsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkg7Z0JBRTdCLGtCQUFrQjtnQkFDbEIsSUFBSWQsK0RBQVVBLENBQUNjLGNBQWM7b0JBQzNCLE1BQU1JLGFBQWFwQixtRUFBY0EsQ0FBQ2dCO29CQUNsQ0UsUUFBUUMsR0FBRyxDQUFDLG1CQUFtQkM7b0JBQy9CWCxlQUFlVztnQkFDakIsT0FBTztvQkFDTCx1QkFBdUI7b0JBQ3ZCWCxlQUFlTztnQkFDakI7Z0JBRUEsU0FBUztnQkFDVEwsY0FBYztZQUNoQjtRQUNGO21DQUFHO1FBQUNDO0tBQUs7SUFFVCxNQUFNUyxhQUFhO1FBQ2pCLElBQUlULFFBQVFGLFlBQVk7WUFDdEIsMkJBQTJCO1lBQzNCLE1BQU1ZLGdCQUFnQnJCLG1FQUFjQSxDQUFDTztZQUNyQ0QsV0FBV0ssS0FBS0csRUFBRSxFQUFFTztZQUNwQlgsY0FBYztRQUNoQjtJQUNGO0lBRUEsTUFBTVksc0JBQXNCLENBQUNDO1FBQzNCZixlQUFlZTtRQUNmYixjQUFjO0lBQ2hCO0lBRUEsSUFBSSxDQUFDQyxNQUFNO1FBQ1QscUJBQ0UsOERBQUNhO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUFnQjs7Ozs7Ozs7Ozs7SUFHckM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDL0IseURBQU1BO2dDQUNMZ0MsU0FBUTtnQ0FDUkMsTUFBSztnQ0FDTEMsU0FBU3hCO2dDQUNUcUIsV0FBVTswQ0FFViw0RUFBQzlCLDBGQUFTQTtvQ0FBQzhCLFdBQVU7Ozs7Ozs7Ozs7OzBDQUV2Qiw4REFBQ0Q7O2tEQUNDLDhEQUFDSzt3Q0FBR0osV0FBVTtrREFBdUJkLEtBQUttQixJQUFJOzs7Ozs7a0RBQzlDLDhEQUFDQzt3Q0FBRU4sV0FBVTs7NENBQXdCOzRDQUM1QmQsS0FBS3FCLFlBQVksQ0FBQ0MsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLN0MsOERBQUNUO3dCQUFJQyxXQUFVO2tDQUNaaEIsNEJBQ0MsOERBQUNmLHlEQUFNQTs0QkFDTGlDLE1BQUs7NEJBQ0xDLFNBQVNSOzRCQUNUSyxXQUFVOzs4Q0FFViw4REFBQzdCLDBGQUFJQTtvQ0FBQzZCLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRekMsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDM0IsMkRBQWNBO29CQUNia0IsU0FBU1Q7b0JBQ1QyQixVQUFVWjtvQkFDVmEsYUFBWTtvQkFDWkMsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLcEI7R0EvRmdCbEM7O1FBQ2dCTCxtREFBV0E7OztLQUQzQksiLCJzb3VyY2VzIjpbIi9Vc2Vycy9sYXluZS9Qcm9qZWN0cy9hd29wcm9qZWN0cy9pZGVhZmxvdy9zcmMvY29tcG9uZW50cy9Eb2N1bWVudEVkaXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IEFycm93TGVmdCwgU2F2ZSB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgdXNlQXBwU3RvcmUgfSBmcm9tIFwiQC9saWIvc3RvcmVcIlxuaW1wb3J0IHsgUmljaFRleHRFZGl0b3IgfSBmcm9tIFwiLi9SaWNoVGV4dEVkaXRvclwiXG5pbXBvcnQgeyBtYXJrZG93blRvSHRtbCwgaHRtbFRvTWFya2Rvd24sIGlzTWFya2Rvd24gfSBmcm9tIFwiQC9saWIvbWFya2Rvd24tdXRpbHNcIlxuXG5pbnRlcmZhY2UgRG9jdW1lbnRFZGl0b3JQcm9wcyB7XG4gIGZpbGVJZDogc3RyaW5nXG4gIG9uQmFjazogKCkgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gRG9jdW1lbnRFZGl0b3IoeyBmaWxlSWQsIG9uQmFjayB9OiBEb2N1bWVudEVkaXRvclByb3BzKSB7XG4gIGNvbnN0IHsgZmlsZXMsIHVwZGF0ZUZpbGUgfSA9IHVzZUFwcFN0b3JlKClcbiAgY29uc3QgW2h0bWxDb250ZW50LCBzZXRIdG1sQ29udGVudF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2hhc0NoYW5nZXMsIHNldEhhc0NoYW5nZXNdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgZmlsZSA9IGZpbGVzLmZpbmQoZiA9PiBmLmlkID09PSBmaWxlSWQpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZmlsZSkge1xuICAgICAgY29uc3QgZmlsZUNvbnRlbnQgPSBmaWxlLmNvbnRlbnRcbiAgICAgIGNvbnNvbGUubG9nKCdGaWxlIGNvbnRlbnQ6JywgZmlsZUNvbnRlbnQpXG5cbiAgICAgIC8vIOajgOa1i+WGheWuueagvOW8j+W5tui9rOaNouS4uiBIVE1MXG4gICAgICBpZiAoaXNNYXJrZG93bihmaWxlQ29udGVudCkpIHtcbiAgICAgICAgY29uc3QgaHRtbFJlc3VsdCA9IG1hcmtkb3duVG9IdG1sKGZpbGVDb250ZW50KVxuICAgICAgICBjb25zb2xlLmxvZygnQ29udmVydGVkIEhUTUw6JywgaHRtbFJlc3VsdClcbiAgICAgICAgc2V0SHRtbENvbnRlbnQoaHRtbFJlc3VsdClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIOWmguaenOS4jeaYryBNYXJrZG93bu+8jOebtOaOpeS9v+eUqOWGheWuuVxuICAgICAgICBzZXRIdG1sQ29udGVudChmaWxlQ29udGVudClcbiAgICAgIH1cblxuICAgICAgLy8g6YeN572u5pu05pS554q25oCBXG4gICAgICBzZXRIYXNDaGFuZ2VzKGZhbHNlKVxuICAgIH1cbiAgfSwgW2ZpbGVdKVxuXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSAoKSA9PiB7XG4gICAgaWYgKGZpbGUgJiYgaGFzQ2hhbmdlcykge1xuICAgICAgLy8g5bCGIEhUTUwg5YaF5a656L2s5o2i5Li6IE1hcmtkb3duIOS/neWtmFxuICAgICAgY29uc3QgY29udGVudFRvU2F2ZSA9IGh0bWxUb01hcmtkb3duKGh0bWxDb250ZW50KVxuICAgICAgdXBkYXRlRmlsZShmaWxlLmlkLCBjb250ZW50VG9TYXZlKVxuICAgICAgc2V0SGFzQ2hhbmdlcyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVDb250ZW50Q2hhbmdlID0gKG5ld0NvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgIHNldEh0bWxDb250ZW50KG5ld0NvbnRlbnQpXG4gICAgc2V0SGFzQ2hhbmdlcyh0cnVlKVxuICB9XG5cbiAgaWYgKCFmaWxlKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPuaWh+S7tuacquaJvuWIsDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XG4gICAgICB7Lyog5aS06YOo5bel5YW35qCPICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlci1iXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXtvbkJhY2t9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJwLTJcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e2ZpbGUubmFtZX08L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIOacgOWQjuS/ruaUuToge2ZpbGUubGFzdE1vZGlmaWVkLnRvTG9jYWxlU3RyaW5nKCl9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAge2hhc0NoYW5nZXMgJiYgKFxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgIOS/neWtmFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWGheWuueWMuuWfnyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8UmljaFRleHRFZGl0b3JcbiAgICAgICAgICBjb250ZW50PXtodG1sQ29udGVudH1cbiAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ29udGVudENoYW5nZX1cbiAgICAgICAgICBwbGFjZWhvbGRlcj1cIuW8gOWni+e8luWGmeaWh+ahoy4uLlwiXG4gICAgICAgICAgZWRpdGFibGU9e3RydWV9XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQXJyb3dMZWZ0IiwiU2F2ZSIsInVzZUFwcFN0b3JlIiwiUmljaFRleHRFZGl0b3IiLCJtYXJrZG93blRvSHRtbCIsImh0bWxUb01hcmtkb3duIiwiaXNNYXJrZG93biIsIkRvY3VtZW50RWRpdG9yIiwiZmlsZUlkIiwib25CYWNrIiwiZmlsZXMiLCJ1cGRhdGVGaWxlIiwiaHRtbENvbnRlbnQiLCJzZXRIdG1sQ29udGVudCIsImhhc0NoYW5nZXMiLCJzZXRIYXNDaGFuZ2VzIiwiZmlsZSIsImZpbmQiLCJmIiwiaWQiLCJmaWxlQ29udGVudCIsImNvbnRlbnQiLCJjb25zb2xlIiwibG9nIiwiaHRtbFJlc3VsdCIsImhhbmRsZVNhdmUiLCJjb250ZW50VG9TYXZlIiwiaGFuZGxlQ29udGVudENoYW5nZSIsIm5ld0NvbnRlbnQiLCJkaXYiLCJjbGFzc05hbWUiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJoMyIsIm5hbWUiLCJwIiwibGFzdE1vZGlmaWVkIiwidG9Mb2NhbGVTdHJpbmciLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwiZWRpdGFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});