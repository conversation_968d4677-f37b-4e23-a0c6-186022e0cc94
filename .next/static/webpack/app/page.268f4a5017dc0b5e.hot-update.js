"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AppPreview.tsx":
/*!***************************************!*\
  !*** ./src/components/AppPreview.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppPreview: () => (/* binding */ AppPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Download,FileSpreadsheet,MapPin,Package,Truck,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ AppPreview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AppPreview() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const steps = [\n        {\n            id: 1,\n            title: '数据上传',\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            id: 2,\n            title: '数据验证',\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: 3,\n            title: '智能调度',\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 4,\n            title: '结果确认',\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        }\n    ];\n    const mockOrderData = [\n        {\n            id: 'ORD001',\n            customer: '客户A',\n            material: '钢材',\n            weight: '2.5吨',\n            date: '2024-01-15'\n        },\n        {\n            id: 'ORD002',\n            customer: '客户B',\n            material: '水泥',\n            weight: '3.2吨',\n            date: '2024-01-16'\n        },\n        {\n            id: 'ORD003',\n            customer: '客户C',\n            material: '砖块',\n            weight: '1.8吨',\n            date: '2024-01-17'\n        }\n    ];\n    const mockVehicleData = [\n        {\n            id: 'VEH001',\n            type: '大货车',\n            capacity: '5吨',\n            status: '可用'\n        },\n        {\n            id: 'VEH002',\n            type: '中货车',\n            capacity: '3吨',\n            status: '可用'\n        },\n        {\n            id: 'VEH003',\n            type: '小货车',\n            capacity: '2吨',\n            status: '维修中'\n        }\n    ];\n    const mockScheduleResult = [\n        {\n            vehicle: 'VEH001',\n            orders: [\n                'ORD001',\n                'ORD003'\n            ],\n            route: '客户A → 客户C',\n            date: '2024-01-15'\n        },\n        {\n            vehicle: 'VEH002',\n            orders: [\n                'ORD002'\n            ],\n            route: '客户B',\n            date: '2024-01-16'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center w-fit\",\n                        children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 w-fit \".concat(currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(currentStep >= step.id ? 'bg-blue-600 text-white' : 'bg-gray-200'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-sm whitespace-nowrap\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this),\n                                    index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-px mx-4 \".concat(currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300')\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, step.id, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6\",\n                children: [\n                    currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"数据上传\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"请上传订单明细、物料信息和车辆信息文件\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    {\n                                        name: '订单明细.xlsx',\n                                        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                        uploaded: true\n                                    },\n                                    {\n                                        name: '物料信息.xlsx',\n                                        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                        uploaded: true\n                                    },\n                                    {\n                                        name: '车辆信息.xlsx',\n                                        icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                        uploaded: false\n                                    }\n                                ].map((file, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-2 border-dashed rounded-lg p-6 text-center \".concat(file.uploaded ? 'border-green-300 bg-green-50' : 'border-gray-300'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(file.icon, {\n                                                className: \"h-12 w-12 mx-auto mb-3 \".concat(file.uploaded ? 'text-green-600' : 'text-gray-400')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: file.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this),\n                                            file.uploaded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600 text-sm mt-2\",\n                                                children: \"✓ 已上传\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"mt-3\",\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"上传文件\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setCurrentStep(2),\n                                    children: \"下一步：数据验证\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"数据验证\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"系统正在验证上传的数据格式和完整性\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                defaultValue: \"orders\",\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"orders\",\n                                                children: \"订单数据\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                value: \"vehicles\",\n                                                children: \"车辆数据\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                        value: \"orders\",\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-green-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"订单数据验证通过\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-green-700 mt-1\",\n                                                        children: [\n                                                            \"共 \",\n                                                            mockOrderData.length,\n                                                            \" 条订单记录，格式正确\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"订单号\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"客户\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"物料\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 142,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"重量\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 143,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"交货日期\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: mockOrderData.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-t\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: order.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 150,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: order.customer\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 151,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: order.material\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 152,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: order.weight\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 153,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: order.date\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 154,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, order.id, true, {\n                                                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                        value: \"vehicles\",\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-yellow-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"车辆数据需要注意\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-700 mt-1\",\n                                                        children: \"1 辆车辆状态为维修中，不参与调度\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            className: \"bg-gray-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"车辆编号\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 177,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"类型\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"载重\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                                        children: \"状态\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: mockVehicleData.map((vehicle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-t\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: vehicle.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: vehicle.type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 187,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: vehicle.capacity\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-2 text-sm\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 rounded-full text-xs \".concat(vehicle.status === '可用' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                                                children: vehicle.status\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                                lineNumber: 190,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, vehicle.id, true, {\n                                                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setCurrentStep(1),\n                                        children: \"上一步\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>setCurrentStep(3),\n                                        children: \"下一步：开始调度\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"智能调度\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"AI 正在为您生成最优的运输调度方案\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"正在计算最优调度方案...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-700 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"✓ 分析订单需求和时间约束\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"✓ 匹配车辆容量和物料属性\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"✓ 优化运输路线和成本\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse\",\n                                                children: \"⏳ 生成调度结果...\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setCurrentStep(4),\n                                    className: \"px-8\",\n                                    children: \"查看调度结果\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold\",\n                                children: \"调度结果\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"以下是系统生成的最优运输调度方案\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-green-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"调度方案生成成功\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-700 mt-1\",\n                                        children: \"预计节省运输成本 15%，提高车辆利用率 20%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                        children: \"车辆\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                        children: \"分配订单\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                        children: \"运输路线\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-2 text-left text-sm font-medium\",\n                                                        children: \"配送日期\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: mockScheduleResult.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-t\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-2 text-sm font-medium\",\n                                                            children: result.vehicle\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-2 text-sm\",\n                                                            children: result.orders.join(', ')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-2 text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: result.route\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-2 text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: result.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setCurrentStep(3),\n                                        children: \"重新调度\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Download_FileSpreadsheet_MapPin_Package_Truck_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"导出方案\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                children: \"确认并执行\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPreview, \"Zx2FzPyJq3Um4yZ93PecHmkcsLU=\");\n_c = AppPreview;\nvar _c;\n$RefreshReg$(_c, \"AppPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppPreview.tsx\n"));

/***/ })

});