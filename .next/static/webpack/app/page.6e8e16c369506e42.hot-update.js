"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DocumentEditor.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentEditor.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentEditor: () => (/* binding */ DocumentEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/store */ \"(app-pages-browser)/./src/lib/store.ts\");\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(app-pages-browser)/./src/components/RichTextEditor.tsx\");\n/* harmony import */ var _lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/markdown-utils */ \"(app-pages-browser)/./src/lib/markdown-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DocumentEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DocumentEditor(param) {\n    let { fileId, onBack } = param;\n    _s();\n    const { files, updateFile } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore)();\n    const [htmlContent, setHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [hasChanges, setHasChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const file = files.find((f)=>f.id === fileId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentEditor.useEffect\": ()=>{\n            if (file) {\n                const fileContent = file.content;\n                setContent(fileContent);\n                // 检测内容格式并设置相应的编辑模式\n                if ((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.isMarkdown)(fileContent)) {\n                    setHtmlContent((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.markdownToHtml)(fileContent));\n                    setEditMode('visual');\n                } else {\n                    setHtmlContent(fileContent);\n                    setEditMode('visual');\n                }\n            }\n        }\n    }[\"DocumentEditor.useEffect\"], [\n        file\n    ]);\n    const handleSave = ()=>{\n        if (file && hasChanges) {\n            // 根据当前编辑模式保存内容\n            const contentToSave = editMode === 'visual' ? (0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(htmlContent) : content;\n            updateFile(file.id, contentToSave);\n            setHasChanges(false);\n        }\n    };\n    const handleContentChange = (newContent)=>{\n        if (editMode === 'visual') {\n            setHtmlContent(newContent);\n            setContent((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.htmlToMarkdown)(newContent));\n        } else {\n            setContent(newContent);\n            setHtmlContent((0,_lib_markdown_utils__WEBPACK_IMPORTED_MODULE_5__.markdownToHtml)(newContent));\n        }\n        setHasChanges(true);\n    };\n    const handleModeChange = (mode)=>{\n        setEditMode(mode);\n    };\n    if (!file) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500\",\n                children: \"文件未找到\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onBack,\n                                className: \"p-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-sm\",\n                                        children: file.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: [\n                                            \"最后修改: \",\n                                            file.lastModified.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center border rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: editMode === 'visual' ? 'default' : 'ghost',\n                                        size: \"sm\",\n                                        onClick: ()=>handleModeChange('visual'),\n                                        className: \"rounded-r-none border-r\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Edit3, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"可视化\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: editMode === 'markdown' ? 'default' : 'ghost',\n                                        size: \"sm\",\n                                        onClick: ()=>handleModeChange('markdown'),\n                                        className: \"rounded-none border-r\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Code2, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Markdown\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: editMode === 'preview' ? 'default' : 'ghost',\n                                        size: \"sm\",\n                                        onClick: ()=>handleModeChange('preview'),\n                                        className: \"rounded-l-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Eye, {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"预览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                onClick: handleSave,\n                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Save_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"保存\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: [\n                    editMode === 'visual' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__.RichTextEditor, {\n                            content: htmlContent,\n                            onChange: handleContentChange,\n                            placeholder: \"开始编写文档...\",\n                            editable: true\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    editMode === 'markdown' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Textarea, {\n                            value: content,\n                            onChange: (e)=>handleContentChange(e.target.value),\n                            className: \"w-full h-full resize-none font-mono text-sm\",\n                            placeholder: \"# 开始编写 Markdown 文档...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    editMode === 'preview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full p-4 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: htmlContent\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-t bg-gray-50 text-xs text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    file.type === 'document' ? '📄' : file.type === 'data' ? '📊' : '⚙️',\n                                    \" \",\n                                    file.type\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"编辑模式: \",\n                                    editMode === 'visual' ? '可视化' : editMode === 'markdown' ? 'Markdown' : '预览'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600\",\n                                children: \"● 未保存\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: (()=>{\n                            const stats = getContentStats(editMode === 'visual' ? htmlContent : content);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            stats.words,\n                                            \" 词\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            stats.characters,\n                                            \" 字符\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            stats.lines,\n                                            \" 行\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"约 \",\n                                            stats.readingTime,\n                                            \" 分钟阅读\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true);\n                        })()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentEditor, \"KsZcLl7OSB+YoLomHYc5j8S0OB0=\", false, function() {\n    return [\n        _lib_store__WEBPACK_IMPORTED_MODULE_3__.useAppStore\n    ];\n});\n_c = DocumentEditor;\nvar _c;\n$RefreshReg$(_c, \"DocumentEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentEditor.tsx\n"));

/***/ })

});