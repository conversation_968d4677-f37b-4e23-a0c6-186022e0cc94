[{"/Users/<USER>/Projects/awoprojects/ideaflow/src/app/layout.tsx": "1", "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx": "2", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx": "3", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Canvas.tsx": "4", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ChatMessage.tsx": "5", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/CodeViewer.tsx": "6", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx": "7", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/FlowEditor.tsx": "8", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Header.tsx": "9", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/LeftPanel.tsx": "10", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/avatar.tsx": "11", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/badge.tsx": "12", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/button.tsx": "13", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dialog.tsx": "14", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dropdown-menu.tsx": "15", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/scroll-area.tsx": "16", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/separator.tsx": "17", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tabs.tsx": "18", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/textarea.tsx": "19", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tooltip.tsx": "20", "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/store.ts": "21", "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/utils.ts": "22"}, {"size": 689, "mtime": 1749715235362, "results": "23", "hashOfConfig": "24"}, {"size": 525, "mtime": 1749716124788, "results": "25", "hashOfConfig": "24"}, {"size": 13539, "mtime": 1749719323823, "results": "26", "hashOfConfig": "24"}, {"size": 3212, "mtime": 1749715908980, "results": "27", "hashOfConfig": "24"}, {"size": 3068, "mtime": 1749715860949, "results": "28", "hashOfConfig": "24"}, {"size": 9829, "mtime": 1749716070623, "results": "29", "hashOfConfig": "24"}, {"size": 3672, "mtime": 1749715885898, "results": "30", "hashOfConfig": "24"}, {"size": 6937, "mtime": 1749717797990, "results": "31", "hashOfConfig": "24"}, {"size": 4437, "mtime": 1749715757478, "results": "32", "hashOfConfig": "24"}, {"size": 6721, "mtime": 1749715838516, "results": "33", "hashOfConfig": "24"}, {"size": 1097, "mtime": 1749715620059, "results": "34", "hashOfConfig": "24"}, {"size": 1631, "mtime": 1749715800449, "results": "35", "hashOfConfig": "24"}, {"size": 2123, "mtime": 1749715620044, "results": "36", "hashOfConfig": "24"}, {"size": 3982, "mtime": 1749715620076, "results": "37", "hashOfConfig": "24"}, {"size": 8284, "mtime": 1749715620071, "results": "38", "hashOfConfig": "24"}, {"size": 1645, "mtime": 1749715620084, "results": "39", "hashOfConfig": "24"}, {"size": 699, "mtime": 1749715620087, "results": "40", "hashOfConfig": "24"}, {"size": 1969, "mtime": 1749715620080, "results": "41", "hashOfConfig": "24"}, {"size": 759, "mtime": 1749715620081, "results": "42", "hashOfConfig": "24"}, {"size": 1891, "mtime": 1749715620090, "results": "43", "hashOfConfig": "24"}, {"size": 5142, "mtime": 1749715729745, "results": "44", "hashOfConfig": "24"}, {"size": 166, "mtime": 1749715461606, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14z5376", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/layout.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Canvas.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ChatMessage.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/CodeViewer.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/FlowEditor.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Header.tsx", ["112"], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/LeftPanel.tsx", ["113", "114"], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/button.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/store.ts", ["115", "116", "117"], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/utils.ts", [], [], {"ruleId": "118", "severity": 2, "message": "119", "line": 26, "column": 23, "nodeType": null, "messageId": "120", "endLine": 26, "endColumn": 36}, {"ruleId": "118", "severity": 2, "message": "121", "line": 14, "column": 3, "nodeType": null, "messageId": "120", "endLine": 14, "endColumn": 8}, {"ruleId": "118", "severity": 2, "message": "122", "line": 15, "column": 3, "nodeType": null, "messageId": "120", "endLine": 15, "endColumn": 6}, {"ruleId": "123", "severity": 2, "message": "124", "line": 23, "column": 15, "nodeType": "125", "messageId": "126", "endLine": 23, "endColumn": 18, "suggestions": "127"}, {"ruleId": "123", "severity": 2, "message": "124", "line": 63, "column": 26, "nodeType": "125", "messageId": "126", "endLine": 63, "endColumn": 29, "suggestions": "128"}, {"ruleId": "118", "severity": 2, "message": "129", "line": 76, "column": 64, "nodeType": null, "messageId": "120", "endLine": 76, "endColumn": 67}, "@typescript-eslint/no-unused-vars", "'updateAppInfo' is assigned a value but never used.", "unusedVar", "'Edit3' is defined but never used.", "'Eye' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["130", "131"], ["132", "133"], "'get' is defined but never used.", {"messageId": "134", "fix": "135", "desc": "136"}, {"messageId": "137", "fix": "138", "desc": "139"}, {"messageId": "134", "fix": "140", "desc": "136"}, {"messageId": "137", "fix": "141", "desc": "139"}, "suggestUnknown", {"range": "142", "text": "143"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "144", "text": "145"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "146", "text": "143"}, {"range": "147", "text": "145"}, [407, 410], "unknown", [407, 410], "never", [1204, 1207], [1204, 1207]]