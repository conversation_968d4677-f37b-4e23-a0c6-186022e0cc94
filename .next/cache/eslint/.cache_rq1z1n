[{"/Users/<USER>/Projects/awoprojects/ideaflow/src/app/layout.tsx": "1", "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx": "2", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx": "3", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Canvas.tsx": "4", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ChatMessage.tsx": "5", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/CodeViewer.tsx": "6", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx": "7", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/FlowEditor.tsx": "8", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Header.tsx": "9", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/LeftPanel.tsx": "10", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/avatar.tsx": "11", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/badge.tsx": "12", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/button.tsx": "13", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dialog.tsx": "14", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dropdown-menu.tsx": "15", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/scroll-area.tsx": "16", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/separator.tsx": "17", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tabs.tsx": "18", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/textarea.tsx": "19", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tooltip.tsx": "20", "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/store.ts": "21", "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/utils.ts": "22", "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/api/health/route.ts": "23", "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx": "24", "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/markdown-utils.ts": "25"}, {"size": 689, "mtime": 1749715235362, "results": "26", "hashOfConfig": "27"}, {"size": 525, "mtime": 1749716124788, "results": "28", "hashOfConfig": "27"}, {"size": 13669, "mtime": 1749782970966, "results": "29", "hashOfConfig": "27"}, {"size": 3212, "mtime": 1749715908980, "results": "30", "hashOfConfig": "27"}, {"size": 3068, "mtime": 1749715860949, "results": "31", "hashOfConfig": "27"}, {"size": 9829, "mtime": 1749716070623, "results": "32", "hashOfConfig": "27"}, {"size": 4445, "mtime": 1749782341534, "results": "33", "hashOfConfig": "27"}, {"size": 9712, "mtime": 1749782716146, "results": "34", "hashOfConfig": "27"}, {"size": 4422, "mtime": 1749719383826, "results": "35", "hashOfConfig": "27"}, {"size": 6683, "mtime": 1749780639033, "results": "36", "hashOfConfig": "27"}, {"size": 1097, "mtime": 1749715620059, "results": "37", "hashOfConfig": "27"}, {"size": 1631, "mtime": 1749715800449, "results": "38", "hashOfConfig": "27"}, {"size": 2123, "mtime": 1749715620044, "results": "39", "hashOfConfig": "27"}, {"size": 3982, "mtime": 1749715620076, "results": "40", "hashOfConfig": "27"}, {"size": 8284, "mtime": 1749715620071, "results": "41", "hashOfConfig": "27"}, {"size": 1645, "mtime": 1749715620084, "results": "42", "hashOfConfig": "27"}, {"size": 699, "mtime": 1749715620087, "results": "43", "hashOfConfig": "27"}, {"size": 1969, "mtime": 1749715620080, "results": "44", "hashOfConfig": "27"}, {"size": 759, "mtime": 1749715620081, "results": "45", "hashOfConfig": "27"}, {"size": 1891, "mtime": 1749715620090, "results": "46", "hashOfConfig": "27"}, {"size": 5145, "mtime": 1749719432247, "results": "47", "hashOfConfig": "27"}, {"size": 166, "mtime": 1749715461606, "results": "48", "hashOfConfig": "27"}, {"size": 220, "mtime": 1749719875626, "results": "49", "hashOfConfig": "27"}, {"size": 8657, "mtime": 1749782319008, "results": "50", "hashOfConfig": "27"}, {"size": 3201, "mtime": 1749781129449, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14z5376", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/layout.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/page.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/AppPreview.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Canvas.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ChatMessage.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/CodeViewer.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/DocumentEditor.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/FlowEditor.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/Header.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/LeftPanel.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/button.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/store.ts", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/utils.ts", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/app/api/health/route.ts", [], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/components/RichTextEditor.tsx", ["127", "128", "129", "130"], [], "/Users/<USER>/Projects/awoprojects/ideaflow/src/lib/markdown-utils.ts", [], [], {"ruleId": "131", "severity": 2, "message": "132", "line": 16, "column": 3, "nodeType": null, "messageId": "133", "endLine": 16, "endColumn": 12}, {"ruleId": "131", "severity": 2, "message": "134", "line": 30, "column": 3, "nodeType": null, "messageId": "133", "endLine": 30, "endColumn": 12}, {"ruleId": "131", "severity": 2, "message": "135", "line": 31, "column": 3, "nodeType": null, "messageId": "133", "endLine": 31, "endColumn": 14}, {"ruleId": "131", "severity": 2, "message": "136", "line": 32, "column": 3, "nodeType": null, "messageId": "133", "endLine": 32, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'Underline' is defined but never used.", "unusedVar", "'AlignLeft' is defined but never used.", "'AlignCenter' is defined but never used.", "'AlignRight' is defined but never used."]