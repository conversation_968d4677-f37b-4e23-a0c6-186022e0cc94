System.register([],function(b){"use strict";return{execute:function(){b("createJSONStorage",E);const k=b("redux",(d,c)=>(s,o,n)=>(n.dispatch=e=>(s(g=>d(g,e),!1,e),e),n.dispatchFromDevtools=!0,{dispatch:(...e)=>n.dispatch(...e),...c})),I=new Map,w=d=>{const c=I.get(d);return c?Object.fromEntries(Object.entries(c.stores).map(([s,o])=>[s,o.getState()])):{}},T=(d,c,s)=>{if(d===void 0)return{type:"untracked",connection:c.connect(s)};const o=I.get(s.name);if(o)return{type:"tracked",store:d,...o};const n={connection:c.connect(s),stores:{}};return I.set(s.name,n),{type:"tracked",store:d,...n}},H=b("devtools",(d,c={})=>(s,o,n)=>{const{enabled:e,anonymousActionType:g,store:u,...h}=c;let m;try{m=(e!=null?e:!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(r){}if(!m)return d(s,o,n);const{connection:l,...S}=T(u,m,h);let f=!0;n.setState=(r,a,i)=>{const t=s(r,a);if(!f)return t;const p=i===void 0?{type:g||"anonymous"}:typeof i=="string"?{type:i}:i;return u===void 0?(l==null||l.send(p,o()),t):(l==null||l.send({...p,type:`${u}/${p.type}`},{...w(h.name),[u]:n.getState()}),t)};const v=(...r)=>{const a=f;f=!1,s(...r),f=a},y=d(n.setState,o,n);if(S.type==="untracked"?l==null||l.init(y):(S.stores[S.store]=n,l==null||l.init(Object.fromEntries(Object.entries(S.stores).map(([r,a])=>[r,r===S.store?y:a.getState()])))),n.dispatchFromDevtools&&typeof n.dispatch=="function"){const r=n.dispatch;n.dispatch=(...a)=>{r(...a)}}return l.subscribe(r=>{var a;switch(r.type){case"ACTION":if(typeof r.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return _(r.payload,i=>{if(i.type==="__setState"){if(u===void 0){v(i.state);return}Object.keys(i.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const t=i.state[u];if(t==null)return;JSON.stringify(n.getState())!==JSON.stringify(t)&&v(t);return}n.dispatchFromDevtools&&typeof n.dispatch=="function"&&n.dispatch(i)});case"DISPATCH":switch(r.payload.type){case"RESET":return v(y),u===void 0?l==null?void 0:l.init(n.getState()):l==null?void 0:l.init(w(h.name));case"COMMIT":if(u===void 0){l==null||l.init(n.getState());return}return l==null?void 0:l.init(w(h.name));case"ROLLBACK":return _(r.state,i=>{if(u===void 0){v(i),l==null||l.init(n.getState());return}v(i[u]),l==null||l.init(w(h.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return _(r.state,i=>{if(u===void 0){v(i);return}JSON.stringify(n.getState())!==JSON.stringify(i[u])&&v(i[u])});case"IMPORT_STATE":{const{nextLiftedState:i}=r.payload,t=(a=i.computedStates.slice(-1)[0])==null?void 0:a.state;if(!t)return;v(u===void 0?t:t[u]),l==null||l.send(null,i);return}case"PAUSE_RECORDING":return f=!f}return}}),y}),_=(d,c)=>{let s;try{s=JSON.parse(d)}catch(o){console.error("[zustand devtools middleware] Could not parse the received json",o)}s!==void 0&&c(s)},U=b("subscribeWithSelector",d=>(c,s,o)=>{const n=o.subscribe;return o.subscribe=(e,g,u)=>{let h=e;if(g){const m=(u==null?void 0:u.equalityFn)||Object.is;let l=e(o.getState());h=S=>{const f=e(S);if(!m(l,f)){const v=l;g(l=f,v)}},u!=null&&u.fireImmediately&&g(l,l)}return n(h)},d(c,s,o)}),C=b("combine",(d,c)=>(...s)=>Object.assign({},d,c(...s)));function E(d,c){let s;try{s=d()}catch(o){return}return{getItem:o=>{var n;const e=u=>u===null?null:JSON.parse(u,c==null?void 0:c.reviver),g=(n=s.getItem(o))!=null?n:null;return g instanceof Promise?g.then(e):e(g)},setItem:(o,n)=>s.setItem(o,JSON.stringify(n,c==null?void 0:c.replacer)),removeItem:o=>s.removeItem(o)}}const O=d=>c=>{try{const s=d(c);return s instanceof Promise?s:{then(o){return O(o)(s)},catch(o){return this}}}catch(s){return{then(o){return this},catch(o){return O(o)(s)}}}},N=(d,c)=>(s,o,n)=>{let e={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:a=>a,version:0,merge:(a,i)=>({...i,...a}),...c},g=!1;const u=new Set,h=new Set;let m;try{m=e.getStorage()}catch(a){}if(!m)return d((...a)=>{console.warn(`[zustand persist middleware] Unable to update item '${e.name}', the given storage is currently unavailable.`),s(...a)},o,n);const l=O(e.serialize),S=()=>{const a=e.partialize({...o()});let i;const t=l({state:a,version:e.version}).then(p=>m.setItem(e.name,p)).catch(p=>{i=p});if(i)throw i;return t},f=n.setState;n.setState=(a,i)=>{f(a,i),S()};const v=d((...a)=>{s(...a),S()},o,n);let y;const r=()=>{var a;if(!m)return;g=!1,u.forEach(t=>t(o()));const i=((a=e.onRehydrateStorage)==null?void 0:a.call(e,o()))||void 0;return O(m.getItem.bind(m))(e.name).then(t=>{if(t)return e.deserialize(t)}).then(t=>{if(t)if(typeof t.version=="number"&&t.version!==e.version){if(e.migrate)return e.migrate(t.state,t.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return t.state}).then(t=>{var p;return y=e.merge(t,(p=o())!=null?p:v),s(y,!0),S()}).then(()=>{i==null||i(y,void 0),g=!0,h.forEach(t=>t(y))}).catch(t=>{i==null||i(void 0,t)})};return n.persist={setOptions:a=>{e={...e,...a},a.getStorage&&(m=a.getStorage())},clearStorage:()=>{m==null||m.removeItem(e.name)},getOptions:()=>e,rehydrate:()=>r(),hasHydrated:()=>g,onHydrate:a=>(u.add(a),()=>{u.delete(a)}),onFinishHydration:a=>(h.add(a),()=>{h.delete(a)})},r(),y||v},z=(d,c)=>(s,o,n)=>{let e={storage:E(()=>localStorage),partialize:r=>r,version:0,merge:(r,a)=>({...a,...r}),...c},g=!1;const u=new Set,h=new Set;let m=e.storage;if(!m)return d((...r)=>{console.warn(`[zustand persist middleware] Unable to update item '${e.name}', the given storage is currently unavailable.`),s(...r)},o,n);const l=()=>{const r=e.partialize({...o()});return m.setItem(e.name,{state:r,version:e.version})},S=n.setState;n.setState=(r,a)=>{S(r,a),l()};const f=d((...r)=>{s(...r),l()},o,n);n.getInitialState=()=>f;let v;const y=()=>{var r,a;if(!m)return;g=!1,u.forEach(t=>{var p;return t((p=o())!=null?p:f)});const i=((a=e.onRehydrateStorage)==null?void 0:a.call(e,(r=o())!=null?r:f))||void 0;return O(m.getItem.bind(m))(e.name).then(t=>{if(t)if(typeof t.version=="number"&&t.version!==e.version){if(e.migrate)return[!0,e.migrate(t.state,t.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,t.state];return[!1,void 0]}).then(t=>{var p;const[J,A]=t;if(v=e.merge(A,(p=o())!=null?p:f),s(v,!0),J)return l()}).then(()=>{i==null||i(v,void 0),v=o(),g=!0,h.forEach(t=>t(v))}).catch(t=>{i==null||i(void 0,t)})};return n.persist={setOptions:r=>{e={...e,...r},r.storage&&(m=r.storage)},clearStorage:()=>{m==null||m.removeItem(e.name)},getOptions:()=>e,rehydrate:()=>y(),hasHydrated:()=>g,onHydrate:r=>(u.add(r),()=>{u.delete(r)}),onFinishHydration:r=>(h.add(r),()=>{h.delete(r)})},e.skipHydration||y(),v||f},$=b("persist",(d,c)=>"getStorage"in c||"serialize"in c||"deserialize"in c?N(d,c):z(d,c))}}});
