System.register(["zustand/vanilla","react","use-sync-external-store/shim/with-selector"],function(n){"use strict";var _={__proto__:null,create:1,default:1,useStore:1},o,i,l;return{setters:[function(t){o=t.createStore;var c={__proto__:null};for(var r in t)_[r]||(c[r]=t[r]);n(c)},function(t){i=t.default},function(t){l=t.default}],execute:function(){n("useStore",f);const{useDebugValue:t}=i,{useSyncExternalStoreWithSelector:c}=l,r=e=>e;function f(e,s=r,u){const a=c(e.subscribe,e.getState,e.getServerState||e.getInitialState,s,u);return t(a),a}const S=e=>{const s=typeof e=="function"?o(e):e,u=(a,g)=>f(s,a,g);return Object.assign(u,s),u},v=n("create",e=>e?S(e):S);var d=n("default",e=>v(e))}}});
