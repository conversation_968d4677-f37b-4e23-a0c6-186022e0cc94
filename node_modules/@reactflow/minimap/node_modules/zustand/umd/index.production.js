!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("zustand/vanilla"),require("react"),require("use-sync-external-store/shim/with-selector")):"function"==typeof define&&define.amd?define(["exports","zustand/vanilla","react","use-sync-external-store/shim/with-selector"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustand={},e.zu<PERSON>,e.<PERSON><PERSON>,e.useSyncExternalStoreShimWithSelector)}(this,(function(e,t,n,r){"use strict";var u=n.useDebugValue,o=r.useSyncExternalStoreWithSelector,i=function(e){return e};function a(e,t,n){void 0===t&&(t=i);var r=o(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return u(r),r}var c=function(e){var n="function"==typeof e?t.createStore(e):e,r=function(e,t){return a(n,e,t)};return Object.assign(r,n),r},s=function(e){return e?c(e):c};e.create=s,e.default=function(e){return s(e)},e.useStore=a,Object.keys(t).forEach((function(n){"default"===n||Object.prototype.hasOwnProperty.call(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[n]}})})),Object.defineProperty(e,"__esModule",{value:!0})}));
