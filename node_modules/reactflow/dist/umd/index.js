!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactFlow={},e.React,e.ReactDOM)}(this,(function(e,t,n){"use strict";function o(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n,r=0;r<e.length;r++)""!==(n=o(e[r]))&&(t+=(t&&" ")+n);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var i,a,s,l={},c={},u={},d={get exports(){return u},set exports(e){u=e}},h={};function f(){return a||(a=1,function(e){e.exports=function(){if(i)return h;i=1;var e=t,n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=e.useState,r=e.useEffect,a=e.useLayoutEffect,s=e.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!n(e,o)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),i=o({inst:{value:n,getSnapshot:t}}),c=i[0].inst,u=i[1];return a((function(){c.value=n,c.getSnapshot=t,l(c)&&u({inst:c})}),[e,n,t]),r((function(){return l(c)&&u({inst:c}),e((function(){l(c)&&u({inst:c})}))}),[e]),s(n),n};return h.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:c,h}()}(d)),u}
/**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */!function(e){e.exports=function(){if(s)return c;s=1;var e=t,n=f(),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=n.useSyncExternalStore,i=e.useRef,a=e.useEffect,l=e.useMemo,u=e.useDebugValue;return c.useSyncExternalStoreWithSelector=function(e,t,n,s,c){var d=i(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=l((function(){function e(e){if(!a){if(a=!0,r=e,e=s(e),void 0!==c&&h.hasValue){var t=h.value;if(c(t,e))return i=t}return i=e}if(t=i,o(r,e))return t;var n=s(e);return void 0!==c&&c(t,n)?t:(r=e,i=n)}var r,i,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]}),[t,n,s,c]);var f=r(e,d[0],d[1]);return a((function(){h.hasValue=!0,h.value=f}),[f]),u(f),f},c}()}({get exports(){return l},set exports(e){l=e}});var g=r(l);const p=e=>{let t;const n=new Set,o=(e,o)=>{const r="function"==typeof e?e(t):e;if(!Object.is(r,t)){const e=t;t=(null!=o?o:"object"!=typeof r)?r:Object.assign({},t,r),n.forEach((n=>n(t,e)))}},r=()=>t,i={setState:o,getState:r,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}};return t=e(o,r,i),i},{useSyncExternalStoreWithSelector:m}=g;function y(e,n=e.getState,o){const r=m(e.subscribe,e.getState,e.getServerState||e.getState,n,o);return t.useDebugValue(r),r}const v=(e,t)=>{const n=(e=>e?p(e):p)(e),o=(e,o=t)=>y(n,e,o);return Object.assign(o,n),o};function b(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}var w={value:()=>{}};function S(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new x(o)}function x(e){this._=e}function E(e,t){return e.trim().split(/^|\s+/).map((function(e){var n="",o=e.indexOf(".");if(o>=0&&(n=e.slice(o+1),e=e.slice(0,o)),e&&!t.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:n}}))}function C(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function _(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=w,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}x.prototype=S.prototype={constructor:x,on:function(e,t){var n,o=this._,r=E(e+"",o),i=-1,a=r.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++i<a;)if(n=(e=r[i]).type)o[n]=_(o[n],e.name,t);else if(null==t)for(n in o)o[n]=_(o[n],e.name,null);return this}for(;++i<a;)if((n=(e=r[i]).type)&&(n=C(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new x(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var N="http://www.w3.org/1999/xhtml",M={svg:"http://www.w3.org/2000/svg",xhtml:N,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function k(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),M.hasOwnProperty(t)?{space:M[t],local:e}:e}function P(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===N&&t.documentElement.namespaceURI===N?t.createElement(e):t.createElementNS(n,e)}}function A(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function O(e){var t=k(e);return(t.local?A:P)(t)}function R(){}function I(e){return null==e?R:function(){return this.querySelector(e)}}function z(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}function D(){return[]}function B(e){return null==e?D:function(){return this.querySelectorAll(e)}}function $(e){return function(){return this.matches(e)}}function T(e){return function(t){return t.matches(e)}}var V=Array.prototype.find;function H(){return this.firstElementChild}var L=Array.prototype.filter;function X(){return Array.from(this.children)}function Y(e){return new Array(e.length)}function F(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function W(e){return function(){return e}}function Z(e,t,n,o,r,i){for(var a,s=0,l=t.length,c=i.length;s<c;++s)(a=t[s])?(a.__data__=i[s],o[s]=a):n[s]=new F(e,i[s]);for(;s<l;++s)(a=t[s])&&(r[s]=a)}function K(e,t,n,o,r,i,a){var s,l,c,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=a.call(l,l.__data__,s,t)+"",u.has(c)?r[s]=l:u.set(c,l));for(s=0;s<h;++s)c=a.call(e,i[s],s,i)+"",(l=u.get(c))?(o[s]=l,l.__data__=i[s],u.delete(c)):n[s]=new F(e,i[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(r[s]=l)}function j(e){return e.__data__}function q(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function U(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function G(e){return function(){this.removeAttribute(e)}}function Q(e){return function(){this.removeAttributeNS(e.space,e.local)}}function J(e,t){return function(){this.setAttribute(e,t)}}function ee(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function te(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function ne(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function oe(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function re(e){return function(){this.style.removeProperty(e)}}function ie(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ae(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function se(e,t){return e.style.getPropertyValue(t)||oe(e).getComputedStyle(e,null).getPropertyValue(t)}function le(e){return function(){delete this[e]}}function ce(e,t){return function(){this[e]=t}}function ue(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function de(e){return e.trim().split(/^|\s+/)}function he(e){return e.classList||new fe(e)}function fe(e){this._node=e,this._names=de(e.getAttribute("class")||"")}function ge(e,t){for(var n=he(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function pe(e,t){for(var n=he(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function me(e){return function(){ge(this,e)}}function ye(e){return function(){pe(this,e)}}function ve(e,t){return function(){(t.apply(this,arguments)?ge:pe)(this,e)}}function be(){this.textContent=""}function we(e){return function(){this.textContent=e}}function Se(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function xe(){this.innerHTML=""}function Ee(e){return function(){this.innerHTML=e}}function Ce(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function _e(){this.nextSibling&&this.parentNode.appendChild(this)}function Ne(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Me(){return null}function ke(){var e=this.parentNode;e&&e.removeChild(this)}function Pe(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Ae(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Oe(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}function Re(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function Ie(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function ze(e,t,n){var o=oe(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function De(e,t){return function(){return ze(this,e,t)}}function Be(e,t){return function(){return ze(this,e,t.apply(this,arguments))}}F.prototype={constructor:F,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},fe.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var $e=[null];function Te(e,t){this._groups=e,this._parents=t}function Ve(){return new Te([[document.documentElement]],$e)}function He(e){return"string"==typeof e?new Te([[document.querySelector(e)]],[document.documentElement]):new Te([[e]],$e)}function Le(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}Te.prototype=Ve.prototype={constructor:Te,select:function(e){"function"!=typeof e&&(e=I(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=t[r],l=s.length,c=o[r]=new Array(l),u=0;u<l;++u)(i=s[u])&&(a=e.call(i,i.__data__,u,s))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new Te(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return z(e.apply(this,arguments))}}(e):B(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,s=t[i],l=s.length,c=0;c<l;++c)(a=s[c])&&(o.push(e.call(a,a.__data__,c,s)),r.push(a));return new Te(o,r)},selectChild:function(e){return this.select(null==e?H:function(e){return function(){return V.call(this.children,e)}}("function"==typeof e?e:T(e)))},selectChildren:function(e){return this.selectAll(null==e?X:function(e){return function(){return L.call(this.children,e)}}("function"==typeof e?e:T(e)))},filter:function(e){"function"!=typeof e&&(e=$(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Te(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,j);var n=t?K:Z,o=this._parents,r=this._groups;"function"!=typeof e&&(e=W(e));for(var i=r.length,a=new Array(i),s=new Array(i),l=new Array(i),c=0;c<i;++c){var u=o[c],d=r[c],h=d.length,f=q(e.call(u,u&&u.__data__,c,o)),g=f.length,p=s[c]=new Array(g),m=a[c]=new Array(g),y=l[c]=new Array(h);n(u,d,p,m,y,f,t);for(var v,b,w=0,S=0;w<g;++w)if(v=p[w]){for(w>=S&&(S=w+1);!(b=m[S])&&++S<g;);v._next=b||null}}return(a=new Te(a,o))._enter=s,a._exit=l,a},enter:function(){return new Te(this._enter||this._groups.map(Y),this._parents)},exit:function(){return new Te(this._exit||this._groups.map(Y),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),l=0;l<a;++l)for(var c,u=n[l],d=o[l],h=u.length,f=s[l]=new Array(h),g=0;g<h;++g)(c=u[g]||d[g])&&(f[g]=c);for(;l<r;++l)s[l]=n[l];return new Te(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=U);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],l=s.length,c=r[i]=new Array(l),u=0;u<l;++u)(a=s[u])&&(c[u]=a);c.sort(t)}return new Te(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,s=i.length;a<s;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=k(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?Q:G:"function"==typeof t?n.local?ne:te:n.local?ee:J)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?re:"function"==typeof t?ae:ie)(e,t,null==n?"":n)):se(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?le:"function"==typeof t?ue:ce)(e,t)):this.node()[e]},classed:function(e,t){var n=de(e+"");if(arguments.length<2){for(var o=he(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?ve:t?me:ye)(n,t))},text:function(e){return arguments.length?this.each(null==e?be:("function"==typeof e?Se:we)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?xe:("function"==typeof e?Ce:Ee)(e)):this.node().innerHTML},raise:function(){return this.each(_e)},lower:function(){return this.each(Ne)},append:function(e){var t="function"==typeof e?e:O(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:O(e),o=null==t?Me:"function"==typeof t?t:I(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(ke)},clone:function(e){return this.select(e?Ae:Pe)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=Oe(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?Ie:Re,o=0;o<a;++o)this.each(s(i[o],t,n));return this}var s=this.node().__on;if(s)for(var l,c=0,u=s.length;c<u;++c)for(o=0,l=s[c];o<a;++o)if((r=i[o]).type===l.type&&r.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?Be:De)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Xe={passive:!1},Ye={capture:!0,passive:!1};function Fe(e){e.stopImmediatePropagation()}function We(e){e.preventDefault(),e.stopImmediatePropagation()}function Ze(e){var t=e.document.documentElement,n=He(e).on("dragstart.drag",We,Ye);"onselectstart"in t?n.on("selectstart.drag",We,Ye):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Ke(e,t){var n=e.document.documentElement,o=He(e).on("dragstart.drag",null);t&&(o.on("click.drag",We,Ye),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var je=e=>()=>e;function qe(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function Ue(e){return!e.ctrlKey&&!e.button}function Ge(){return this.parentNode}function Qe(e,t){return null==t?{x:e.x,y:e.y}:t}function Je(){return navigator.maxTouchPoints||"ontouchstart"in this}function et(){var e,t,n,o,r=Ue,i=Ge,a=Qe,s=Je,l={},c=S("start","drag","end"),u=0,d=0;function h(e){e.on("mousedown.drag",f).filter(s).on("touchstart.drag",m).on("touchmove.drag",y,Xe).on("touchend.drag touchcancel.drag",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(a,s){if(!o&&r.call(this,a,s)){var l=b(this,i.call(this,a,s),a,s,"mouse");l&&(He(a.view).on("mousemove.drag",g,Ye).on("mouseup.drag",p,Ye),Ze(a.view),Fe(a),n=!1,e=a.clientX,t=a.clientY,l("start",a))}}function g(o){if(We(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>d}l.mouse("drag",o)}function p(e){He(e.view).on("mousemove.drag mouseup.drag",null),Ke(e.view,n),We(e),l.mouse("end",e)}function m(e,t){if(r.call(this,e,t)){var n,o,a=e.changedTouches,s=i.call(this,e,t),l=a.length;for(n=0;n<l;++n)(o=b(this,s,e,t,a[n].identifier,a[n]))&&(Fe(e),o("start",e,a[n]))}}function y(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=l[o[t].identifier])&&(We(e),n("drag",e,o[t]))}function v(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=l[r[t].identifier])&&(Fe(e),n("end",e,r[t]))}function b(e,t,n,o,r,i){var s,d,f,g=c.copy(),p=Le(i||n,t);if(null!=(f=a.call(e,new qe("beforestart",{sourceEvent:n,target:h,identifier:r,active:u,x:p[0],y:p[1],dx:0,dy:0,dispatch:g}),o)))return s=f.x-p[0]||0,d=f.y-p[1]||0,function n(i,a,c){var m,y=p;switch(i){case"start":l[r]=n,m=u++;break;case"end":delete l[r],--u;case"drag":p=Le(c||a,t),m=u}g.call(i,e,new qe(i,{sourceEvent:a,subject:f,target:h,identifier:r,active:m,x:p[0]+s,y:p[1]+d,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:g}),o)}}return h.filter=function(e){return arguments.length?(r="function"==typeof e?e:je(!!e),h):r},h.container=function(e){return arguments.length?(i="function"==typeof e?e:je(e),h):i},h.subject=function(e){return arguments.length?(a="function"==typeof e?e:je(e),h):a},h.touchable=function(e){return arguments.length?(s="function"==typeof e?e:je(!!e),h):s},h.on=function(){var e=c.on.apply(c,arguments);return e===c?h:e},h.clickDistance=function(e){return arguments.length?(d=(e=+e)*e,h):Math.sqrt(d)},h}function tt(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function nt(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function ot(){}qe.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var rt=.7,it=1/rt,at="\\s*([+-]?\\d+)\\s*",st="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",lt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ct=/^#([0-9a-f]{3,8})$/,ut=new RegExp(`^rgb\\(${at},${at},${at}\\)$`),dt=new RegExp(`^rgb\\(${lt},${lt},${lt}\\)$`),ht=new RegExp(`^rgba\\(${at},${at},${at},${st}\\)$`),ft=new RegExp(`^rgba\\(${lt},${lt},${lt},${st}\\)$`),gt=new RegExp(`^hsl\\(${st},${lt},${lt}\\)$`),pt=new RegExp(`^hsla\\(${st},${lt},${lt},${st}\\)$`),mt={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function yt(){return this.rgb().formatHex()}function vt(){return this.rgb().formatRgb()}function bt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=ct.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?wt(t):3===n?new Ct(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?St(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?St(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=ut.exec(e))?new Ct(t[1],t[2],t[3],1):(t=dt.exec(e))?new Ct(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=ht.exec(e))?St(t[1],t[2],t[3],t[4]):(t=ft.exec(e))?St(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=gt.exec(e))?At(t[1],t[2]/100,t[3]/100,1):(t=pt.exec(e))?At(t[1],t[2]/100,t[3]/100,t[4]):mt.hasOwnProperty(e)?wt(mt[e]):"transparent"===e?new Ct(NaN,NaN,NaN,0):null}function wt(e){return new Ct(e>>16&255,e>>8&255,255&e,1)}function St(e,t,n,o){return o<=0&&(e=t=n=NaN),new Ct(e,t,n,o)}function xt(e){return e instanceof ot||(e=bt(e)),e?new Ct((e=e.rgb()).r,e.g,e.b,e.opacity):new Ct}function Et(e,t,n,o){return 1===arguments.length?xt(e):new Ct(e,t,n,null==o?1:o)}function Ct(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function _t(){return`#${Pt(this.r)}${Pt(this.g)}${Pt(this.b)}`}function Nt(){const e=Mt(this.opacity);return`${1===e?"rgb(":"rgba("}${kt(this.r)}, ${kt(this.g)}, ${kt(this.b)}${1===e?")":`, ${e})`}`}function Mt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function kt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Pt(e){return((e=kt(e))<16?"0":"")+e.toString(16)}function At(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Rt(e,t,n,o)}function Ot(e){if(e instanceof Rt)return new Rt(e.h,e.s,e.l,e.opacity);if(e instanceof ot||(e=bt(e)),!e)return new Rt;if(e instanceof Rt)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,s=i-r,l=(i+r)/2;return s?(a=t===i?(n-o)/s+6*(n<o):n===i?(o-t)/s+2:(t-n)/s+4,s/=l<.5?i+r:2-i-r,a*=60):s=l>0&&l<1?0:a,new Rt(a,s,l,e.opacity)}function Rt(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function It(e){return(e=(e||0)%360)<0?e+360:e}function zt(e){return Math.max(0,Math.min(1,e||0))}function Dt(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}tt(ot,bt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:yt,formatHex:yt,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Ot(this).formatHsl()},formatRgb:vt,toString:vt}),tt(Ct,Et,nt(ot,{brighter(e){return e=null==e?it:Math.pow(it,e),new Ct(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?rt:Math.pow(rt,e),new Ct(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ct(kt(this.r),kt(this.g),kt(this.b),Mt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:_t,formatHex:_t,formatHex8:function(){return`#${Pt(this.r)}${Pt(this.g)}${Pt(this.b)}${Pt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Nt,toString:Nt})),tt(Rt,(function(e,t,n,o){return 1===arguments.length?Ot(e):new Rt(e,t,n,null==o?1:o)}),nt(ot,{brighter(e){return e=null==e?it:Math.pow(it,e),new Rt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?rt:Math.pow(rt,e),new Rt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Ct(Dt(e>=240?e-240:e+120,r,o),Dt(e,r,o),Dt(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new Rt(It(this.h),zt(this.s),zt(this.l),Mt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Mt(this.opacity);return`${1===e?"hsl(":"hsla("}${It(this.h)}, ${100*zt(this.s)}%, ${100*zt(this.l)}%${1===e?")":`, ${e})`}`}}));var Bt=e=>()=>e;function $t(e){return 1==(e=+e)?Tt:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):Bt(isNaN(t)?n:t)}}function Tt(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):Bt(isNaN(e)?t:e)}var Vt=function e(t){var n=$t(t);function o(e,t){var o=n((e=Et(e)).r,(t=Et(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),a=Tt(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function Ht(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Lt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Xt=new RegExp(Lt.source,"g");function Yt(e,t){var n,o,r,i=Lt.lastIndex=Xt.lastIndex=0,a=-1,s=[],l=[];for(e+="",t+="";(n=Lt.exec(e))&&(o=Xt.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:Ht(n,o)})),i=Xt.lastIndex;return i<t.length&&(r=t.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,o=0;o<t;++o)s[(n=l[o]).i]=n.x(e);return s.join("")})}var Ft,Wt=180/Math.PI,Zt={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Kt(e,t,n,o,r,i){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*o)&&(n-=e*l,o-=t*l),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,l/=s),e*o<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*Wt,skewX:Math.atan(l)*Wt,scaleX:a,scaleY:s}}function jt(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],l=[];return i=e(i),a=e(a),function(e,o,r,i,a,s){if(e!==r||o!==i){var l=a.push("translate(",null,t,null,n);s.push({i:l-4,x:Ht(e,r)},{i:l-2,x:Ht(o,i)})}else(r||i)&&a.push("translate("+r+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,l),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Ht(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,a.rotate,s,l),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Ht(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,a.skewX,s,l),function(e,t,n,o,i,a){if(e!==n||t!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:Ht(e,n)},{i:s-2,x:Ht(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,l),i=a=null,function(e){for(var t,n=-1,o=l.length;++n<o;)s[(t=l[n]).i]=t.x(e);return s.join("")}}}var qt=jt((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Zt:Kt(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),Ut=jt((function(e){return null==e?Zt:(Ft||(Ft=document.createElementNS("http://www.w3.org/2000/svg","g")),Ft.setAttribute("transform",e),(e=Ft.transform.baseVal.consolidate())?Kt((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):Zt)}),", ",")",")");function Gt(e){return((e=Math.exp(e))+1/e)/2}var Qt,Jt,en=function e(t,n,o){function r(e,r){var i,a,s=e[0],l=e[1],c=e[2],u=r[0],d=r[1],h=r[2],f=u-s,g=d-l,p=f*f+g*g;if(p<1e-12)a=Math.log(h/c)/t,i=function(e){return[s+e*f,l+e*g,c*Math.exp(t*e*a)]};else{var m=Math.sqrt(p),y=(h*h-c*c+o*p)/(2*c*n*m),v=(h*h-c*c-o*p)/(2*h*n*m),b=Math.log(Math.sqrt(y*y+1)-y),w=Math.log(Math.sqrt(v*v+1)-v);a=(w-b)/t,i=function(e){var o,r=e*a,i=Gt(b),u=c/(n*m)*(i*(o=t*r+b,((o=Math.exp(2*o))-1)/(o+1))-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[s+u*f,l+u*g,c*i/Gt(t*r+b)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4),tn=0,nn=0,on=0,rn=0,an=0,sn=0,ln="object"==typeof performance&&performance.now?performance:Date,cn="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function un(){return an||(cn(dn),an=ln.now()+sn)}function dn(){an=0}function hn(){this._call=this._time=this._next=null}function fn(e,t,n){var o=new hn;return o.restart(e,t,n),o}function gn(){an=(rn=ln.now())+sn,tn=nn=0;try{!function(){un(),++tn;for(var e,t=Qt;t;)(e=an-t._time)>=0&&t._call.call(void 0,e),t=t._next;--tn}()}finally{tn=0,function(){var e,t,n=Qt,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:Qt=t);Jt=e,mn(o)}(),an=0}}function pn(){var e=ln.now(),t=e-rn;t>1e3&&(sn-=t,rn=e)}function mn(e){tn||(nn&&(nn=clearTimeout(nn)),e-an>24?(e<1/0&&(nn=setTimeout(gn,e-ln.now()-sn)),on&&(on=clearInterval(on))):(on||(rn=ln.now(),on=setInterval(pn,1e3)),tn=1,cn(gn)))}function yn(e,t,n){var o=new hn;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}hn.prototype=fn.prototype={constructor:hn,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?un():+n)+(null==t?0:+t),this._next||Jt===this||(Jt?Jt._next=this:Qt=this,Jt=this),this._call=e,this._time=n,mn()},stop:function(){this._call&&(this._call=null,this._time=1/0,mn())}};var vn=S("start","end","cancel","interrupt"),bn=[];function wn(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var c,u,d,h;if(1!==n.state)return l();for(c in r)if((h=r[c]).name===n.name){if(3===h.state)return yn(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[c]):+c<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[c])}if(yn((function(){3===n.state&&(n.state=4,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(n.state=3,o=new Array(d=n.tween.length),c=0,u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=o.length;++i<a;)o[i].call(e,r);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var o in n.state=6,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=fn(i,0,n.time)}(e,n,{name:t,index:o,group:r,on:vn,tween:bn,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function Sn(e,t){var n=En(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function xn(e,t){var n=En(e,t);if(n.state>3)throw new Error("too late; already running");return n}function En(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Cn(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function _n(e,t){var n,o;return function(){var r=xn(this,e),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}r.tween=o}}function Nn(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=xn(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:t,value:n},l=0,c=r.length;l<c;++l)if(r[l].name===t){r[l]=s;break}l===c&&r.push(s)}i.tween=r}}function Mn(e,t,n){var o=e._id;return e.each((function(){var e=xn(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return En(e,o).value[t]}}function kn(e,t){var n;return("number"==typeof t?Ht:t instanceof bt?Vt:(n=bt(t))?(t=n,Vt):Yt)(e,t)}function Pn(e){return function(){this.removeAttribute(e)}}function An(e){return function(){this.removeAttributeNS(e.space,e.local)}}function On(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function Rn(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function In(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(e))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttribute(e)}}function zn(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttributeNS(e.space,e.local)}}function Dn(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Bn(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function $n(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Bn(e,r)),n}return r._value=t,r}function Tn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Dn(e,r)),n}return r._value=t,r}function Vn(e,t){return function(){Sn(this,e).delay=+t.apply(this,arguments)}}function Hn(e,t){return t=+t,function(){Sn(this,e).delay=t}}function Ln(e,t){return function(){xn(this,e).duration=+t.apply(this,arguments)}}function Xn(e,t){return t=+t,function(){xn(this,e).duration=t}}function Yn(e,t){if("function"!=typeof t)throw new Error;return function(){xn(this,e).ease=t}}function Fn(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?Sn:xn;return function(){var a=i(this,e),s=a.on;s!==o&&(r=(o=s).copy()).on(t,n),a.on=r}}var Wn=Ve.prototype.constructor;function Zn(e){return function(){this.style.removeProperty(e)}}function Kn(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}function jn(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&Kn(e,i,n)),o}return i._value=t,i}function qn(e){return function(t){this.textContent=e.call(this,t)}}function Un(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&qn(o)),t}return o._value=e,o}var Gn=0;function Qn(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function Jn(){return++Gn}var eo=Ve.prototype;Qn.prototype={constructor:Qn,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=I(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,l,c=o[a],u=c.length,d=i[a]=new Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,wn(d[h],t,n,h,d,En(s,n)));return new Qn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=B(e));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var l,c=o[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),g=En(l,n),p=0,m=f.length;p<m;++p)(h=f[p])&&wn(h,t,n,p,f,g);i.push(f),a.push(l)}return new Qn(i,a,t,n)},selectChild:eo.selectChild,selectChildren:eo.selectChildren,filter:function(e){"function"!=typeof e&&(e=$(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Qn(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var l,c=t[s],u=n[s],d=c.length,h=a[s]=new Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<o;++s)a[s]=t[s];return new Qn(a,this._parents,this._name,this._id)},selection:function(){return new Wn(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=Jn(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)if(a=s[c]){var u=En(a,t);wn(a,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new Qn(o,this._parents,e,n)},call:eo.call,nodes:eo.nodes,node:eo.node,size:eo.size,empty:eo.empty,each:eo.each,on:function(e,t){var n=this._id;return arguments.length<2?En(this.node(),n).on.on(e):this.each(Fn(n,e,t))},attr:function(e,t){var n=k(e),o="transform"===n?Ut:kn;return this.attrTween(e,"function"==typeof t?(n.local?zn:In)(n,o,Mn(this,"attr."+e,t)):null==t?(n.local?An:Pn)(n):(n.local?Rn:On)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=k(e);return this.tween(n,(o.local?$n:Tn)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?qt:kn;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=se(this,e),a=(this.style.removeProperty(e),se(this,e));return i===a?null:i===n&&a===o?r:r=t(n=i,o=a)}}(e,o)).on("end.style."+e,Zn(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var a=se(this,e),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(e),l=s=se(this,e)),a===l?null:a===o&&l===r?i:(r=l,i=t(o=a,s))}}(e,o,Mn(this,"style."+e,t))).each(function(e,t){var n,o,r,i,a="style."+t,s="end."+a;return function(){var l=xn(this,e),c=l.on,u=null==l.value[a]?i||(i=Zn(t)):void 0;c===n&&r===u||(o=(n=c).copy()).on(s,r=u),l.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var a=se(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,jn(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(Mn(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,Un(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=En(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?_n:Nn)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Vn:Hn)(t,e)):En(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Ln:Xn)(t,e)):En(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(Yn(t,e)):En(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;xn(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},l={value:function(){0==--r&&i()}};n.each((function(){var n=xn(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:eo[Symbol.iterator]};var to={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function no(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}Ve.prototype.interrupt=function(e){return this.each((function(){Cn(this,e)}))},Ve.prototype.transition=function(e){var t,n;e instanceof Qn?(t=e._id,e=e._name):(t=Jn(),(n=to).time=un(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)(a=s[c])&&wn(a,e,t,c,s,n||no(a,t));return new Qn(o,this._parents,e,t)};var oo=e=>()=>e;function ro(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function io(e,t,n){this.k=e,this.x=t,this.y=n}io.prototype={constructor:io,scale:function(e){return 1===e?this:new io(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new io(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var ao=new io(1,0,0);function so(e){e.stopImmediatePropagation()}function lo(e){e.preventDefault(),e.stopImmediatePropagation()}function co(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function uo(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function ho(){return this.__zoom||ao}function fo(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function go(){return navigator.maxTouchPoints||"ontouchstart"in this}function po(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function mo(){var e,t,n,o=co,r=uo,i=po,a=fo,s=go,l=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],u=250,d=en,h=S("start","zoom","end"),f=500,g=0,p=10;function m(e){e.property("__zoom",ho).on("wheel.zoom",C,{passive:!1}).on("mousedown.zoom",_).on("dblclick.zoom",N).filter(s).on("touchstart.zoom",M).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",P).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(l[0],Math.min(l[1],t)))===e.k?e:new io(t,e.x,e.y)}function v(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new io(e.k,o,r)}function b(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function w(e,t,n,o){e.on("start.zoom",(function(){x(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){x(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,a=x(e,i).event(o),s=r.apply(e,i),l=null==n?b(s):"function"==typeof n?n.apply(e,i):n,c=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,h="function"==typeof t?t.apply(e,i):t,f=d(u.invert(l).concat(c/u.k),h.invert(l).concat(c/h.k));return function(e){if(1===e)e=h;else{var t=f(e),n=c/t[2];e=new io(n,l[0]-t[0]*n,l[1]-t[1]*n)}a.zoom(null,e)}}))}function x(e,t,n){return!n&&e.__zooming||new E(e,t)}function E(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function C(e,...t){if(o.apply(this,arguments)){var n=x(this,t).event(e),r=this.__zoom,s=Math.max(l[0],Math.min(l[1],r.k*Math.pow(2,a.apply(this,arguments)))),u=Le(e);if(n.wheel)n.mouse[0][0]===u[0]&&n.mouse[0][1]===u[1]||(n.mouse[1]=r.invert(n.mouse[0]=u)),clearTimeout(n.wheel);else{if(r.k===s)return;n.mouse=[u,r.invert(u)],Cn(this),n.start()}lo(e),n.wheel=setTimeout(d,150),n.zoom("mouse",i(v(y(r,s),n.mouse[0],n.mouse[1]),n.extent,c))}function d(){n.wheel=null,n.end()}}function _(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,a=x(this,t,!0).event(e),s=He(e.view).on("mousemove.zoom",h,!0).on("mouseup.zoom",f,!0),l=Le(e,r),u=e.clientX,d=e.clientY;Ze(e.view),so(e),a.mouse=[l,this.__zoom.invert(l)],Cn(this),a.start()}function h(e){if(lo(e),!a.moved){var t=e.clientX-u,n=e.clientY-d;a.moved=t*t+n*n>g}a.event(e).zoom("mouse",i(v(a.that.__zoom,a.mouse[0]=Le(e,r),a.mouse[1]),a.extent,c))}function f(e){s.on("mousemove.zoom mouseup.zoom",null),Ke(e.view,a.moved),lo(e),a.event(e).end()}}function N(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,a=Le(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(a),l=n.k*(e.shiftKey?.5:2),d=i(v(y(n,l),a,s),r.apply(this,t),c);lo(e),u>0?He(this).transition().duration(u).call(w,d,a,e):He(this).call(m.transform,d,a,e)}}function M(n,...r){if(o.apply(this,arguments)){var i,a,s,l,c=n.touches,u=c.length,d=x(this,r,n.changedTouches.length===u).event(n);for(so(n),a=0;a<u;++a)l=[l=Le(s=c[a],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=l[0],e=setTimeout((function(){e=null}),f)),Cn(this),d.start())}}function k(e,...t){if(this.__zooming){var n,o,r,a,s=x(this,t).event(e),l=e.changedTouches,u=l.length;for(lo(e),n=0;n<u;++n)r=Le(o=l[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],g=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,m=(m=g[0]-h[0])*m+(m=g[1]-h[1])*m;o=y(o,Math.sqrt(p/m)),r=[(d[0]+f[0])/2,(d[1]+f[1])/2],a=[(h[0]+g[0])/2,(h[1]+g[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],a=s.touch0[1]}s.zoom("touch",i(v(o,r,a),s.extent,c))}}function P(e,...o){if(this.__zooming){var r,i,a=x(this,o).event(e),s=e.changedTouches,l=s.length;for(so(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),f),r=0;r<l;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=Le(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var c=He(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return m.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",ho),e!==r?w(e,t,n,o):r.interrupt().each((function(){x(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},m.scaleBy=function(e,t,n,o){m.scaleTo(e,(function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n}),n,o)},m.scaleTo=function(e,t,n,o){m.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,a=null==n?b(e):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(a),l="function"==typeof t?t.apply(this,arguments):t;return i(v(y(o,l),a,s),e,c)}),n,o)},m.translateBy=function(e,t,n,o){m.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),c)}),null,o)},m.translateTo=function(e,t,n,o,a){m.transform(e,(function(){var e=r.apply(this,arguments),a=this.__zoom,s=null==o?b(e):"function"==typeof o?o.apply(this,arguments):o;return i(ao.translate(s[0],s[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,c)}),o,a)},E.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=He(this.that).datum();h.call(e,this.that,new ro(e,{sourceEvent:this.sourceEvent,target:m,type:e,transform:this.that.__zoom,dispatch:h}),t)}},m.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:oo(+e),m):a},m.filter=function(e){return arguments.length?(o="function"==typeof e?e:oo(!!e),m):o},m.touchable=function(e){return arguments.length?(s="function"==typeof e?e:oo(!!e),m):s},m.extent=function(e){return arguments.length?(r="function"==typeof e?e:oo([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),m):r},m.scaleExtent=function(e){return arguments.length?(l[0]=+e[0],l[1]=+e[1],m):[l[0],l[1]]},m.translateExtent=function(e){return arguments.length?(c[0][0]=+e[0][0],c[1][0]=+e[1][0],c[0][1]=+e[0][1],c[1][1]=+e[1][1],m):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},m.constrain=function(e){return arguments.length?(i=e,m):i},m.duration=function(e){return arguments.length?(u=+e,m):u},m.interpolate=function(e){return arguments.length?(d=e,m):d},m.on=function(){var e=h.on.apply(h,arguments);return e===h?m:e},m.clickDistance=function(e){return arguments.length?(g=(e=+e)*e,m):Math.sqrt(g)},m.tapDistance=function(e){return arguments.length?(p=+e,m):p},m}io.prototype;const yo=t.createContext(null),vo=yo.Provider,bo=e=>`Node type "${e}" not found. Using fallback type "default".`,wo=()=>"The React Flow parent container needs a width and a height to render the graph.",So=()=>"Only child nodes can use a parent extent.",xo=e=>`Marker type "${e}" doesn't exist.`,Eo=(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,Co=()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",_o=e=>`Edge type "${e}" not found. Using fallback type "default".`,No=e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,Mo=(()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001")();function ko(e,n){const o=t.useContext(yo);if(null===o)throw new Error(Mo);return y(o,e,n)}const Po=()=>{const e=t.useContext(yo);if(null===e)throw new Error(Mo);return t.useMemo((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy})),[e])},Ao=e=>e.userSelectionActive?"none":"all";function Oo({position:e,children:n,className:r,style:i,...a}){const s=ko(Ao),l=`${e}`.split("-");return t.createElement("div",{className:o(["react-flow__panel",r,...l]),style:{...i,pointerEvents:s},...a},n)}function Ro({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:t.createElement(Oo,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},t.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var Io=t.memo((({x:e,y:n,label:r,labelStyle:i={},labelShowBg:a=!0,labelBgStyle:s={},labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:d,...h})=>{const f=t.useRef(null),[g,p]=t.useState({x:0,y:0,width:0,height:0}),m=o(["react-flow__edge-textwrapper",d]);return t.useEffect((()=>{if(f.current){const e=f.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}}),[r]),void 0!==r&&r?t.createElement("g",{transform:`translate(${e-g.width/2} ${n-g.height/2})`,className:m,visibility:g.width?"visible":"hidden",...h},a&&t.createElement("rect",{width:g.width+2*l[0],x:-l[0],y:-l[1],height:g.height+2*l[1],className:"react-flow__edge-textbg",style:s,rx:c,ry:c}),t.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:f,style:i},r),u):null}));const zo=e=>({width:e.offsetWidth,height:e.offsetHeight}),Do=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),Bo=(e={x:0,y:0},t)=>({x:Do(e.x,t[0][0],t[1][0]),y:Do(e.y,t[0][1],t[1][1])}),$o=(e,t,n)=>e<t?Do(Math.abs(e-t),1,50)/50:e>n?-Do(Math.abs(e-n),1,50)/50:0,To=(e,t)=>[20*$o(e.x,35,t.width-35),20*$o(e.y,35,t.height-35)],Vo=e=>e.getRootNode?.()||window?.document,Ho=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Lo=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Xo=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),Yo=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),Fo=(e,t)=>Xo(Ho(Lo(e),Lo(t))),Wo=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},Zo=e=>!isNaN(e)&&isFinite(e),Ko=Symbol.for("internals"),jo=["Enter"," ","Escape"];function qo(e){const t=((e=>"nativeEvent"in e)(e)?e.nativeEvent:e).composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(t?.nodeName)||t?.hasAttribute("contenteditable")||!!t?.closest(".nokey")}const Uo=e=>"clientX"in e,Go=(e,t)=>{const n=Uo(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},Qo=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,Jo=({id:e,path:n,labelX:o,labelY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g=20})=>t.createElement(t.Fragment,null,t.createElement("path",{id:e,style:d,d:n,fill:"none",className:"react-flow__edge-path",markerEnd:h,markerStart:f}),g&&t.createElement("path",{d:n,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),i&&Zo(o)&&Zo(r)?t.createElement(Io,{x:o,y:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null);Jo.displayName="BaseEdge";function er(e,t,n){return void 0===n?n:o=>{const r=t().edges.find((t=>t.id===e));r&&n(o,{...r})}}function tr({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function nr({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const l=.125*e+.375*r+.375*a+.125*n,c=.125*t+.375*i+.375*s+.125*o;return[l,c,Math.abs(l-e),Math.abs(c-t)]}var or,rr,ir,ar,sr,lr;function cr({pos:t,x1:n,y1:o,x2:r,y2:i}){return t===e.Position.Left||t===e.Position.Right?[.5*(n+r),o]:[n,.5*(o+i)]}function ur({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top}){const[s,l]=cr({pos:o,x1:t,y1:n,x2:r,y2:i}),[c,u]=cr({pos:a,x1:r,y1:i,x2:t,y2:n}),[d,h,f,g]=nr({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${t},${n} C${s},${l} ${c},${u} ${r},${i}`,d,h,f,g]}e.ConnectionMode=void 0,(or=e.ConnectionMode||(e.ConnectionMode={})).Strict="strict",or.Loose="loose",e.PanOnScrollMode=void 0,(rr=e.PanOnScrollMode||(e.PanOnScrollMode={})).Free="free",rr.Vertical="vertical",rr.Horizontal="horizontal",e.SelectionMode=void 0,(ir=e.SelectionMode||(e.SelectionMode={})).Partial="partial",ir.Full="full",e.ConnectionLineType=void 0,(ar=e.ConnectionLineType||(e.ConnectionLineType={})).Bezier="default",ar.Straight="straight",ar.Step="step",ar.SmoothStep="smoothstep",ar.SimpleBezier="simplebezier",e.MarkerType=void 0,(sr=e.MarkerType||(e.MarkerType={})).Arrow="arrow",sr.ArrowClosed="arrowclosed",e.Position=void 0,(lr=e.Position||(e.Position={})).Left="left",lr.Top="top",lr.Right="right",lr.Bottom="bottom";const dr=t.memo((({sourceX:n,sourceY:o,targetX:r,targetY:i,sourcePosition:a=e.Position.Bottom,targetPosition:s=e.Position.Top,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,interactionWidth:y})=>{const[v,b,w]=ur({sourceX:n,sourceY:o,sourcePosition:a,targetX:r,targetY:i,targetPosition:s});return t.createElement(Jo,{path:v,labelX:b,labelY:w,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,interactionWidth:y})}));dr.displayName="SimpleBezierEdge";const hr={[e.Position.Left]:{x:-1,y:0},[e.Position.Right]:{x:1,y:0},[e.Position.Top]:{x:0,y:-1},[e.Position.Bottom]:{x:0,y:1}},fr=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function gr({source:t,sourcePosition:n=e.Position.Bottom,target:o,targetPosition:r=e.Position.Top,center:i,offset:a}){const s=hr[n],l=hr[r],c={x:t.x+s.x*a,y:t.y+s.y*a},u={x:o.x+l.x*a,y:o.y+l.y*a},d=(({source:t,sourcePosition:n=e.Position.Bottom,target:o})=>n===e.Position.Left||n===e.Position.Right?t.x<o.x?{x:1,y:0}:{x:-1,y:0}:t.y<o.y?{x:0,y:1}:{x:0,y:-1})({source:c,sourcePosition:n,target:u}),h=0!==d.x?"x":"y",f=d[h];let g,p,m=[];const y={x:0,y:0},v={x:0,y:0},[b,w,S,x]=tr({sourceX:t.x,sourceY:t.y,targetX:o.x,targetY:o.y});if(s[h]*l[h]==-1){g=i.x??b,p=i.y??w;const e=[{x:g,y:c.y},{x:g,y:u.y}],t=[{x:c.x,y:p},{x:u.x,y:p}];m=s[h]===f?"x"===h?e:t:"x"===h?t:e}else{const e=[{x:c.x,y:u.y}],i=[{x:u.x,y:c.y}];if(m="x"===h?s.x===f?i:e:s.y===f?e:i,n===r){const e=Math.abs(t[h]-o[h]);if(e<=a){const n=Math.min(a-1,a-e);s[h]===f?y[h]=(c[h]>t[h]?-1:1)*n:v[h]=(u[h]>o[h]?-1:1)*n}}if(n!==r){const t="x"===h?"y":"x",n=s[h]===l[t],o=c[t]>u[t],r=c[t]<u[t];(1===s[h]&&(!n&&o||n&&r)||1!==s[h]&&(!n&&r||n&&o))&&(m="x"===h?e:i)}const d={x:c.x+y.x,y:c.y+y.y},b={x:u.x+v.x,y:u.y+v.y};Math.max(Math.abs(d.x-m[0].x),Math.abs(b.x-m[0].x))>=Math.max(Math.abs(d.y-m[0].y),Math.abs(b.y-m[0].y))?(g=(d.x+b.x)/2,p=m[0].y):(g=m[0].x,p=(d.y+b.y)/2)}return[[t,{x:c.x+y.x,y:c.y+y.y},...m,{x:u.x+v.x,y:u.y+v.y},o],g,p,S,x]}function pr({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top,borderRadius:s=5,centerX:l,centerY:c,offset:u=20}){const[d,h,f,g,p]=gr({source:{x:t,y:n},sourcePosition:o,target:{x:r,y:i},targetPosition:a,center:{x:l,y:c},offset:u});return[d.reduce(((e,t,n)=>{let o="";return o=n>0&&n<d.length-1?function(e,t,n,o){const r=Math.min(fr(e,t)/2,fr(t,n)/2,o),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+r*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+r*(e.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(d[n-1],t,d[n+1],s):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=o}),""),h,f,g,p]}const mr=t.memo((({sourceX:n,sourceY:o,targetX:r,targetY:i,label:a,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:d,style:h,sourcePosition:f=e.Position.Bottom,targetPosition:g=e.Position.Top,markerEnd:p,markerStart:m,pathOptions:y,interactionWidth:v})=>{const[b,w,S]=pr({sourceX:n,sourceY:o,sourcePosition:f,targetX:r,targetY:i,targetPosition:g,borderRadius:y?.borderRadius,offset:y?.offset});return t.createElement(Jo,{path:b,labelX:w,labelY:S,label:a,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:d,style:h,markerEnd:p,markerStart:m,interactionWidth:v})}));mr.displayName="SmoothStepEdge";const yr=t.memo((e=>t.createElement(mr,{...e,pathOptions:t.useMemo((()=>({borderRadius:0,offset:e.pathOptions?.offset})),[e.pathOptions?.offset])})));function vr({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,s]=tr({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,s]}yr.displayName="StepEdge";const br=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})=>{const[p,m,y]=vr({sourceX:e,sourceY:n,targetX:o,targetY:r});return t.createElement(Jo,{path:p,labelX:m,labelY:y,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})}));function wr(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function Sr({pos:t,x1:n,y1:o,x2:r,y2:i,c:a}){switch(t){case e.Position.Left:return[n-wr(n-r,a),o];case e.Position.Right:return[n+wr(r-n,a),o];case e.Position.Top:return[n,o-wr(o-i,a)];case e.Position.Bottom:return[n,o+wr(i-o,a)]}}function xr({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top,curvature:s=.25}){const[l,c]=Sr({pos:o,x1:t,y1:n,x2:r,y2:i,c:s}),[u,d]=Sr({pos:a,x1:r,y1:i,x2:t,y2:n,c:s}),[h,f,g,p]=nr({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:l,sourceControlY:c,targetControlX:u,targetControlY:d});return[`M${t},${n} C${l},${c} ${u},${d} ${r},${i}`,h,f,g,p]}br.displayName="StraightEdge";const Er=t.memo((({sourceX:n,sourceY:o,targetX:r,targetY:i,sourcePosition:a=e.Position.Bottom,targetPosition:s=e.Position.Top,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,pathOptions:y,interactionWidth:v})=>{const[b,w,S]=xr({sourceX:n,sourceY:o,sourcePosition:a,targetX:r,targetY:i,targetPosition:s,curvature:y?.curvature});return t.createElement(Jo,{path:b,labelX:w,labelY:S,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,interactionWidth:v})}));Er.displayName="BezierEdge";const Cr=t.createContext(null),_r=Cr.Provider;Cr.Consumer;const Nr=()=>t.useContext(Cr),Mr=e=>"id"in e&&"source"in e&&"target"in e,kr=e=>"id"in e&&!("source"in e)&&!("target"in e),Pr=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,Ar=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`},Or=(e,t)=>{if(!e.source||!e.target)return t;let n;return n=Mr(e)?{...e}:{...e,id:Pr(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:t.concat(n)},Rr=(e,t,n,o={shouldReplaceId:!0})=>{const{id:r,...i}=e;if(!t.source||!t.target)return n;if(!n.find((e=>e.id===r)))return n;const a={...i,id:o.shouldReplaceId?Pr(t):r,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle};return n.filter((e=>e.id!==r)).concat(a)},Ir=({x:e,y:t},[n,o,r],i,[a,s])=>{const l={x:(e-n)/r,y:(t-o)/r};return i?{x:a*Math.round(l.x/a),y:s*Math.round(l.y/s)}:l},zr=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),Dr=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},Br=(e,t=[0,0])=>{if(0===e.length)return{x:0,y:0,width:0,height:0};const n=e.reduce(((e,n)=>{const{x:o,y:r}=Dr(n,t).positionAbsolute;return Ho(e,Lo({x:o,y:r,width:n.width||0,height:n.height||0}))}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Xo(n)},$r=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1,s=[0,0])=>{const l={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},c=[];return e.forEach((e=>{const{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(a&&!o||r)return!1;const{positionAbsolute:u}=Dr(e,s),d={x:u.x,y:u.y,width:t||0,height:n||0},h=Wo(l,d);(void 0===t||void 0===n||null===t||null===n||i&&h>0||h>=(t||0)*(n||0)||e.dragging)&&c.push(e)})),c},Tr=(e,t)=>{const n=e.map((e=>e.id));return t.filter((e=>n.includes(e.source)||n.includes(e.target)))},Vr=(e,t,n,o,r,i=.1)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),l=Math.min(a,s),c=Do(l,o,r);return{x:t/2-(e.x+e.width/2)*c,y:n/2-(e.y+e.height/2)*c,zoom:c}},Hr=(e,t=0)=>e.transition().duration(t);function Lr(e,t,n,o){return(t[n]||[]).reduce(((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t)),[])}const Xr={source:null,target:null,sourceHandle:null,targetHandle:null},Yr=()=>({handleDomNode:null,isValid:!1,connection:Xr,endHandle:null});function Fr(t,n,o,r,i,a,s){const l="target"===i,c=s.querySelector(`.react-flow__handle[data-id="${t?.nodeId}-${t?.id}-${t?.type}"]`),u={...Yr(),handleDomNode:c};if(c){const t=Wr(void 0,c),i=c.getAttribute("data-nodeid"),s=c.getAttribute("data-handleid"),d=c.classList.contains("connectable"),h=c.classList.contains("connectableend"),f={source:l?i:o,sourceHandle:l?s:r,target:l?o:i,targetHandle:l?r:s};u.connection=f;d&&h&&(n===e.ConnectionMode.Strict?l&&"source"===t||!l&&"target"===t:i!==o||s!==r)&&(u.endHandle={nodeId:i,handleId:s,type:t},u.isValid=a(f))}return u}function Wr(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function Zr(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function Kr(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function jr({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:i,setState:a,isValidConnection:s,edgeUpdaterType:l,onReconnectEnd:c}){const u=Vo(e.target),{connectionMode:d,domNode:h,autoPanOnConnect:f,connectionRadius:g,onConnectStart:p,panBy:m,getNodes:y,cancelConnection:v}=i();let b,w=0;const{x:S,y:x}=Go(e),E=u?.elementFromPoint(S,x),C=Wr(l,E),_=h?.getBoundingClientRect();if(!_||!C)return;let N,M=Go(e,_),k=!1,P=null,A=!1,O=null;const R=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce(((e,r)=>{if(r[Ko]){const{handleBounds:i}=r[Ko];let a=[],s=[];i&&(a=Lr(r,i,"source",`${t}-${n}-${o}`),s=Lr(r,i,"target",`${t}-${n}-${o}`)),e.push(...a,...s)}return e}),[])}({nodes:y(),nodeId:n,handleId:t,handleType:C}),I=()=>{if(!f)return;const[e,t]=To(M,_);m({x:e,y:t}),w=requestAnimationFrame(I)};function z(e){const{transform:o}=i();M=Go(e,_);const{handle:l,validHandleResult:c}=function(e,t,n,o,r,i){const{x:a,y:s}=Go(e),l=t.elementsFromPoint(a,s).find((e=>e.classList.contains("react-flow__handle")));if(l){const e=l.getAttribute("data-nodeid");if(e){const t=Wr(void 0,l),o=l.getAttribute("data-handleid"),a=i({nodeId:e,id:o,type:t});if(a){const i=r.find((n=>n.nodeId===e&&n.type===t&&n.id===o));return{handle:{id:o,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let c=[],u=1/0;if(r.forEach((e=>{const t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){const n=i(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}})),!c.length)return{handle:null,validHandleResult:Yr()};if(1===c.length)return c[0];const d=c.some((({validHandleResult:e})=>e.isValid)),h=c.some((({handle:e})=>"target"===e.type));return c.find((({handle:e,validHandleResult:t})=>h?"target"===e.type:!d||t.isValid))||c[0]}(e,u,Ir(M,o,!1,[1,1]),g,R,(e=>Fr(e,d,n,t,r?"target":"source",s,u)));if(b=l,k||(I(),k=!0),O=c.handleDomNode,P=c.connection,A=c.isValid,a({connectionPosition:b&&A?zr({x:b.x,y:b.y},o):M,connectionStatus:Kr(!!b,A),connectionEndHandle:c.endHandle}),!b&&!A&&!O)return Zr(N);P.source!==P.target&&O&&(Zr(N),N=O,O.classList.add("connecting","react-flow__handle-connecting"),O.classList.toggle("valid",A),O.classList.toggle("react-flow__handle-valid",A))}function D(e){(b||O)&&P&&A&&o?.(P),i().onConnectEnd?.(e),l&&c?.(e),Zr(N),v(),cancelAnimationFrame(w),k=!1,A=!1,P=null,O=null,u.removeEventListener("mousemove",z),u.removeEventListener("mouseup",D),u.removeEventListener("touchmove",z),u.removeEventListener("touchend",D)}a({connectionPosition:M,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:C,connectionStartHandle:{nodeId:n,handleId:t,type:C},connectionEndHandle:null}),p?.(e,{nodeId:n,handleId:t,handleType:C}),u.addEventListener("mousemove",z),u.addEventListener("mouseup",D),u.addEventListener("touchmove",z),u.addEventListener("touchend",D)}const qr=()=>!0,Ur=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),Gr=t.forwardRef((({type:n="source",position:r=e.Position.Top,isValidConnection:i,isConnectable:a=!0,isConnectableStart:s=!0,isConnectableEnd:l=!0,id:c,onConnect:u,children:d,className:h,onMouseDown:f,onTouchStart:g,...p},m)=>{const y=c||null,v="target"===n,w=Po(),S=Nr(),{connectOnClick:x,noPanClassName:E}=ko(Ur,b),{connecting:C,clickConnecting:_}=ko(((e,t,n)=>o=>{const{connectionStartHandle:r,connectionEndHandle:i,connectionClickStartHandle:a}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}})(S,y,n),b);S||w.getState().onError?.("010",Co());const N=e=>{const{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=w.getState(),r={...t,...e};if(o){const{edges:e,setEdges:t}=w.getState();t(Or(r,e))}n?.(r),u?.(r)},M=e=>{if(!S)return;const t=Uo(e);s&&(t&&0===e.button||!t)&&jr({event:e,handleId:y,nodeId:S,onConnect:N,isTarget:v,getState:w.getState,setState:w.setState,isValidConnection:i||w.getState().isValidConnection||qr}),t?f?.(e):g?.(e)};return t.createElement("div",{"data-handleid":y,"data-nodeid":S,"data-handlepos":r,"data-id":`${S}-${y}-${n}`,className:o(["react-flow__handle",`react-flow__handle-${r}`,"nodrag",E,h,{source:!v,target:v,connectable:a,connectablestart:s,connectableend:l,connecting:_,connectionindicator:a&&(s&&!C||l&&C)}]),onMouseDown:M,onTouchStart:M,onClick:x?e=>{const{onClickConnectStart:t,onClickConnectEnd:o,connectionClickStartHandle:r,connectionMode:a,isValidConnection:l}=w.getState();if(!S||!r&&!s)return;if(!r)return t?.(e,{nodeId:S,handleId:y,handleType:n}),void w.setState({connectionClickStartHandle:{nodeId:S,type:n,handleId:y}});const c=Vo(e.target),u=i||l||qr,{connection:d,isValid:h}=Fr({nodeId:S,id:y,type:n},a,r.nodeId,r.handleId||null,r.type,u,c);h&&N(d),o?.(e),w.setState({connectionClickStartHandle:null})}:void 0,ref:m,...p},d)}));Gr.displayName="Handle";var Qr=t.memo(Gr);const Jr=({data:n,isConnectable:o,targetPosition:r=e.Position.Top,sourcePosition:i=e.Position.Bottom})=>t.createElement(t.Fragment,null,t.createElement(Qr,{type:"target",position:r,isConnectable:o}),n?.label,t.createElement(Qr,{type:"source",position:i,isConnectable:o}));Jr.displayName="DefaultNode";var ei=t.memo(Jr);const ti=({data:n,isConnectable:o,sourcePosition:r=e.Position.Bottom})=>t.createElement(t.Fragment,null,n?.label,t.createElement(Qr,{type:"source",position:r,isConnectable:o}));ti.displayName="InputNode";var ni=t.memo(ti);const oi=({data:n,isConnectable:o,targetPosition:r=e.Position.Top})=>t.createElement(t.Fragment,null,t.createElement(Qr,{type:"target",position:r,isConnectable:o}),n?.label);oi.displayName="OutputNode";var ri=t.memo(oi);const ii=()=>null;ii.displayName="GroupNode";const ai=e=>({selectedNodes:e.getNodes().filter((e=>e.selected)),selectedEdges:e.edges.filter((e=>e.selected)).map((e=>({...e})))}),si=e=>e.id;function li(e,t){return b(e.selectedNodes.map(si),t.selectedNodes.map(si))&&b(e.selectedEdges.map(si),t.selectedEdges.map(si))}const ci=t.memo((({onSelectionChange:e})=>{const n=Po(),{selectedNodes:o,selectedEdges:r}=ko(ai,li);return t.useEffect((()=>{const t={nodes:o,edges:r};e?.(t),n.getState().onSelectionChange.forEach((e=>e(t)))}),[o,r,e]),null}));ci.displayName="SelectionListener";const ui=e=>!!e.onSelectionChange;function di({onSelectionChange:e}){const n=ko(ui);return e||n?t.createElement(ci,{onSelectionChange:e}):null}const hi=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function fi(e,n){t.useEffect((()=>{void 0!==e&&n(e)}),[e])}function gi(e,n,o){t.useEffect((()=>{void 0!==n&&o({[e]:n})}),[n])}const pi=({nodes:e,edges:n,defaultNodes:o,defaultEdges:r,onConnect:i,onConnectStart:a,onConnectEnd:s,onClickConnectStart:l,onClickConnectEnd:c,nodesDraggable:u,nodesConnectable:d,nodesFocusable:h,edgesFocusable:f,edgesUpdatable:g,elevateNodesOnSelect:p,minZoom:m,maxZoom:y,nodeExtent:v,onNodesChange:w,onEdgesChange:S,elementsSelectable:x,connectionMode:E,snapGrid:C,snapToGrid:_,translateExtent:N,connectOnClick:M,defaultEdgeOptions:k,fitView:P,fitViewOptions:A,onNodesDelete:O,onEdgesDelete:R,onNodeDrag:I,onNodeDragStart:z,onNodeDragStop:D,onSelectionDrag:B,onSelectionDragStart:$,onSelectionDragStop:T,noPanClassName:V,nodeOrigin:H,rfId:L,autoPanOnConnect:X,autoPanOnNodeDrag:Y,onError:F,connectionRadius:W,isValidConnection:Z,nodeDragThreshold:K})=>{const{setNodes:j,setEdges:q,setDefaultNodesAndEdges:U,setMinZoom:G,setMaxZoom:Q,setTranslateExtent:J,setNodeExtent:ee,reset:te}=ko(hi,b),ne=Po();return t.useEffect((()=>{const e=r?.map((e=>({...e,...k})));return U(o,e),()=>{te()}}),[]),gi("defaultEdgeOptions",k,ne.setState),gi("connectionMode",E,ne.setState),gi("onConnect",i,ne.setState),gi("onConnectStart",a,ne.setState),gi("onConnectEnd",s,ne.setState),gi("onClickConnectStart",l,ne.setState),gi("onClickConnectEnd",c,ne.setState),gi("nodesDraggable",u,ne.setState),gi("nodesConnectable",d,ne.setState),gi("nodesFocusable",h,ne.setState),gi("edgesFocusable",f,ne.setState),gi("edgesUpdatable",g,ne.setState),gi("elementsSelectable",x,ne.setState),gi("elevateNodesOnSelect",p,ne.setState),gi("snapToGrid",_,ne.setState),gi("snapGrid",C,ne.setState),gi("onNodesChange",w,ne.setState),gi("onEdgesChange",S,ne.setState),gi("connectOnClick",M,ne.setState),gi("fitViewOnInit",P,ne.setState),gi("fitViewOnInitOptions",A,ne.setState),gi("onNodesDelete",O,ne.setState),gi("onEdgesDelete",R,ne.setState),gi("onNodeDrag",I,ne.setState),gi("onNodeDragStart",z,ne.setState),gi("onNodeDragStop",D,ne.setState),gi("onSelectionDrag",B,ne.setState),gi("onSelectionDragStart",$,ne.setState),gi("onSelectionDragStop",T,ne.setState),gi("noPanClassName",V,ne.setState),gi("nodeOrigin",H,ne.setState),gi("rfId",L,ne.setState),gi("autoPanOnConnect",X,ne.setState),gi("autoPanOnNodeDrag",Y,ne.setState),gi("onError",F,ne.setState),gi("connectionRadius",W,ne.setState),gi("isValidConnection",Z,ne.setState),gi("nodeDragThreshold",K,ne.setState),fi(e,j),fi(n,q),fi(m,G),fi(y,Q),fi(N,J),fi(v,ee),null},mi={display:"none"},yi={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},vi="react-flow__node-desc",bi="react-flow__edge-desc",wi=e=>e.ariaLiveMessage;function Si({rfId:e}){const n=ko(wi);return t.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:yi},n)}function xi({rfId:e,disableKeyboardA11y:n}){return t.createElement(t.Fragment,null,t.createElement("div",{id:`${vi}-${e}`,style:mi},"Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),t.createElement("div",{id:`${bi}-${e}`,style:mi},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!n&&t.createElement(Si,{rfId:e}))}var Ei=(e=null,n={actInsideInputWithModifier:!0})=>{const[o,r]=t.useState(!1),i=t.useRef(!1),a=t.useRef(new Set([])),[s,l]=t.useMemo((()=>{if(null!==e){const t=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.split("+"))),n=t.reduce(((e,t)=>e.concat(...t)),[]);return[t,n]}return[[],[]]}),[e]);return t.useEffect((()=>{const t="undefined"!=typeof document?document:null,o=n?.target||t;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey;if((!i.current||i.current&&!n.actInsideInputWithModifier)&&qo(e))return!1;const t=_i(e.code,l);a.current.add(e[t]),Ci(s,a.current,!1)&&(e.preventDefault(),r(!0))},t=e=>{if((!i.current||i.current&&!n.actInsideInputWithModifier)&&qo(e))return!1;const t=_i(e.code,l);Ci(s,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},c=()=>{a.current.clear(),r(!1)};return o?.addEventListener("keydown",e),o?.addEventListener("keyup",t),window.addEventListener("blur",c),()=>{o?.removeEventListener("keydown",e),o?.removeEventListener("keyup",t),window.removeEventListener("blur",c)}}}),[e,r]),o};function Ci(e,t,n){return e.filter((e=>n||e.length===t.size)).some((e=>e.every((e=>t.has(e)))))}function _i(e,t){return t.includes(e)?"code":"key"}function Ni(e,t,n,o){const r=e.parentNode||e.parentId;if(!r)return n;const i=t.get(r),a=Dr(i,o);return Ni(i,t,{x:(n.x??0)+a.x,y:(n.y??0)+a.y,z:(i[Ko]?.z??0)>(n.z??0)?i[Ko]?.z??0:n.z??0},o)}function Mi(e,t,n){e.forEach((o=>{const r=o.parentNode||o.parentId;if(r&&!e.has(r))throw new Error(`Parent node ${r} not found`);if(r||n?.[o.id]){const{x:r,y:i,z:a}=Ni(o,e,{...o.position,z:o[Ko]?.z??0},t);o.positionAbsolute={x:r,y:i},o[Ko].z=a,n?.[o.id]&&(o[Ko].isParent=!0)}}))}function ki(e,t,n,o){const r=new Map,i={},a=o?1e3:0;return e.forEach((e=>{const n=(Zo(e.zIndex)?e.zIndex:0)+(e.selected?a:0),o=t.get(e.id),s={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},l=e.parentNode||e.parentId;l&&(i[l]=!0);const c=o?.type&&o?.type!==e.type;Object.defineProperty(s,Ko,{enumerable:!1,value:{handleBounds:c?void 0:o?.[Ko]?.handleBounds,z:n}}),r.set(e.id,s)})),Mi(r,n,i),r}function Pi(e,t={}){const{getNodes:n,width:o,height:r,minZoom:i,maxZoom:a,d3Zoom:s,d3Selection:l,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),h=t.initial&&!c&&u;if(s&&l&&(h||!t.initial)){const e=n().filter((e=>{const n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some((t=>t.id===e.id)):n})),c=e.every((e=>e.width&&e.height));if(e.length>0&&c){const n=Br(e,d),{x:c,y:u,zoom:h}=Vr(n,o,r,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=ao.translate(c,u).scale(h);return"number"==typeof t.duration&&t.duration>0?s.transform(Hr(l,t.duration),f):s.transform(l,f),!0}}return!1}function Ai(e,t){return e.forEach((e=>{const n=t.get(e.id);n&&t.set(n.id,{...n,[Ko]:n[Ko],selected:e.selected})})),new Map(t)}function Oi(e,t){return t.map((t=>{const n=e.find((e=>e.id===t.id));return n&&(t.selected=n.selected),t}))}function Ri({changedNodes:e,changedEdges:t,get:n,set:o}){const{nodeInternals:r,edges:i,onNodesChange:a,onEdgesChange:s,hasDefaultNodes:l,hasDefaultEdges:c}=n();e?.length&&(l&&o({nodeInternals:Ai(e,r)}),a?.(e)),t?.length&&(c&&o({edges:Oi(t,i)}),s?.(t))}const Ii=()=>{},zi={zoomIn:Ii,zoomOut:Ii,zoomTo:Ii,getZoom:()=>1,setViewport:Ii,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:Ii,fitBounds:Ii,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},Di=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection});function Bi(){const e=(()=>{const e=Po(),{d3Zoom:n,d3Selection:o}=ko(Di,b),r=t.useMemo((()=>o&&n?{zoomIn:e=>n.scaleBy(Hr(o,e?.duration),1.2),zoomOut:e=>n.scaleBy(Hr(o,e?.duration),1/1.2),zoomTo:(e,t)=>n.scaleTo(Hr(o,t?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(t,r)=>{const[i,a,s]=e.getState().transform,l=ao.translate(t.x??i,t.y??a).scale(t.zoom??s);n.transform(Hr(o,r?.duration),l)},getViewport:()=>{const[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>Pi(e.getState,t),setCenter:(t,r,i)=>{const{width:a,height:s,maxZoom:l}=e.getState(),c=void 0!==i?.zoom?i.zoom:l,u=a/2-t*c,d=s/2-r*c,h=ao.translate(u,d).scale(c);n.transform(Hr(o,i?.duration),h)},fitBounds:(t,r)=>{const{width:i,height:a,minZoom:s,maxZoom:l}=e.getState(),{x:c,y:u,zoom:d}=Vr(t,i,a,s,l,r?.padding??.1),h=ao.translate(c,u).scale(d);n.transform(Hr(o,r?.duration),h)},project:t=>{const{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),Ir(t,n,o,r)},screenToFlowPosition:t=>{const{transform:n,snapToGrid:o,snapGrid:r,domNode:i}=e.getState();if(!i)return t;const{x:a,y:s}=i.getBoundingClientRect(),l={x:t.x-a,y:t.y-s};return Ir(l,n,o,r)},flowToScreenPosition:t=>{const{transform:n,domNode:o}=e.getState();if(!o)return t;const{x:r,y:i}=o.getBoundingClientRect(),a=zr(t,n);return{x:a.x+r,y:a.y+i}},viewportInitialized:!0}:zi),[n,o]);return r})(),n=Po(),o=t.useCallback((()=>n.getState().getNodes().map((e=>({...e})))),[]),r=t.useCallback((e=>n.getState().nodeInternals.get(e)),[]),i=t.useCallback((()=>{const{edges:e=[]}=n.getState();return e.map((e=>({...e})))}),[]),a=t.useCallback((e=>{const{edges:t=[]}=n.getState();return t.find((t=>t.id===e))}),[]),s=t.useCallback((e=>{const{getNodes:t,setNodes:o,hasDefaultNodes:r,onNodesChange:i}=n.getState(),a=t(),s="function"==typeof e?e(a):e;if(r)o(s);else if(i){i(0===s.length?a.map((e=>({type:"remove",id:e.id}))):s.map((e=>({item:e,type:"reset"}))))}}),[]),l=t.useCallback((e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i}=n.getState(),a="function"==typeof e?e(t):e;if(r)o(a);else if(i){i(0===a.length?t.map((e=>({type:"remove",id:e.id}))):a.map((e=>({item:e,type:"reset"}))))}}),[]),c=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:i,onNodesChange:a}=n.getState();if(i){r([...o(),...t])}else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),u=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:i,onEdgesChange:a}=n.getState();if(i)r([...o,...t]);else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),d=t.useCallback((()=>{const{getNodes:e,edges:t=[],transform:o}=n.getState(),[r,i,a]=o;return{nodes:e().map((e=>({...e}))),edges:t.map((e=>({...e}))),viewport:{x:r,y:i,zoom:a}}}),[]),h=t.useCallback((({nodes:e,edges:t})=>{const{nodeInternals:o,getNodes:r,edges:i,hasDefaultNodes:a,hasDefaultEdges:s,onNodesDelete:l,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=n.getState(),h=(e||[]).map((e=>e.id)),f=(t||[]).map((e=>e.id)),g=r().reduce(((e,t)=>{const n=t.parentNode||t.parentId,o=!h.includes(t.id)&&n&&e.find((e=>e.id===n));return("boolean"!=typeof t.deletable||t.deletable)&&(h.includes(t.id)||o)&&e.push(t),e}),[]),p=i.filter((e=>"boolean"!=typeof e.deletable||e.deletable)),m=p.filter((e=>f.includes(e.id)));if(g||m){const e=Tr(g,p),t=[...m,...e],r=t.reduce(((e,t)=>(e.includes(t.id)||e.push(t.id),e)),[]);if((s||a)&&(s&&n.setState({edges:i.filter((e=>!r.includes(e.id)))}),a&&(g.forEach((e=>{o.delete(e.id)})),n.setState({nodeInternals:new Map(o)}))),r.length>0&&(c?.(t),d&&d(r.map((e=>({id:e,type:"remove"}))))),g.length>0&&(l?.(g),u)){u(g.map((e=>({id:e.id,type:"remove"}))))}}}),[]),f=t.useCallback((e=>{const t=Zo((o=e).width)&&Zo(o.height)&&Zo(o.x)&&Zo(o.y);var o;const r=t?null:n.getState().nodeInternals.get(e.id);if(!t&&!r)return[null,null,t];return[t?e:Yo(r),r,t]}),[]),g=t.useCallback(((e,t=!0,o)=>{const[r,i,a]=f(e);return r?(o||n.getState().getNodes()).filter((e=>{if(!(a||e.id!==i.id&&e.positionAbsolute))return!1;const n=Yo(e),o=Wo(n,r);return t&&o>0||o>=r.width*r.height})):[]}),[]),p=t.useCallback(((e,t,n=!0)=>{const[o]=f(e);if(!o)return!1;const r=Wo(o,t);return n&&r>0||r>=o.width*o.height}),[]);return t.useMemo((()=>({...e,getNodes:o,getNode:r,getEdges:i,getEdge:a,setNodes:s,setEdges:l,addNodes:c,addEdges:u,toObject:d,deleteElements:h,getIntersectingNodes:g,isNodeIntersecting:p})),[e,o,r,i,a,s,l,c,u,d,h,g,p])}const $i={actInsideInputWithModifier:!1};const Ti={position:"absolute",width:"100%",height:"100%",top:0,left:0},Vi=e=>({x:e.x,y:e.y,zoom:e.k}),Hi=(e,t)=>e.target.closest(`.${t}`),Li=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Xi=e=>{const t=e.ctrlKey&&Qo()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},Yi=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),Fi=({onMove:n,onMoveStart:o,onMoveEnd:r,onPaneContextMenu:i,zoomOnScroll:a=!0,zoomOnPinch:s=!0,panOnScroll:l=!1,panOnScrollSpeed:c=.5,panOnScrollMode:u=e.PanOnScrollMode.Free,zoomOnDoubleClick:d=!0,elementsSelectable:h,panOnDrag:f=!0,defaultViewport:g,translateExtent:p,minZoom:m,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:w=!0,children:S,noWheelClassName:x,noPanClassName:E})=>{const C=t.useRef(),_=Po(),N=t.useRef(!1),M=t.useRef(!1),k=t.useRef(null),P=t.useRef({x:0,y:0,zoom:0}),{d3Zoom:A,d3Selection:O,d3ZoomHandler:R,userSelectionActive:I}=ko(Yi,b),z=Ei(v),D=t.useRef(0),B=t.useRef(!1),$=t.useRef();return function(e){const n=Po();t.useEffect((()=>{let t;const o=()=>{if(!e.current)return;const t=zo(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",wo()),n.setState({width:t.width||500,height:t.height||500})};return o(),window.addEventListener("resize",o),e.current&&(t=new ResizeObserver((()=>o())),t.observe(e.current)),()=>{window.removeEventListener("resize",o),t&&e.current&&t.unobserve(e.current)}}),[])}(k),t.useEffect((()=>{if(k.current){const e=k.current.getBoundingClientRect(),t=mo().scaleExtent([m,y]).translateExtent(p),n=He(k.current).call(t),o=ao.translate(g.x,g.y).scale(Do(g.zoom,m,y)),r=[[0,0],[e.width,e.height]],i=t.constrain()(o,r,p);t.transform(n,i),t.wheelDelta(Xi),_.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[i.x,i.y,i.k],domNode:k.current.closest(".react-flow")})}}),[]),t.useEffect((()=>{O&&A&&(!l||z||I?void 0!==R&&O.on("wheel.zoom",(function(e,t){if(!w&&"wheel"===e.type&&!e.ctrlKey||Hi(e,x))return null;e.preventDefault(),R.call(this,e,t)}),{passive:!1}):O.on("wheel.zoom",(t=>{if(Hi(t,x))return!1;t.preventDefault(),t.stopImmediatePropagation();const i=O.property("__zoom").k||1;if(t.ctrlKey&&s){const e=Le(t),n=Xi(t),o=i*Math.pow(2,n);return void A.scaleTo(O,o,e,t)}const a=1===t.deltaMode?20:1;let l=u===e.PanOnScrollMode.Vertical?0:t.deltaX*a,d=u===e.PanOnScrollMode.Horizontal?0:t.deltaY*a;!Qo()&&t.shiftKey&&u!==e.PanOnScrollMode.Vertical&&(l=t.deltaY*a,d=0),A.translateBy(O,-l/i*c,-d/i*c,{internal:!0});const h=Vi(O.property("__zoom")),{onViewportChangeStart:f,onViewportChange:g,onViewportChangeEnd:p}=_.getState();clearTimeout($.current),B.current||(B.current=!0,o?.(t,h),f?.(h)),B.current&&(n?.(t,h),g?.(h),$.current=setTimeout((()=>{r?.(t,h),p?.(h),B.current=!1}),150))}),{passive:!1}))}),[I,l,u,O,A,R,z,s,w,x,o,n,r]),t.useEffect((()=>{A&&A.on("start",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;D.current=e.sourceEvent?.button;const{onViewportChangeStart:t}=_.getState(),n=Vi(e.transform);N.current=!0,P.current=n,"mousedown"===e.sourceEvent?.type&&_.setState({paneDragging:!0}),t?.(n),o?.(e.sourceEvent,n)}))}),[A,o]),t.useEffect((()=>{A&&(I&&!N.current?A.on("zoom",null):I||A.on("zoom",(e=>{const{onViewportChange:t}=_.getState();if(_.setState({transform:[e.transform.x,e.transform.y,e.transform.k]}),M.current=!(!i||!Li(f,D.current??0)),(n||t)&&!e.sourceEvent?.internal){const o=Vi(e.transform);t?.(o),n?.(e.sourceEvent,o)}})))}),[I,A,n,f,i]),t.useEffect((()=>{A&&A.on("end",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;const{onViewportChangeEnd:t}=_.getState();if(N.current=!1,_.setState({paneDragging:!1}),i&&Li(f,D.current??0)&&!M.current&&i(e.sourceEvent),M.current=!1,(r||t)&&(n=P.current,o=e.transform,n.x!==o.x||n.y!==o.y||n.zoom!==o.k)){const n=Vi(e.transform);P.current=n,clearTimeout(C.current),C.current=setTimeout((()=>{t?.(n),r?.(e.sourceEvent,n)}),l?150:0)}var n,o}))}),[A,l,f,r,i]),t.useEffect((()=>{A&&A.filter((e=>{const t=z||a,n=s&&e.ctrlKey;if((!0===f||Array.isArray(f)&&f.includes(1))&&1===e.button&&"mousedown"===e.type&&(Hi(e,"react-flow__node")||Hi(e,"react-flow__edge")))return!0;if(!(f||t||l||d||s))return!1;if(I)return!1;if(!d&&"dblclick"===e.type)return!1;if(Hi(e,x)&&"wheel"===e.type)return!1;if(Hi(e,E)&&("wheel"!==e.type||l&&"wheel"===e.type&&!z))return!1;if(!s&&e.ctrlKey&&"wheel"===e.type)return!1;if(!t&&!l&&!n&&"wheel"===e.type)return!1;if(!f&&("mousedown"===e.type||"touchstart"===e.type))return!1;if(Array.isArray(f)&&!f.includes(e.button)&&"mousedown"===e.type)return!1;const o=Array.isArray(f)&&f.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o}))}),[I,A,a,s,l,d,f,h,z]),t.createElement("div",{className:"react-flow__renderer",ref:k,style:Ti},S)},Wi=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function Zi(){const{userSelectionActive:e,userSelectionRect:n}=ko(Wi,b);return e&&n?t.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}function Ki(e,t){const n=t.parentNode||t.parentId,o=e.find((e=>e.id===n));if(o){const e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style}||{},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){const e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){const e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function ji(e,t){if(e.some((e=>"reset"===e.type)))return e.filter((e=>"reset"===e.type)).map((e=>e.item));const n=e.filter((e=>"add"===e.type)).map((e=>e.item));return t.reduce(((t,n)=>{const o=e.filter((e=>e.id===n.id));if(0===o.length)return t.push(n),t;const r={...n};for(const e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&Ki(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&Ki(t,r);break;case"remove":return t}return t.push(r),t}),n)}function qi(e,t){return ji(e,t)}function Ui(e,t){return ji(e,t)}const Gi=(e,t)=>({id:e,type:"select",selected:t});function Qi(e,t){return e.reduce(((e,n)=>{const o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(Gi(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(Gi(n.id,!1))),e}),[])}const Ji=(e,t)=>n=>{n.target===t.current&&e?.(n)},ea=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),ta=t.memo((({isSelecting:n,selectionMode:r=e.SelectionMode.Full,panOnDrag:i,onSelectionStart:a,onSelectionEnd:s,onPaneClick:l,onPaneContextMenu:c,onPaneScroll:u,onPaneMouseEnter:d,onPaneMouseMove:h,onPaneMouseLeave:f,children:g})=>{const p=t.useRef(null),m=Po(),y=t.useRef(0),v=t.useRef(0),w=t.useRef(),{userSelectionActive:S,elementsSelectable:x,dragging:E}=ko(ea,b),C=()=>{m.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,v.current=0},_=e=>{l?.(e),m.getState().resetSelectedElements(),m.setState({nodesSelectionActive:!1})},N=u?e=>u(e):void 0,M=x&&(n||S);return t.createElement("div",{className:o(["react-flow__pane",{dragging:E,selection:n}]),onClick:M?void 0:Ji(_,p),onContextMenu:Ji((e=>{Array.isArray(i)&&i?.includes(2)?e.preventDefault():c?.(e)}),p),onWheel:Ji(N,p),onMouseEnter:M?void 0:d,onMouseDown:M?e=>{const{resetSelectedElements:t,domNode:o}=m.getState();if(w.current=o?.getBoundingClientRect(),!x||!n||0!==e.button||e.target!==p.current||!w.current)return;const{x:r,y:i}=Go(e,w.current);t(),m.setState({userSelectionRect:{width:0,height:0,startX:r,startY:i,x:r,y:i}}),a?.(e)}:void 0,onMouseMove:M?t=>{const{userSelectionRect:o,nodeInternals:i,edges:a,transform:s,onNodesChange:l,onEdgesChange:c,nodeOrigin:u,getNodes:d}=m.getState();if(!n||!w.current||!o)return;m.setState({userSelectionActive:!0,nodesSelectionActive:!1});const h=Go(t,w.current),f=o.startX??0,g=o.startY??0,p={...o,x:h.x<f?h.x:f,y:h.y<g?h.y:g,width:Math.abs(h.x-f),height:Math.abs(h.y-g)},b=d(),S=$r(i,p,s,r===e.SelectionMode.Partial,!0,u),x=Tr(S,a).map((e=>e.id)),E=S.map((e=>e.id));if(y.current!==E.length){y.current=E.length;const e=Qi(b,E);e.length&&l?.(e)}if(v.current!==x.length){v.current=x.length;const e=Qi(a,x);e.length&&c?.(e)}m.setState({userSelectionRect:p})}:h,onMouseUp:M?e=>{if(0!==e.button)return;const{userSelectionRect:t}=m.getState();!S&&t&&e.target===p.current&&_?.(e),m.setState({nodesSelectionActive:y.current>0}),C(),s?.(e)}:void 0,onMouseLeave:M?e=>{S&&(m.setState({nodesSelectionActive:y.current>0}),s?.(e)),C()}:f,ref:p,style:Ti},g,t.createElement(Zi,null))}));function na(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const o=t.get(n);return!!o&&(!!o.selected||na(o,t))}function oa(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)return!1;o=o.parentElement}while(o);return!1}function ra(e,t,n,o){return Array.from(e.values()).filter((n=>(n.selected||n.id===o)&&(!n.parentNode||n.parentId||!na(n,e))&&(n.draggable||t&&void 0===n.draggable))).map((e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:n.x-(e.positionAbsolute?.x??0),y:n.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})))}function ia(e,t,n,o,r=[0,0],i){const a=function(e,t){return t&&"parent"!==t?[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]:t}(e,e.extent||o);let s=a;const l=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&l&&"parent"!==e.extent){const t=n.get(l),{x:o,y:i}=Dr(t,r).positionAbsolute;s=[[e.extent[0][0]+o,e.extent[0][1]+i],[e.extent[1][0]+o,e.extent[1][1]+i]]}}else if(l&&e.width&&e.height){const t=n.get(l),{x:o,y:i}=Dr(t,r).positionAbsolute;s=t&&Zo(o)&&Zo(i)&&Zo(t.width)&&Zo(t.height)?[[o+e.width*r[0],i+e.height*r[1]],[o+t.width-e.width+e.width*r[0],i+t.height-e.height+e.height*r[1]]]:s}else i?.("005",So()),s=a;let c={x:0,y:0};if(l){const e=n.get(l);c=Dr(e,r).positionAbsolute}const u=s&&"parent"!==s?Bo(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function aa({nodeId:e,dragItems:t,nodeInternals:n}){const o=t.map((e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute})));return[e?o.find((t=>t.id===e)):o[0],o]}ta.displayName="Pane";const sa=(e,t,n,o)=>{const r=t.querySelectorAll(e);if(!r||!r.length)return null;const i=Array.from(r),a=t.getBoundingClientRect(),s=a.width*o[0],l=a.height*o[1];return i.map((e=>{const t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-s)/n,y:(t.top-a.top-l)/n,...zo(e)}}))};function la(e,t,n){return void 0===n?n:o=>{const r=t().nodeInternals.get(e);r&&n(o,{...r})}}function ca({id:e,store:t,unselect:n=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:s,onError:l}=t.getState(),c=s.get(e);c?(t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",No(e))}function ua(){const e=Po(),n=t.useCallback((({sourceEvent:t})=>{const{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,s={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(s.x/o[0]):s.x,ySnapped:r?o[1]*Math.round(s.y/o[1]):s.y,...s}}),[]);return n}function da(e){return(t,n,o)=>e?.(t,o)}function ha({nodeRef:e,disabled:n=!1,noDragClassName:o,handleSelector:r,nodeId:i,isSelectable:a,selectNodesOnDrag:s}){const l=Po(),[c,u]=t.useState(!1),d=t.useRef([]),h=t.useRef({x:null,y:null}),f=t.useRef(0),g=t.useRef(null),p=t.useRef({x:0,y:0}),m=t.useRef(null),y=t.useRef(!1),v=t.useRef(!1),b=t.useRef(!1),w=ua();return t.useEffect((()=>{if(e?.current){const t=He(e.current),c=({x:e,y:t})=>{const{nodeInternals:n,onNodeDrag:o,onSelectionDrag:r,updateNodePositions:a,nodeExtent:s,snapGrid:c,snapToGrid:f,nodeOrigin:g,onError:p}=l.getState();h.current={x:e,y:t};let y=!1,v={x:0,y:0,x2:0,y2:0};if(d.current.length>1&&s){const e=Br(d.current,g);v=Lo(e)}if(d.current=d.current.map((o=>{const r={x:e-o.distance.x,y:t-o.distance.y};f&&(r.x=c[0]*Math.round(r.x/c[0]),r.y=c[1]*Math.round(r.y/c[1]));const i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];d.current.length>1&&s&&!o.extent&&(i[0][0]=o.positionAbsolute.x-v.x+s[0][0],i[1][0]=o.positionAbsolute.x+(o.width??0)-v.x2+s[1][0],i[0][1]=o.positionAbsolute.y-v.y+s[0][1],i[1][1]=o.positionAbsolute.y+(o.height??0)-v.y2+s[1][1]);const a=ia(o,r,n,i,g,p);return y=y||o.position.x!==a.position.x||o.position.y!==a.position.y,o.position=a.position,o.positionAbsolute=a.positionAbsolute,o})),!y)return;a(d.current,!0,!0),u(!0);const b=i?o:da(r);if(b&&m.current){const[e,t]=aa({nodeId:i,dragItems:d.current,nodeInternals:n});b(m.current,e,t)}},S=()=>{if(!g.current)return;const[e,t]=To(p.current,g.current);if(0!==e||0!==t){const{transform:n,panBy:o}=l.getState();h.current.x=(h.current.x??0)-e/n[2],h.current.y=(h.current.y??0)-t/n[2],o({x:e,y:t})&&c(h.current)}f.current=requestAnimationFrame(S)},x=t=>{const{nodeInternals:n,multiSelectionActive:o,nodesDraggable:r,unselectNodesAndEdges:c,onNodeDragStart:u,onSelectionDragStart:f}=l.getState();v.current=!0;const g=i?u:da(f);s&&a||o||!i||n.get(i)?.selected||c(),i&&a&&s&&ca({id:i,store:l,nodeRef:e});const p=w(t);if(h.current=p,d.current=ra(n,r,p,i),g&&d.current){const[e,o]=aa({nodeId:i,dragItems:d.current,nodeInternals:n});g(t.sourceEvent,e,o)}};if(!n){const n=et().on("start",(e=>{const{domNode:t,nodeDragThreshold:n}=l.getState();0===n&&x(e),b.current=!1;const o=w(e);h.current=o,g.current=t?.getBoundingClientRect()||null,p.current=Go(e.sourceEvent,g.current)})).on("drag",(e=>{const t=w(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=l.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(b.current=!0),!b.current){if(!y.current&&v.current&&n&&(y.current=!0,S()),!v.current){const n=t.xSnapped-(h?.current?.x??0),r=t.ySnapped-(h?.current?.y??0);Math.sqrt(n*n+r*r)>o&&x(e)}(h.current.x!==t.xSnapped||h.current.y!==t.ySnapped)&&d.current&&v.current&&(m.current=e.sourceEvent,p.current=Go(e.sourceEvent,g.current),c(t))}})).on("end",(e=>{if(v.current&&!b.current&&(u(!1),y.current=!1,v.current=!1,cancelAnimationFrame(f.current),d.current)){const{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:r}=l.getState(),a=i?o:da(r);if(t(d.current,!1,!1),a){const[t,o]=aa({nodeId:i,dragItems:d.current,nodeInternals:n});a(e.sourceEvent,t,o)}}})).filter((t=>{const n=t.target;return!t.button&&(!o||!oa(n,`.${o}`,e))&&(!r||oa(n,r,e))}));return t.call(n),()=>{t.on(".drag",null)}}t.on(".drag",null)}}),[e,n,o,r,a,l,i,s,w]),c}function fa(){const e=Po();return t.useCallback((t=>{const{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:i,snapToGrid:a,snapGrid:s,onError:l,nodesDraggable:c}=e.getState(),u=i().filter((e=>e.selected&&(e.draggable||c&&void 0===e.draggable))),d=a?s[0]:5,h=a?s[1]:5,f=t.isShiftPressed?4:1,g=t.x*d*f,p=t.y*h*f;r(u.map((e=>{if(e.positionAbsolute){const t={x:e.positionAbsolute.x+g,y:e.positionAbsolute.y+p};a&&(t.x=s[0]*Math.round(t.x/s[0]),t.y=s[1]*Math.round(t.y/s[1]));const{positionAbsolute:r,position:i}=ia(e,t,n,o,void 0,l);e.position=i,e.positionAbsolute=r}return e})),!0,!1)}),[])}const ga={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var pa=e=>{const n=({id:n,type:r,data:i,xPos:a,yPos:s,xPosOrigin:l,yPosOrigin:c,selected:u,onClick:d,onMouseEnter:h,onMouseMove:f,onMouseLeave:g,onContextMenu:p,onDoubleClick:m,style:y,className:v,isDraggable:b,isSelectable:w,isConnectable:S,isFocusable:x,selectNodesOnDrag:E,sourcePosition:C,targetPosition:_,hidden:N,resizeObserver:M,dragHandle:k,zIndex:P,isParent:A,noDragClassName:O,noPanClassName:R,initialized:I,disableKeyboardA11y:z,ariaLabel:D,rfId:B,hasHandleBounds:$})=>{const T=Po(),V=t.useRef(null),H=t.useRef(null),L=t.useRef(C),X=t.useRef(_),Y=t.useRef(r),F=w||b||d||h||f||g,W=fa(),Z=la(n,T.getState,h),K=la(n,T.getState,f),j=la(n,T.getState,g),q=la(n,T.getState,p),U=la(n,T.getState,m);t.useEffect((()=>()=>{H.current&&(M?.unobserve(H.current),H.current=null)}),[]),t.useEffect((()=>{if(V.current&&!N){const e=V.current;I&&$&&H.current===e||(H.current&&M?.unobserve(H.current),M?.observe(e),H.current=e)}}),[N,I,$]),t.useEffect((()=>{const e=Y.current!==r,t=L.current!==C,o=X.current!==_;V.current&&(e||t||o)&&(e&&(Y.current=r),t&&(L.current=C),o&&(X.current=_),T.getState().updateNodeDimensions([{id:n,nodeElement:V.current,forceUpdate:!0}]))}),[n,r,C,_]);const G=ha({nodeRef:V,disabled:N||!b,noDragClassName:O,handleSelector:k,nodeId:n,isSelectable:w,selectNodesOnDrag:E});return N?null:t.createElement("div",{className:o(["react-flow__node",`react-flow__node-${r}`,{[R]:b},v,{selected:u,selectable:w,parent:A,dragging:G}]),ref:V,style:{zIndex:P,transform:`translate(${l}px,${c}px)`,pointerEvents:F?"all":"none",visibility:I?"visible":"hidden",...y},"data-id":n,"data-testid":`rf__node-${n}`,onMouseEnter:Z,onMouseMove:K,onMouseLeave:j,onContextMenu:q,onClick:e=>{const{nodeDragThreshold:t}=T.getState();if(w&&(!E||!b||t>0)&&ca({id:n,store:T,nodeRef:V}),d){const t=T.getState().nodeInternals.get(n);t&&d(e,{...t})}},onDoubleClick:U,onKeyDown:x?e=>{if(!qo(e)&&!z)if(jo.includes(e.key)&&w){const t="Escape"===e.key;ca({id:n,store:T,unselect:t,nodeRef:V})}else b&&u&&Object.prototype.hasOwnProperty.call(ga,e.key)&&(T.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~a}, y: ${~~s}`}),W({x:ga[e.key].x,y:ga[e.key].y,isShiftPressed:e.shiftKey}))}:void 0,tabIndex:x?0:void 0,role:x?"button":void 0,"aria-describedby":z?void 0:`${vi}-${B}`,"aria-label":D},t.createElement(_r,{value:n},t.createElement(e,{id:n,data:i,type:r,xPos:a,yPos:s,selected:u,isConnectable:S,sourcePosition:C,targetPosition:_,dragging:G,dragHandle:k,zIndex:P})))};return n.displayName="NodeWrapper",t.memo(n)};const ma=e=>{const t=e.getNodes().filter((e=>e.selected));return{...Br(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};var ya=t.memo((function({onSelectionContextMenu:e,noPanClassName:n,disableKeyboardA11y:r}){const i=Po(),{width:a,height:s,x:l,y:c,transformString:u,userSelectionActive:d}=ko(ma,b),h=fa(),f=t.useRef(null);if(t.useEffect((()=>{r||f.current?.focus({preventScroll:!0})}),[r]),ha({nodeRef:f}),d||!a||!s)return null;const g=e?t=>{const n=i.getState().getNodes().filter((e=>e.selected));e(t,n)}:void 0;return t.createElement("div",{className:o(["react-flow__nodesselection","react-flow__container",n]),style:{transform:u}},t.createElement("div",{ref:f,className:"react-flow__nodesselection-rect",onContextMenu:g,tabIndex:r?void 0:-1,onKeyDown:r?void 0:e=>{Object.prototype.hasOwnProperty.call(ga,e.key)&&h({x:ga[e.key].x,y:ga[e.key].y,isShiftPressed:e.shiftKey})},style:{width:a,height:s,top:c,left:l}}))}));const va=e=>e.nodesSelectionActive,ba=({children:e,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,deleteKeyCode:l,onMove:c,onMoveStart:u,onMoveEnd:d,selectionKeyCode:h,selectionOnDrag:f,selectionMode:g,onSelectionStart:p,onSelectionEnd:m,multiSelectionKeyCode:y,panActivationKeyCode:v,zoomActivationKeyCode:b,elementsSelectable:w,zoomOnScroll:S,zoomOnPinch:x,panOnScroll:E,panOnScrollSpeed:C,panOnScrollMode:_,zoomOnDoubleClick:N,panOnDrag:M,defaultViewport:k,translateExtent:P,minZoom:A,maxZoom:O,preventScrolling:R,onSelectionContextMenu:I,noWheelClassName:z,noPanClassName:D,disableKeyboardA11y:B})=>{const $=ko(va),T=Ei(h),V=Ei(v),H=V||M,L=V||E,X=T||f&&!0!==H;return(({deleteKeyCode:e,multiSelectionKeyCode:n})=>{const o=Po(),{deleteElements:r}=Bi(),i=Ei(e,$i),a=Ei(n);t.useEffect((()=>{if(i){const{edges:e,getNodes:t}=o.getState(),n=t().filter((e=>e.selected)),i=e.filter((e=>e.selected));r({nodes:n,edges:i}),o.setState({nodesSelectionActive:!1})}}),[i]),t.useEffect((()=>{o.setState({multiSelectionActive:a})}),[a])})({deleteKeyCode:l,multiSelectionKeyCode:y}),t.createElement(Fi,{onMove:c,onMoveStart:u,onMoveEnd:d,onPaneContextMenu:a,elementsSelectable:w,zoomOnScroll:S,zoomOnPinch:x,panOnScroll:L,panOnScrollSpeed:C,panOnScrollMode:_,zoomOnDoubleClick:N,panOnDrag:!T&&H,defaultViewport:k,translateExtent:P,minZoom:A,maxZoom:O,zoomActivationKeyCode:b,preventScrolling:R,noWheelClassName:z,noPanClassName:D},t.createElement(ta,{onSelectionStart:p,onSelectionEnd:m,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,panOnDrag:H,isSelecting:!!X,selectionMode:g},e,$&&t.createElement(ya,{onSelectionContextMenu:I,noPanClassName:D,disableKeyboardA11y:B})))};ba.displayName="FlowRenderer";var wa=t.memo(ba);function Sa(e){return{...{input:pa(e.input||ni),default:pa(e.default||ei),output:pa(e.output||ri),group:pa(e.group||ii)},...Object.keys(e).filter((e=>!["input","default","output","group"].includes(e))).reduce(((t,n)=>(t[n]=pa(e[n]||ei),t)),{})}}const xa=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),Ea=n=>{const{nodesDraggable:o,nodesConnectable:r,nodesFocusable:i,elementsSelectable:a,updateNodeDimensions:s,onError:l}=ko(xa,b),c=(u=n.onlyRenderVisibleElements,ko(t.useCallback((e=>u?$r(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes()),[u])));var u;const d=t.useRef(),h=t.useMemo((()=>{if("undefined"==typeof ResizeObserver)return null;const e=new ResizeObserver((e=>{const t=e.map((e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})));s(t)}));return d.current=e,e}),[]);return t.useEffect((()=>()=>{d?.current?.disconnect()}),[]),t.createElement("div",{className:"react-flow__nodes",style:Ti},c.map((s=>{let c=s.type||"default";n.nodeTypes[c]||(l?.("003",bo(c)),c="default");const u=n.nodeTypes[c]||n.nodeTypes.default,d=!!(s.draggable||o&&void 0===s.draggable),f=!!(s.selectable||a&&void 0===s.selectable),g=!!(s.connectable||r&&void 0===s.connectable),p=!!(s.focusable||i&&void 0===s.focusable),m=n.nodeExtent?Bo(s.positionAbsolute,n.nodeExtent):s.positionAbsolute,y=m?.x??0,v=m?.y??0,b=(({x:e,y:t,width:n,height:o,origin:r})=>n&&o?r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]}:{x:e,y:t})({x:y,y:v,width:s.width??0,height:s.height??0,origin:n.nodeOrigin});return t.createElement(u,{key:s.id,id:s.id,className:s.className,style:s.style,type:c,data:s.data,sourcePosition:s.sourcePosition||e.Position.Bottom,targetPosition:s.targetPosition||e.Position.Top,hidden:s.hidden,xPos:y,yPos:v,xPosOrigin:b.x,yPosOrigin:b.y,selectNodesOnDrag:n.selectNodesOnDrag,onClick:n.onNodeClick,onMouseEnter:n.onNodeMouseEnter,onMouseMove:n.onNodeMouseMove,onMouseLeave:n.onNodeMouseLeave,onContextMenu:n.onNodeContextMenu,onDoubleClick:n.onNodeDoubleClick,selected:!!s.selected,isDraggable:d,isSelectable:f,isConnectable:g,isFocusable:p,resizeObserver:h,dragHandle:s.dragHandle,zIndex:s[Ko]?.z??0,isParent:!!s[Ko]?.isParent,noDragClassName:n.noDragClassName,noPanClassName:n.noPanClassName,initialized:!!s.width&&!!s.height,rfId:n.rfId,disableKeyboardA11y:n.disableKeyboardA11y,ariaLabel:s.ariaLabel,hasHandleBounds:!!s[Ko]?.handleBounds})})))};Ea.displayName="NodeRenderer";var Ca=t.memo(Ea);const _a=(t,n,o)=>o===e.Position.Left?t-n:o===e.Position.Right?t+n:t,Na=(t,n,o)=>o===e.Position.Top?t-n:o===e.Position.Bottom?t+n:t,Ma="react-flow__edgeupdater",ka=({position:e,centerX:n,centerY:r,radius:i=10,onMouseDown:a,onMouseEnter:s,onMouseOut:l,type:c})=>t.createElement("circle",{onMouseDown:a,onMouseEnter:s,onMouseOut:l,className:o([Ma,`${Ma}-${c}`]),cx:_a(n,i,e),cy:Na(r,i,e),r:i,stroke:"transparent",fill:"transparent"}),Pa=()=>!0;var Aa=e=>{const n=({id:n,className:r,type:i,data:a,onClick:s,onEdgeDoubleClick:l,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:y,source:v,target:b,sourceX:w,sourceY:S,targetX:x,targetY:E,sourcePosition:C,targetPosition:_,elementsSelectable:N,hidden:M,sourceHandleId:k,targetHandleId:P,onContextMenu:A,onMouseEnter:O,onMouseMove:R,onMouseLeave:I,reconnectRadius:z,onReconnect:D,onReconnectStart:B,onReconnectEnd:$,markerEnd:T,markerStart:V,rfId:H,ariaLabel:L,isFocusable:X,isReconnectable:Y,pathOptions:F,interactionWidth:W,disableKeyboardA11y:Z})=>{const K=t.useRef(null),[j,q]=t.useState(!1),[U,G]=t.useState(!1),Q=Po(),J=t.useMemo((()=>`url('#${Ar(V,H)}')`),[V,H]),ee=t.useMemo((()=>`url('#${Ar(T,H)}')`),[T,H]);if(M)return null;const te=er(n,Q.getState,l),ne=er(n,Q.getState,A),oe=er(n,Q.getState,O),re=er(n,Q.getState,R),ie=er(n,Q.getState,I),ae=(e,t)=>{if(0!==e.button)return;const{edges:o,isValidConnection:r}=Q.getState(),i=t?b:v,a=(t?P:k)||null,s=t?"target":"source",l=r||Pa,c=t,u=o.find((e=>e.id===n));G(!0),B?.(e,u,s);jr({event:e,handleId:a,nodeId:i,onConnect:e=>D?.(u,e),isTarget:c,getState:Q.getState,setState:Q.setState,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:e=>{G(!1),$?.(e,u,s)}})},se=()=>q(!0),le=()=>q(!1),ce=!N&&!s;return t.createElement("g",{className:o(["react-flow__edge",`react-flow__edge-${i}`,r,{selected:c,animated:u,inactive:ce,updating:j}]),onClick:e=>{const{edges:t,addSelectedEdges:o,unselectNodesAndEdges:r,multiSelectionActive:i}=Q.getState(),a=t.find((e=>e.id===n));a&&(N&&(Q.setState({nodesSelectionActive:!1}),a.selected&&i?(r({nodes:[],edges:[a]}),K.current?.blur()):o([n])),s&&s(e,a))},onDoubleClick:te,onContextMenu:ne,onMouseEnter:oe,onMouseMove:re,onMouseLeave:ie,onKeyDown:X?e=>{if(!Z&&jo.includes(e.key)&&N){const{unselectNodesAndEdges:t,addSelectedEdges:o,edges:r}=Q.getState();"Escape"===e.key?(K.current?.blur(),t({edges:[r.find((e=>e.id===n))]})):o([n])}}:void 0,tabIndex:X?0:void 0,role:X?"button":"img","data-testid":`rf__edge-${n}`,"aria-label":null===L?void 0:L||`Edge from ${v} to ${b}`,"aria-describedby":X?`${bi}-${H}`:void 0,ref:K},!U&&t.createElement(e,{id:n,source:v,target:b,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,data:a,style:y,sourceX:w,sourceY:S,targetX:x,targetY:E,sourcePosition:C,targetPosition:_,sourceHandleId:k,targetHandleId:P,markerStart:J,markerEnd:ee,pathOptions:F,interactionWidth:W}),Y&&t.createElement(t.Fragment,null,("source"===Y||!0===Y)&&t.createElement(ka,{position:C,centerX:w,centerY:S,radius:z,onMouseDown:e=>ae(e,!0),onMouseEnter:se,onMouseOut:le,type:"source"}),("target"===Y||!0===Y)&&t.createElement(ka,{position:_,centerX:x,centerY:E,radius:z,onMouseDown:e=>ae(e,!1),onMouseEnter:se,onMouseOut:le,type:"target"})))};return n.displayName="EdgeWrapper",t.memo(n)};function Oa(e){return{...{default:Aa(e.default||Er),straight:Aa(e.bezier||br),step:Aa(e.step||yr),smoothstep:Aa(e.step||mr),simplebezier:Aa(e.simplebezier||dr)},...Object.keys(e).filter((e=>!["default","bezier"].includes(e))).reduce(((t,n)=>(t[n]=Aa(e[n]||Er),t)),{})}}function Ra(t,n,o=null){const r=(o?.x||0)+n.x,i=(o?.y||0)+n.y,a=o?.width||n.width,s=o?.height||n.height;switch(t){case e.Position.Top:return{x:r+a/2,y:i};case e.Position.Right:return{x:r+a,y:i+s/2};case e.Position.Bottom:return{x:r+a/2,y:i+s};case e.Position.Left:return{x:r,y:i+s/2}}}function Ia(e,t){return e?1!==e.length&&t?t&&e.find((e=>e.id===t))||null:e[0]:null}function za(e){const t=e?.[Ko]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}const Da=[{level:0,isMaxLevel:!0,edges:[]}];function Ba(e,n,o){return function(e,t,n=!1){let o=-1;const r=e.reduce(((e,r)=>{const i=Zo(r.zIndex);let a=i?r.zIndex:0;if(n){const e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,s=Math.max(n?.[Ko]?.z||0,e?.[Ko]?.z||0,1e3);a=(i?r.zIndex:0)+(o?s:0)}return e[a]?e[a].push(r):e[a]=[r],o=a>o?a:o,e}),{}),i=Object.entries(r).map((([e,t])=>{const n=+e;return{edges:t,level:n,isMaxLevel:n===o}}));return 0===i.length?Da:i}(ko(t.useCallback((t=>e?t.edges.filter((e=>{const o=n.get(e.source),r=n.get(e.target);return o?.width&&o?.height&&r?.width&&r?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:i,width:a,height:s,transform:l}){const c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+i)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);const u=Lo({x:(0-l[0])/l[2],y:(0-l[1])/l[2],width:a/l[2],height:s/l[2]}),d=Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x)),h=Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y));return Math.ceil(d*h)>0}({sourcePos:o.positionAbsolute||{x:0,y:0},targetPos:r.positionAbsolute||{x:0,y:0},sourceWidth:o.width,sourceHeight:o.height,targetWidth:r.width,targetHeight:r.height,width:t.width,height:t.height,transform:t.transform})})):t.edges),[e,n])),n,o)}const $a={[e.MarkerType.Arrow]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[e.MarkerType.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const Ta=({id:e,type:n,color:o,width:r=12.5,height:i=12.5,markerUnits:a="strokeWidth",strokeWidth:s,orient:l="auto-start-reverse"})=>{const c=function(e){const n=Po();return t.useMemo((()=>Object.prototype.hasOwnProperty.call($a,e)?$a[e]:(n.getState().onError?.("009",xo(e)),null)),[e])}(n);return c?t.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${i}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:l,refX:"0",refY:"0"},t.createElement(c,{color:o,strokeWidth:s})):null},Va=({defaultColor:e,rfId:n})=>{const o=ko(t.useCallback((({defaultColor:e,rfId:t})=>n=>{const o=[];return n.edges.reduce(((n,r)=>([r.markerStart,r.markerEnd].forEach((r=>{if(r&&"object"==typeof r){const i=Ar(r,t);o.includes(i)||(n.push({id:i,color:r.color||e,...r}),o.push(i))}})),n)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))})({defaultColor:e,rfId:n}),[e,n]),((e,t)=>!(e.length!==t.length||e.some(((e,n)=>e.id!==t[n].id)))));return t.createElement("defs",null,o.map((e=>t.createElement(Ta,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient}))))};Va.displayName="MarkerDefinitions";var Ha=t.memo(Va);const La=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),Xa=({defaultMarkerColor:n,onlyRenderVisibleElements:r,elevateEdgesOnSelect:i,rfId:a,edgeTypes:s,noPanClassName:l,onEdgeContextMenu:c,onEdgeMouseEnter:u,onEdgeMouseMove:d,onEdgeMouseLeave:h,onEdgeClick:f,onEdgeDoubleClick:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,reconnectRadius:v,children:w,disableKeyboardA11y:S})=>{const{edgesFocusable:x,edgesUpdatable:E,elementsSelectable:C,width:_,height:N,connectionMode:M,nodeInternals:k,onError:P}=ko(La,b),A=Ba(r,k,i);return _?t.createElement(t.Fragment,null,A.map((({level:r,edges:i,isMaxLevel:b})=>t.createElement("svg",{key:r,style:{zIndex:r},width:_,height:N,className:"react-flow__edges react-flow__container"},b&&t.createElement(Ha,{defaultColor:n,rfId:a}),t.createElement("g",null,i.map((n=>{const[r,i,b]=za(k.get(n.source)),[w,_,N]=za(k.get(n.target));if(!b||!N)return null;let A=n.type||"default";s[A]||(P?.("011",_o(A)),A="default");const O=s[A]||s.default,R=M===e.ConnectionMode.Strict?_.target:(_.target??[]).concat(_.source??[]),I=Ia(i.source,n.sourceHandle),z=Ia(R,n.targetHandle),D=I?.position||e.Position.Bottom,B=z?.position||e.Position.Top,$=!!(n.focusable||x&&void 0===n.focusable),T=n.reconnectable||n.updatable,V=void 0!==p&&(T||E&&void 0===T);if(!I||!z)return P?.("008",Eo(I,n)),null;const{sourceX:H,sourceY:L,targetX:X,targetY:Y}=((e,t,n,o,r,i)=>{const a=Ra(n,e,t),s=Ra(i,o,r);return{sourceX:a.x,sourceY:a.y,targetX:s.x,targetY:s.y}})(r,I,D,w,z,B);return t.createElement(O,{key:n.id,id:n.id,className:o([n.className,l]),type:A,data:n.data,selected:!!n.selected,animated:!!n.animated,hidden:!!n.hidden,label:n.label,labelStyle:n.labelStyle,labelShowBg:n.labelShowBg,labelBgStyle:n.labelBgStyle,labelBgPadding:n.labelBgPadding,labelBgBorderRadius:n.labelBgBorderRadius,style:n.style,source:n.source,target:n.target,sourceHandleId:n.sourceHandle,targetHandleId:n.targetHandle,markerEnd:n.markerEnd,markerStart:n.markerStart,sourceX:H,sourceY:L,targetX:X,targetY:Y,sourcePosition:D,targetPosition:B,elementsSelectable:C,onContextMenu:c,onMouseEnter:u,onMouseMove:d,onMouseLeave:h,onClick:f,onEdgeDoubleClick:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,reconnectRadius:v,rfId:a,ariaLabel:n.ariaLabel,isFocusable:$,isReconnectable:V,pathOptions:"pathOptions"in n?n.pathOptions:void 0,interactionWidth:n.interactionWidth,disableKeyboardA11y:S})})))))),w):null};Xa.displayName="EdgeRenderer";var Ya=t.memo(Xa);const Fa=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function Wa({children:e}){const n=ko(Fa);return t.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:n}},e)}const Za={[e.Position.Left]:e.Position.Right,[e.Position.Right]:e.Position.Left,[e.Position.Top]:e.Position.Bottom,[e.Position.Bottom]:e.Position.Top},Ka=({nodeId:n,handleType:o,style:r,type:i=e.ConnectionLineType.Bezier,CustomComponent:a,connectionStatus:s})=>{const{fromNode:l,handleId:c,toX:u,toY:d,connectionMode:h}=ko(t.useCallback((e=>({fromNode:e.nodeInternals.get(n),handleId:e.connectionHandleId,toX:(e.connectionPosition.x-e.transform[0])/e.transform[2],toY:(e.connectionPosition.y-e.transform[1])/e.transform[2],connectionMode:e.connectionMode})),[n]),b),f=l?.[Ko]?.handleBounds;let g=f?.[o];if(h===e.ConnectionMode.Loose&&(g=g||f?.["source"===o?"target":"source"]),!l||!g)return null;const p=c?g.find((e=>e.id===c)):g[0],m=p?p.x+p.width/2:(l.width??0)/2,y=p?p.y+p.height/2:l.height??0,v=(l.positionAbsolute?.x??0)+m,w=(l.positionAbsolute?.y??0)+y,S=p?.position,x=S?Za[S]:null;if(!S||!x)return null;if(a)return t.createElement(a,{connectionLineType:i,connectionLineStyle:r,fromNode:l,fromHandle:p,fromX:v,fromY:w,toX:u,toY:d,fromPosition:S,toPosition:x,connectionStatus:s});let E="";const C={sourceX:v,sourceY:w,sourcePosition:S,targetX:u,targetY:d,targetPosition:x};return i===e.ConnectionLineType.Bezier?[E]=xr(C):i===e.ConnectionLineType.Step?[E]=pr({...C,borderRadius:0}):i===e.ConnectionLineType.SmoothStep?[E]=pr(C):i===e.ConnectionLineType.SimpleBezier?[E]=ur(C):E=`M${v},${w} ${u},${d}`,t.createElement("path",{d:E,fill:"none",className:"react-flow__connection-path",style:r})};Ka.displayName="ConnectionLine";const ja=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function qa({containerStyle:e,style:n,type:r,component:i}){const{nodeId:a,handleType:s,nodesConnectable:l,width:c,height:u,connectionStatus:d}=ko(ja,b);return!!(a&&s&&c&&l)?t.createElement("svg",{style:e,width:c,height:u,className:"react-flow__edges react-flow__connectionline react-flow__container"},t.createElement("g",{className:o(["react-flow__connection",d])},t.createElement(Ka,{nodeId:a,handleType:s,style:n,type:r,CustomComponent:i,connectionStatus:d}))):null}function Ua(e,n){t.useRef(null),Po();return t.useMemo((()=>n(e)),[e])}const Ga=({nodeTypes:e,edgeTypes:n,onMove:o,onMoveStart:r,onMoveEnd:i,onInit:a,onNodeClick:s,onEdgeClick:l,onNodeDoubleClick:c,onEdgeDoubleClick:u,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,onSelectionContextMenu:p,onSelectionStart:m,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:b,connectionLineComponent:w,connectionLineContainerStyle:S,selectionKeyCode:x,selectionOnDrag:E,selectionMode:C,multiSelectionKeyCode:_,panActivationKeyCode:N,zoomActivationKeyCode:M,deleteKeyCode:k,onlyRenderVisibleElements:P,elementsSelectable:A,selectNodesOnDrag:O,defaultViewport:R,translateExtent:I,minZoom:z,maxZoom:D,preventScrolling:B,defaultMarkerColor:$,zoomOnScroll:T,zoomOnPinch:V,panOnScroll:H,panOnScrollSpeed:L,panOnScrollMode:X,zoomOnDoubleClick:Y,panOnDrag:F,onPaneClick:W,onPaneMouseEnter:Z,onPaneMouseMove:K,onPaneMouseLeave:j,onPaneScroll:q,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,elevateEdgesOnSelect:le,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})=>{const fe=Ua(e,Sa),ge=Ua(n,Oa);return function(e){const n=Bi(),o=t.useRef(!1);t.useEffect((()=>{!o.current&&n.viewportInitialized&&e&&(setTimeout((()=>e(n)),1),o.current=!0)}),[e,n.viewportInitialized])}(a),t.createElement(wa,{onPaneClick:W,onPaneMouseEnter:Z,onPaneMouseMove:K,onPaneMouseLeave:j,onPaneContextMenu:U,onPaneScroll:q,deleteKeyCode:k,selectionKeyCode:x,selectionOnDrag:E,selectionMode:C,onSelectionStart:m,onSelectionEnd:y,multiSelectionKeyCode:_,panActivationKeyCode:N,zoomActivationKeyCode:M,elementsSelectable:A,onMove:o,onMoveStart:r,onMoveEnd:i,zoomOnScroll:T,zoomOnPinch:V,zoomOnDoubleClick:Y,panOnScroll:H,panOnScrollSpeed:L,panOnScrollMode:X,panOnDrag:F,defaultViewport:R,translateExtent:I,minZoom:z,maxZoom:D,onSelectionContextMenu:p,preventScrolling:B,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,disableKeyboardA11y:ce},t.createElement(Wa,null,t.createElement(Ya,{edgeTypes:ge,onEdgeClick:l,onEdgeDoubleClick:u,onlyRenderVisibleElements:P,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,defaultMarkerColor:$,noPanClassName:se,elevateEdgesOnSelect:!!le,disableKeyboardA11y:ce,rfId:he},t.createElement(qa,{style:b,type:v,component:w,containerStyle:S})),t.createElement("div",{className:"react-flow__edgelabel-renderer"}),t.createElement(Ca,{nodeTypes:fe,onNodeClick:s,onNodeDoubleClick:c,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,selectNodesOnDrag:O,onlyRenderVisibleElements:P,noPanClassName:se,noDragClassName:ie,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})))};Ga.displayName="GraphView";var Qa=t.memo(Ga);const Ja=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],es={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:Ja,nodeExtent:Ja,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:e.ConnectionMode.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:(e,t)=>{},isValidConnection:void 0},ts=()=>{return e=(e,t)=>({...es,setNodes:n=>{const{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:i}=t();e({nodeInternals:ki(n,o,r,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:o={}}=t();e({edges:n.map((e=>({...o,...e})))})},setDefaultNodesAndEdges:(n,o)=>{const r=void 0!==n,i=void 0!==o,a=r?ki(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:a,edges:i?o:[],hasDefaultNodes:r,hasDefaultEdges:i})},updateNodeDimensions:n=>{const{onNodesChange:o,nodeInternals:r,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:s,domNode:l,nodeOrigin:c}=t(),u=l?.querySelector(".react-flow__viewport");if(!u)return;const d=window.getComputedStyle(u),{m22:h}=new window.DOMMatrixReadOnly(d.transform),f=n.reduce(((e,t)=>{const n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[Ko]:{...n[Ko],handleBounds:void 0}});else if(n){const o=zo(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[Ko]:{...n[Ko],handleBounds:{source:sa(".source",t.nodeElement,h,c),target:sa(".target",t.nodeElement,h,c)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e}),[]);Mi(r,c);const g=a||i&&!a&&Pi(t,{initial:!0,...s});e({nodeInternals:new Map(r),fitViewOnInitDone:g}),f?.length>0&&o?.(f)},updateNodePositions:(e,n=!0,o=!1)=>{const{triggerNodeChanges:r}=t();r(e.map((e=>{const t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t})))},triggerNodeChanges:n=>{const{onNodesChange:o,nodeInternals:r,hasDefaultNodes:i,nodeOrigin:a,getNodes:s,elevateNodesOnSelect:l}=t();if(n?.length){if(i){const t=ki(qi(n,s()),r,a,l);e({nodeInternals:t})}o?.(n)}},addSelectedNodes:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Gi(e,!0))):(a=Qi(i(),n),s=Qi(r,[])),Ri({changedNodes:a,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Gi(e,!0))):(a=Qi(r,n),s=Qi(i(),[])),Ri({changedNodes:s,changedEdges:a,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{const{edges:r,getNodes:i}=t(),a=o||r;Ri({changedNodes:(n||i()).map((e=>(e.selected=!1,Gi(e.id,!1)))),changedEdges:a.map((e=>Gi(e.id,!1))),get:t,set:e})},setMinZoom:n=>{const{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:o}=t();Ri({changedNodes:o().filter((e=>e.selected)).map((e=>Gi(e.id,!1))),changedEdges:n.filter((e=>e.selected)).map((e=>Gi(e.id,!1))),get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:o}=t();o.forEach((e=>{e.positionAbsolute=Bo(e.position,n)})),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{const{transform:n,width:o,height:r,d3Zoom:i,d3Selection:a,translateExtent:s}=t();if(!i||!a||!e.x&&!e.y)return!1;const l=ao.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=[[0,0],[o,r]],u=i?.constrain()(l,c,s);return i.transform(a,u),n[0]!==u.x||n[1]!==u.y||n[2]!==u.k},cancelConnection:()=>e({connectionNodeId:es.connectionNodeId,connectionHandleId:es.connectionHandleId,connectionHandleType:es.connectionHandleType,connectionStatus:es.connectionStatus,connectionStartHandle:es.connectionStartHandle,connectionEndHandle:es.connectionEndHandle}),reset:()=>e({...es})}),t=Object.is,e?v(e,t):v;var e,t},ns=({children:e})=>{const n=t.useRef(null);return n.current||(n.current=ts()),t.createElement(vo,{value:n.current},e)};ns.displayName="ReactFlowProvider";const os=({children:e})=>t.useContext(yo)?t.createElement(t.Fragment,null,e):t.createElement(ns,null,e);os.displayName="ReactFlowWrapper";const rs={input:ni,default:ei,output:ri,group:ii},is={default:Er,straight:br,step:yr,smoothstep:mr,simplebezier:dr},as=[0,0],ss=[15,15],ls={x:0,y:0,zoom:1},cs={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},us=t.forwardRef((({nodes:n,edges:r,defaultNodes:i,defaultEdges:a,className:s,nodeTypes:l=rs,edgeTypes:c=is,onNodeClick:u,onEdgeClick:d,onInit:h,onMove:f,onMoveStart:g,onMoveEnd:p,onConnect:m,onConnectStart:y,onConnectEnd:v,onClickConnectStart:b,onClickConnectEnd:w,onNodeMouseEnter:S,onNodeMouseMove:x,onNodeMouseLeave:E,onNodeContextMenu:C,onNodeDoubleClick:_,onNodeDragStart:N,onNodeDrag:M,onNodeDragStop:k,onNodesDelete:P,onEdgesDelete:A,onSelectionChange:O,onSelectionDragStart:R,onSelectionDrag:I,onSelectionDragStop:z,onSelectionContextMenu:D,onSelectionStart:B,onSelectionEnd:$,connectionMode:T=e.ConnectionMode.Strict,connectionLineType:V=e.ConnectionLineType.Bezier,connectionLineStyle:H,connectionLineComponent:L,connectionLineContainerStyle:X,deleteKeyCode:Y="Backspace",selectionKeyCode:F="Shift",selectionOnDrag:W=!1,selectionMode:Z=e.SelectionMode.Full,panActivationKeyCode:K="Space",multiSelectionKeyCode:j=(Qo()?"Meta":"Control"),zoomActivationKeyCode:q=(Qo()?"Meta":"Control"),snapToGrid:U=!1,snapGrid:G=ss,onlyRenderVisibleElements:Q=!1,selectNodesOnDrag:J=!0,nodesDraggable:ee,nodesConnectable:te,nodesFocusable:ne,nodeOrigin:oe=as,edgesFocusable:re,edgesUpdatable:ie,elementsSelectable:ae,defaultViewport:se=ls,minZoom:le=.5,maxZoom:ce=2,translateExtent:ue=Ja,preventScrolling:de=!0,nodeExtent:he,defaultMarkerColor:fe="#b1b1b7",zoomOnScroll:ge=!0,zoomOnPinch:pe=!0,panOnScroll:me=!1,panOnScrollSpeed:ye=.5,panOnScrollMode:ve=e.PanOnScrollMode.Free,zoomOnDoubleClick:be=!0,panOnDrag:we=!0,onPaneClick:Se,onPaneMouseEnter:xe,onPaneMouseMove:Ee,onPaneMouseLeave:Ce,onPaneScroll:_e,onPaneContextMenu:Ne,children:Me,onEdgeContextMenu:ke,onEdgeDoubleClick:Pe,onEdgeMouseEnter:Ae,onEdgeMouseMove:Oe,onEdgeMouseLeave:Re,onEdgeUpdate:Ie,onEdgeUpdateStart:ze,onEdgeUpdateEnd:De,onReconnect:Be,onReconnectStart:$e,onReconnectEnd:Te,reconnectRadius:Ve=10,edgeUpdaterRadius:He=10,onNodesChange:Le,onEdgesChange:Xe,noDragClassName:Ye="nodrag",noWheelClassName:Fe="nowheel",noPanClassName:We="nopan",fitView:Ze=!1,fitViewOptions:Ke,connectOnClick:je=!0,attributionPosition:qe,proOptions:Ue,defaultEdgeOptions:Ge,elevateNodesOnSelect:Qe=!0,elevateEdgesOnSelect:Je=!1,disableKeyboardA11y:et=!1,autoPanOnConnect:tt=!0,autoPanOnNodeDrag:nt=!0,connectionRadius:ot=20,isValidConnection:rt,onError:it,style:at,id:st,nodeDragThreshold:lt,...ct},ut)=>{const dt=st||"1";return t.createElement("div",{...ct,style:{...at,...cs},ref:ut,className:o(["react-flow",s]),"data-testid":"rf__wrapper",id:st},t.createElement(os,null,t.createElement(Qa,{onInit:h,onMove:f,onMoveStart:g,onMoveEnd:p,onNodeClick:u,onEdgeClick:d,onNodeMouseEnter:S,onNodeMouseMove:x,onNodeMouseLeave:E,onNodeContextMenu:C,onNodeDoubleClick:_,nodeTypes:l,edgeTypes:c,connectionLineType:V,connectionLineStyle:H,connectionLineComponent:L,connectionLineContainerStyle:X,selectionKeyCode:F,selectionOnDrag:W,selectionMode:Z,deleteKeyCode:Y,multiSelectionKeyCode:j,panActivationKeyCode:K,zoomActivationKeyCode:q,onlyRenderVisibleElements:Q,selectNodesOnDrag:J,defaultViewport:se,translateExtent:ue,minZoom:le,maxZoom:ce,preventScrolling:de,zoomOnScroll:ge,zoomOnPinch:pe,zoomOnDoubleClick:be,panOnScroll:me,panOnScrollSpeed:ye,panOnScrollMode:ve,panOnDrag:we,onPaneClick:Se,onPaneMouseEnter:xe,onPaneMouseMove:Ee,onPaneMouseLeave:Ce,onPaneScroll:_e,onPaneContextMenu:Ne,onSelectionContextMenu:D,onSelectionStart:B,onSelectionEnd:$,onEdgeContextMenu:ke,onEdgeDoubleClick:Pe,onEdgeMouseEnter:Ae,onEdgeMouseMove:Oe,onEdgeMouseLeave:Re,onReconnect:Be??Ie,onReconnectStart:$e??ze,onReconnectEnd:Te??De,reconnectRadius:Ve??He,defaultMarkerColor:fe,noDragClassName:Ye,noWheelClassName:Fe,noPanClassName:We,elevateEdgesOnSelect:Je,rfId:dt,disableKeyboardA11y:et,nodeOrigin:oe,nodeExtent:he}),t.createElement(pi,{nodes:n,edges:r,defaultNodes:i,defaultEdges:a,onConnect:m,onConnectStart:y,onConnectEnd:v,onClickConnectStart:b,onClickConnectEnd:w,nodesDraggable:ee,nodesConnectable:te,nodesFocusable:ne,edgesFocusable:re,edgesUpdatable:ie,elementsSelectable:ae,elevateNodesOnSelect:Qe,minZoom:le,maxZoom:ce,nodeExtent:he,onNodesChange:Le,onEdgesChange:Xe,snapToGrid:U,snapGrid:G,connectionMode:T,translateExtent:ue,connectOnClick:je,defaultEdgeOptions:Ge,fitView:Ze,fitViewOptions:Ke,onNodesDelete:P,onEdgesDelete:A,onNodeDragStart:N,onNodeDrag:M,onNodeDragStop:k,onSelectionDrag:I,onSelectionDragStart:R,onSelectionDragStop:z,noPanClassName:We,nodeOrigin:oe,rfId:dt,autoPanOnConnect:tt,autoPanOnNodeDrag:nt,onError:it,connectionRadius:ot,isValidConnection:rt,nodeDragThreshold:lt}),t.createElement(di,{onSelectionChange:O}),Me,t.createElement(Ro,{proOptions:Ue,position:qe}),t.createElement(xi,{rfId:dt,disableKeyboardA11y:et})))}));us.displayName="ReactFlow";const ds=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");const hs=e=>e.getNodes();const fs=e=>e.edges;const gs=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2]});function ps(e){return n=>{const[o,r]=t.useState(n),i=t.useCallback((t=>r((n=>e(t,n)))),[]);return[o,r,i]}}const ms=ps(qi),ys=ps(Ui);const vs={includeHiddenNodes:!1};const bs=({id:e,x:n,y:r,width:i,height:a,style:s,color:l,strokeColor:c,strokeWidth:u,className:d,borderRadius:h,shapeRendering:f,onClick:g,selected:p})=>{const{background:m,backgroundColor:y}=s||{},v=l||m||y;return t.createElement("rect",{className:o(["react-flow__minimap-node",{selected:p},d]),x:n,y:r,rx:h,ry:h,width:i,height:a,fill:v,stroke:c,strokeWidth:u,shapeRendering:f,onClick:g?t=>g(t,e):void 0})};bs.displayName="MiniMapNode";var ws=t.memo(bs);const Ss=e=>e.nodeOrigin,xs=e=>e.getNodes().filter((e=>!e.hidden&&e.width&&e.height)),Es=e=>e instanceof Function?e:()=>e;var Cs=t.memo((function({nodeStrokeColor:e="transparent",nodeColor:n="#e2e2e2",nodeClassName:o="",nodeBorderRadius:r=5,nodeStrokeWidth:i=2,nodeComponent:a=ws,onClick:s}){const l=ko(xs,b),c=ko(Ss),u=Es(n),d=Es(e),h=Es(o),f="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return t.createElement(t.Fragment,null,l.map((e=>{const{x:n,y:o}=Dr(e,c).positionAbsolute;return t.createElement(a,{key:e.id,x:n,y:o,width:e.width,height:e.height,style:e.style,selected:e.selected,className:h(e),color:u(e),borderRadius:r,strokeColor:d(e),strokeWidth:i,shapeRendering:f,onClick:s,id:e.id})})))}));const _s=e=>{const t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?Fo(Br(t,e.nodeOrigin),n):n,rfId:e.rfId}};function Ns({style:e,className:n,nodeStrokeColor:r="transparent",nodeColor:i="#e2e2e2",nodeClassName:a="",nodeBorderRadius:s=5,nodeStrokeWidth:l=2,nodeComponent:c,maskColor:u="rgb(240, 240, 240, 0.6)",maskStrokeColor:d="none",maskStrokeWidth:h=1,position:f="bottom-right",onClick:g,onNodeClick:p,pannable:m=!1,zoomable:y=!1,ariaLabel:v="React Flow mini map",inversePan:w=!1,zoomStep:S=10,offsetScale:x=5}){const E=Po(),C=t.useRef(null),{boundingRect:_,viewBB:N,rfId:M}=ko(_s,b),k=e?.width??200,P=e?.height??150,A=_.width/k,O=_.height/P,R=Math.max(A,O),I=R*k,z=R*P,D=x*R,B=_.x-(I-_.width)/2-D,$=_.y-(z-_.height)/2-D,T=I+2*D,V=z+2*D,H=`react-flow__minimap-desc-${M}`,L=t.useRef(0);L.current=R,t.useEffect((()=>{if(C.current){const e=He(C.current),t=e=>{const{transform:t,d3Selection:n,d3Zoom:o}=E.getState();if("wheel"!==e.sourceEvent.type||!n||!o)return;const r=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*S,i=t[2]*Math.pow(2,r);o.scaleTo(n,i)},n=e=>{const{transform:t,d3Selection:n,d3Zoom:o,translateExtent:r,width:i,height:a}=E.getState();if("mousemove"!==e.sourceEvent.type||!n||!o)return;const s=L.current*Math.max(1,t[2])*(w?-1:1),l={x:t[0]-e.sourceEvent.movementX*s,y:t[1]-e.sourceEvent.movementY*s},c=[[0,0],[i,a]],u=ao.translate(l.x,l.y).scale(t[2]),d=o.constrain()(u,c,r);o.transform(n,d)},o=mo().on("zoom",m?n:null).on("zoom.wheel",y?t:null);return e.call(o),()=>{e.on("zoom",null)}}}),[m,y,w,S]);const X=g?e=>{const t=Le(e);g(e,{x:t[0],y:t[1]})}:void 0,Y=p?(e,t)=>{const n=E.getState().nodeInternals.get(t);p(e,n)}:void 0;return t.createElement(Oo,{position:f,style:e,className:o(["react-flow__minimap",n]),"data-testid":"rf__minimap"},t.createElement("svg",{width:k,height:P,viewBox:`${B} ${$} ${T} ${V}`,role:"img","aria-labelledby":H,ref:C,onClick:X},v&&t.createElement("title",{id:H},v),t.createElement(Cs,{onClick:Y,nodeColor:i,nodeStrokeColor:r,nodeBorderRadius:s,nodeClassName:a,nodeStrokeWidth:l,nodeComponent:c}),t.createElement("path",{className:"react-flow__minimap-mask",d:`M${B-D},${$-D}h${T+2*D}v${V+2*D}h${-T-2*D}z\n        M${N.x},${N.y}h${N.width}v${N.height}h${-N.width}z`,fill:u,fillRule:"evenodd",stroke:d,strokeWidth:h,pointerEvents:"none"})))}Ns.displayName="MiniMap";var Ms=t.memo(Ns);function ks(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}function Ps(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},t.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function As(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},t.createElement("path",{d:"M0 0h32v4.2H0z"}))}function Os(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},t.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function Rs(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},t.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function Is(){return t.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},t.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}const zs=({children:e,className:n,...r})=>t.createElement("button",{type:"button",className:o(["react-flow__controls-button",n]),...r},e);zs.displayName="ControlButton";const Ds=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),Bs=({style:e,showZoom:n=!0,showFitView:r=!0,showInteractive:i=!0,fitViewOptions:a,onZoomIn:s,onZoomOut:l,onFitView:c,onInteractiveChange:u,className:d,children:h,position:f="bottom-left"})=>{const g=Po(),[p,m]=t.useState(!1),{isInteractive:y,minZoomReached:v,maxZoomReached:b}=ko(Ds,ks),{zoomIn:w,zoomOut:S,fitView:x}=Bi();if(t.useEffect((()=>{m(!0)}),[]),!p)return null;return t.createElement(Oo,{className:o(["react-flow__controls",d]),position:f,style:e,"data-testid":"rf__controls"},n&&t.createElement(t.Fragment,null,t.createElement(zs,{onClick:()=>{w(),s?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:b},t.createElement(Ps,null)),t.createElement(zs,{onClick:()=>{S(),l?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:v},t.createElement(As,null))),r&&t.createElement(zs,{className:"react-flow__controls-fitview",onClick:()=>{x(a),c?.()},title:"fit view","aria-label":"fit view"},t.createElement(Os,null)),i&&t.createElement(zs,{className:"react-flow__controls-interactive",onClick:()=>{g.setState({nodesDraggable:!y,nodesConnectable:!y,elementsSelectable:!y}),u?.(!y)},title:"toggle interactivity","aria-label":"toggle interactivity"},y?t.createElement(Is,null):t.createElement(Rs,null)),h)};Bs.displayName="Controls";var $s,Ts=t.memo(Bs);function Vs({color:e,dimensions:n,lineWidth:o}){return t.createElement("path",{stroke:e,strokeWidth:o,d:`M${n[0]/2} 0 V${n[1]} M0 ${n[1]/2} H${n[0]}`})}function Hs({color:e,radius:n}){return t.createElement("circle",{cx:n,cy:n,r:n,fill:e})}e.BackgroundVariant=void 0,($s=e.BackgroundVariant||(e.BackgroundVariant={})).Lines="lines",$s.Dots="dots",$s.Cross="cross";const Ls={[e.BackgroundVariant.Dots]:"#91919a",[e.BackgroundVariant.Lines]:"#eee",[e.BackgroundVariant.Cross]:"#e2e2e2"},Xs={[e.BackgroundVariant.Dots]:1,[e.BackgroundVariant.Lines]:1,[e.BackgroundVariant.Cross]:6},Ys=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function Fs({id:n,variant:r=e.BackgroundVariant.Dots,gap:i=20,size:a,lineWidth:s=1,offset:l=2,color:c,style:u,className:d}){const h=t.useRef(null),{transform:f,patternId:g}=ko(Ys,b),p=c||Ls[r],m=a||Xs[r],y=r===e.BackgroundVariant.Dots,v=r===e.BackgroundVariant.Cross,w=Array.isArray(i)?i:[i,i],S=[w[0]*f[2]||1,w[1]*f[2]||1],x=m*f[2],E=v?[x,x]:S,C=y?[x/l,x/l]:[E[0]/l,E[1]/l];return t.createElement("svg",{className:o(["react-flow__background",d]),style:{...u,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:h,"data-testid":"rf__background"},t.createElement("pattern",{id:g+n,x:f[0]%S[0],y:f[1]%S[1],width:S[0],height:S[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${C[0]},-${C[1]})`},y?t.createElement(Hs,{color:p,radius:x/l}):t.createElement(Vs,{dimensions:E,color:p,lineWidth:s})),t.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${g+n})`}))}Fs.displayName="Background";var Ws=t.memo(Fs);const Zs=e=>e.domNode?.querySelector(".react-flow__renderer");function Ks({children:e}){const t=ko(Zs);return t?n.createPortal(e,t):null}const js=(e,t)=>e.length===t.length&&e.every(((e,n)=>((e,t)=>e?.positionAbsolute?.x===t?.positionAbsolute?.x&&e?.positionAbsolute?.y===t?.positionAbsolute?.y&&e?.width===t?.width&&e?.height===t?.height&&e?.selected===t?.selected&&e?.[Ko]?.z===t?.[Ko]?.z)(e,t[n]))),qs=e=>({transform:e.transform,nodeOrigin:e.nodeOrigin,selectedNodesCount:e.getNodes().filter((e=>e.selected)).length});function Us(t,n,o,r,i){let a=.5;"start"===i?a=0:"end"===i&&(a=1);let s=[(t.x+t.width*a)*n[2]+n[0],t.y*n[2]+n[1]-r],l=[-100*a,-100];switch(o){case e.Position.Right:s=[(t.x+t.width)*n[2]+n[0]+r,(t.y+t.height*a)*n[2]+n[1]],l=[0,-100*a];break;case e.Position.Bottom:s[1]=(t.y+t.height)*n[2]+n[1]+r,l[1]=0;break;case e.Position.Left:s=[t.x*n[2]+n[0]-r,(t.y+t.height*a)*n[2]+n[1]],l=[-100,-100*a]}return`translate(${s[0]}px, ${s[1]}px) translate(${l[0]}%, ${l[1]}%)`}var Gs;e.ResizeControlVariant=void 0,(Gs=e.ResizeControlVariant||(e.ResizeControlVariant={})).Line="line",Gs.Handle="handle";const Qs={width:0,height:0,x:0,y:0},Js={...Qs,pointerX:0,pointerY:0,aspectRatio:1};var el=t.memo((function({nodeId:n,position:r,variant:i=e.ResizeControlVariant.Handle,className:a,style:s={},children:l,color:c,minWidth:u=10,minHeight:d=10,maxWidth:h=Number.MAX_VALUE,maxHeight:f=Number.MAX_VALUE,keepAspectRatio:g=!1,shouldResize:p,onResizeStart:m,onResize:y,onResizeEnd:v}){const b=Nr(),w="string"==typeof n?n:b,S=Po(),x=t.useRef(null),E=t.useRef(Js),C=t.useRef(Qs),_=ua(),N=i===e.ResizeControlVariant.Line?"right":"bottom-right",M=r??N;t.useEffect((()=>{if(!x.current||!w)return;const e=He(x.current),t=M.includes("right")||M.includes("left"),n=M.includes("bottom")||M.includes("top"),o=M.includes("left"),r=M.includes("top"),i=et().on("start",(e=>{const t=S.getState().nodeInternals.get(w),{xSnapped:n,ySnapped:o}=_(e);C.current={width:t?.width??0,height:t?.height??0,x:t?.position.x??0,y:t?.position.y??0},E.current={...C.current,pointerX:n,pointerY:o,aspectRatio:C.current.width/C.current.height},m?.(e,{...C.current})})).on("drag",(e=>{const{nodeInternals:i,triggerNodeChanges:a}=S.getState(),{xSnapped:s,ySnapped:l}=_(e),c=i.get(w);if(c){const i=[],{pointerX:m,pointerY:v,width:b,height:S,x:x,y:_,aspectRatio:N}=E.current,{x:M,y:k,width:P,height:A}=C.current,O=Math.floor(t?s-m:0),R=Math.floor(n?l-v:0);let I=Do(b+(o?-O:O),u,h),z=Do(S+(r?-R:R),d,f);if(g){const e=I/z,o=t&&n;I=e<=N&&o||n&&!t?z*N:I,z=e>N&&o||t&&!n?I/N:z,I>=h?(I=h,z=h/N):I<=u&&(I=u,z=u/N),z>=f?(z=f,I=f*N):z<=d&&(z=d,I=d*N)}const D=I!==P,B=z!==A;if(o||r){const e=o?x-(I-b):x,t=r?_-(z-S):_,n=e!==M&&D,a=t!==k&&B;if(n||a){const o={id:c.id,type:"position",position:{x:n?e:M,y:a?t:k}};i.push(o),C.current.x=o.position.x,C.current.y=o.position.y}}if(D||B){const e={id:w,type:"dimensions",updateStyle:!0,resizing:!0,dimensions:{width:I,height:z}};i.push(e),C.current.width=I,C.current.height=z}if(0===i.length)return;const $=function({width:e,prevWidth:t,height:n,prevHeight:o,invertX:r,invertY:i}){const a=e-t,s=n-o,l=[a>0?1:a<0?-1:0,s>0?1:s<0?-1:0];return a&&r&&(l[0]=-1*l[0]),s&&i&&(l[1]=-1*l[1]),l}({width:C.current.width,prevWidth:P,height:C.current.height,prevHeight:A,invertX:o,invertY:r}),T={...C.current,direction:$},V=p?.(e,T);if(!1===V)return;y?.(e,T),a(i)}})).on("end",(e=>{const t={id:w,type:"dimensions",resizing:!1};v?.(e,{...C.current}),S.getState().triggerNodeChanges([t])}));return e.call(i),()=>{e.on(".drag",null)}}),[w,M,u,d,h,f,g,_,m,y,v]);const k=M.split("-"),P=i===e.ResizeControlVariant.Line?"borderColor":"backgroundColor",A=c?{...s,[P]:c}:s;return t.createElement("div",{className:o(["react-flow__resize-control","nodrag",...k,i,a]),ref:x,style:A},l)}));const tl=["top-left","top-right","bottom-left","bottom-right"],nl=["top","right","bottom","left"];e.Background=Ws,e.BaseEdge=Jo,e.BezierEdge=Er,e.ControlButton=zs,e.Controls=Ts,e.EdgeLabelRenderer=function({children:e}){const t=ko(ds);return t?n.createPortal(e,t):null},e.EdgeText=Io,e.Handle=Qr,e.MiniMap=Ms,e.NodeResizeControl=el,e.NodeResizer=function({nodeId:n,isVisible:o=!0,handleClassName:r,handleStyle:i,lineClassName:a,lineStyle:s,color:l,minWidth:c=10,minHeight:u=10,maxWidth:d=Number.MAX_VALUE,maxHeight:h=Number.MAX_VALUE,keepAspectRatio:f=!1,shouldResize:g,onResizeStart:p,onResize:m,onResizeEnd:y}){return o?t.createElement(t.Fragment,null,nl.map((o=>t.createElement(el,{key:o,className:a,style:s,nodeId:n,position:o,variant:e.ResizeControlVariant.Line,color:l,minWidth:c,minHeight:u,maxWidth:d,maxHeight:h,onResizeStart:p,keepAspectRatio:f,shouldResize:g,onResize:m,onResizeEnd:y}))),tl.map((e=>t.createElement(el,{key:e,className:r,style:i,nodeId:n,position:e,color:l,minWidth:c,minHeight:u,maxWidth:d,maxHeight:h,onResizeStart:p,keepAspectRatio:f,shouldResize:g,onResize:m,onResizeEnd:y})))):null},e.NodeToolbar=function({nodeId:n,children:r,className:i,style:a,isVisible:s,position:l=e.Position.Top,offset:c=10,align:u="center",...d}){const h=Nr(),f=t.useCallback((e=>(Array.isArray(n)?n:[n||h||""]).reduce(((t,n)=>{const o=e.nodeInternals.get(n);return o&&t.push(o),t}),[])),[n,h]),g=ko(f,js),{transform:p,nodeOrigin:m,selectedNodesCount:y}=ko(qs,b);if(!("boolean"==typeof s?s:1===g.length&&g[0].selected&&1===y)||!g.length)return null;const v=Br(g,m),w=Math.max(...g.map((e=>(e[Ko]?.z||1)+1))),S={position:"absolute",transform:Us(v,p,l,c,u),zIndex:w,...a};return t.createElement(Ks,null,t.createElement("div",{style:S,className:o(["react-flow__node-toolbar",i]),...d},r))},e.Panel=Oo,e.ReactFlow=us,e.ReactFlowProvider=ns,e.SimpleBezierEdge=dr,e.SmoothStepEdge=mr,e.StepEdge=yr,e.StraightEdge=br,e.addEdge=Or,e.applyEdgeChanges=Ui,e.applyNodeChanges=qi,e.boxToRect=Xo,e.clamp=Do,e.default=us,e.getBezierPath=xr,e.getBoundsOfRects=Fo,e.getConnectedEdges=Tr,e.getIncomers=(e,t,n)=>{if(!kr(e))return[];const o=n.filter((t=>t.target===e.id)).map((e=>e.source));return t.filter((e=>o.includes(e.id)))},e.getMarkerEnd=(e,t)=>void 0!==t&&t?`url(#${t})`:void 0!==e?`url(#react-flow__${e})`:"none",e.getNodePositionWithOrigin=Dr,e.getNodesBounds=Br,e.getOutgoers=(e,t,n)=>{if(!kr(e))return[];const o=n.filter((t=>t.source===e.id)).map((e=>e.target));return t.filter((e=>o.includes(e.id)))},e.getRectOfNodes=(e,t=[0,0])=>(console.warn("[DEPRECATED] `getRectOfNodes` is deprecated. Instead use `getNodesBounds` https://reactflow.dev/api-reference/utils/get-nodes-bounds."),Br(e,t)),e.getSimpleBezierPath=ur,e.getSmoothStepPath=pr,e.getStraightPath=vr,e.getTransformForBounds=(e,t,n,o,r,i=.1)=>{const{x:a,y:s,zoom:l}=Vr(e,t,n,o,r,i);return console.warn("[DEPRECATED] `getTransformForBounds` is deprecated. Instead use `getViewportForBounds`. Beware that the return value is type Viewport (`{ x: number, y: number, zoom: number }`) instead of Transform (`[number, number, number]`). https://reactflow.dev/api-reference/utils/get-viewport-for-bounds"),[a,s,l]},e.getViewportForBounds=Vr,e.handleParentExpand=Ki,e.internalsSymbol=Ko,e.isEdge=Mr,e.isNode=kr,e.reconnectEdge=Rr,e.rectToBox=Lo,e.updateEdge=(e,t,n,o={shouldReplaceId:!0})=>(console.warn("[DEPRECATED] `updateEdge` is deprecated. Instead use `reconnectEdge` https://reactflow.dev/api-reference/utils/reconnect-edge"),Rr(e,t,n,o)),e.useEdges=function(){return ko(fs,b)},e.useEdgesState=ys,e.useGetPointerPosition=ua,e.useKeyPress=Ei,e.useNodeId=Nr,e.useNodes=function(){return ko(hs,b)},e.useNodesInitialized=function(e=vs){return ko((e=>t=>0!==t.nodeInternals.size&&t.getNodes().filter((t=>!!e.includeHiddenNodes||!t.hidden)).every((e=>void 0!==e[Ko]?.handleBounds)))(e))},e.useNodesState=ms,e.useOnSelectionChange=function({onChange:e}){const n=Po();t.useEffect((()=>{const t=[...n.getState().onSelectionChange,e];return n.setState({onSelectionChange:t}),()=>{const t=n.getState().onSelectionChange.filter((t=>t!==e));n.setState({onSelectionChange:t})}}),[e])},e.useOnViewportChange=function({onStart:e,onChange:n,onEnd:o}){const r=Po();t.useEffect((()=>{r.setState({onViewportChangeStart:e})}),[e]),t.useEffect((()=>{r.setState({onViewportChange:n})}),[n]),t.useEffect((()=>{r.setState({onViewportChangeEnd:o})}),[o])},e.useReactFlow=Bi,e.useStore=ko,e.useStoreApi=Po,e.useUpdateNodeInternals=function(){const e=Po();return t.useCallback((t=>{const{domNode:n,updateNodeDimensions:o}=e.getState(),r=(Array.isArray(t)?t:[t]).reduce(((e,t)=>{const o=n?.querySelector(`.react-flow__node[data-id="${t}"]`);return o&&e.push({id:t,nodeElement:o,forceUpdate:!0}),e}),[]);requestAnimationFrame((()=>o(r)))}),[])},e.useViewport=function(){return ko(gs,b)},Object.defineProperty(e,"__esModule",{value:!0})}));
