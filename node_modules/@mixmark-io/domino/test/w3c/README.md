# Document Object Model (DOM) Conformance Test Suites

http://www.w3.org/DOM/Test/

Since domino doesn't implement deprecated DOM features some tests are no longer relevant. These tests have been moved to the `obsolete` directory so that they are excluded from the suite.

## Attributes vs. Nodes

The majority of the excluded level1/core tests expect Attributes to inherit from the Node interface which is no longer required in DOM level 4. Also `Element.attributes` no longer returns a NamedNodeMap but a read-only array.

## Obsolete Attributes

Another big hunk of excluded tests checks the support of reflected attributes that have become obsolete in HTML 5.
